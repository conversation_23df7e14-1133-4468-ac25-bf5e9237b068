{"cells": [{"cell_type": "markdown", "id": "256aa5f1", "metadata": {}, "source": ["## PDF Processing Pipeline with PyMuPDF4LLM and OCRmyPDF\n", "\n", "This script implements a comprehensive PDF processing pipeline that:\n", "1. Detects PDF type (scanned vs born-digital)\n", "2. Applies OCRmyPDF preprocessing for scanned PDFs\n", "3. Uses PyMuPDF4LLM for text extraction with TOC-based header logic\n", "4. Falls back to heuristics for PDFs without TOC\n", "5. Returns structured metadata and extracted content\n"]}, {"cell_type": "code", "execution_count": 5, "id": "a0b31bb6", "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "import subprocess\n", "import tempfile\n", "import time\n", "from pathlib import Path\n", "from typing import Dict, List, Optional, Tuple, Any\n", "import json\n", "import logging\n", "\n", "# Core PDF processing libraries\n", "import fitz  # PyMuPDF\n", "import pymupdf4llm\n", "import ocrmypdf\n", "\n", "# Setup logging\n", "logging.basicConfig(level=logging.INFO)\n", "logger = logging.getLogger(__name__)\n"]}, {"cell_type": "code", "execution_count": 2, "id": "1608f32d", "metadata": {}, "outputs": [], "source": ["def is_pdf_scanned(pdf_path: str, text_threshold: float = 0.1) -> bool:\n", "    \"\"\"\n", "    Detect if a PDF is scanned (image-based) or born-digital.\n", "    \n", "    Args:\n", "        pdf_path: Path to the PDF file\n", "        text_threshold: Minimum ratio of text content to consider as born-digital\n", "        \n", "    Returns:\n", "        True if PDF is scanned, False if born-digital\n", "    \"\"\"\n", "    try:\n", "        doc = fitz.open(pdf_path)\n", "        total_chars = 0\n", "        total_area = 0\n", "        \n", "        # Sample first 5 pages to determine PDF type\n", "        sample_pages = min(5, len(doc))\n", "        \n", "        for page_num in range(sample_pages):\n", "            page = doc[page_num]\n", "            text = page.get_text()\n", "            total_chars += len(text.strip())\n", "            total_area += page.rect.width * page.rect.height\n", "        \n", "        doc.close()\n", "        \n", "        # Calculate text density\n", "        if total_area == 0:\n", "            return True\n", "            \n", "        text_density = total_chars / total_area\n", "        is_scanned = text_density < text_threshold\n", "        \n", "        logger.info(f\"PDF analysis: {total_chars} chars, density: {text_density:.6f}, scanned: {is_scanned}\")\n", "        return is_scanned\n", "        \n", "    except Exception as e:\n", "        logger.error(f\"Error analyzing PDF: {e}\")\n", "        return True  # Assume scanned if analysis fails\n"]}, {"cell_type": "code", "execution_count": 6, "id": "456b6db5", "metadata": {}, "outputs": [], "source": ["def has_table_of_contents(pdf_path: str) -> bool:\n", "    \"\"\"\n", "    Check if PDF has an embedded table of contents.\n", "    \n", "    Args:\n", "        pdf_path: Path to the PDF file\n", "        \n", "    Returns:\n", "        True if TOC exists, False otherwise\n", "    \"\"\"\n", "    try:\n", "        doc = fitz.open(pdf_path)\n", "        toc = doc.get_toc()\n", "        doc.close()\n", "        return len(toc) > 0\n", "    except Exception as e:\n", "        logger.error(f\"Error checking TOC: {e}\")\n", "        return False"]}, {"cell_type": "code", "execution_count": 7, "id": "99edc6e7", "metadata": {}, "outputs": [], "source": ["def preprocess_with_ocrmypdf(input_path: str, output_path: str = None) -> str:\n", "    \"\"\"\n", "    Preprocess PDF with OCRmyPDF to add OCR layer and fix orientation.\n", "    \n", "    Args:\n", "        input_path: Path to input PDF\n", "        output_path: Path for output PDF (optional)\n", "        \n", "    Returns:\n", "        Path to processed PDF\n", "    \"\"\"\n", "    if output_path is None:\n", "        # Create temporary file for processed PDF\n", "        temp_dir = tempfile.gettempdir()\n", "        base_name = Path(input_path).stem\n", "        output_path = os.path.join(temp_dir, f\"{base_name}_ocr_processed.pdf\")\n", "    \n", "    try:\n", "        logger.info(f\"Starting OCRmyPDF preprocessing: {input_path}\")\n", "        start_time = time.time()\n", "        \n", "        # OCRmyPDF configuration for optimal processing\n", "        ocrmypdf.ocr(\n", "            input_path,\n", "            output_path,\n", "            language=['eng'],  # Can be configured for other languages\n", "            rotate_pages=True,  # Auto-rotate pages with horizontal text\n", "            deskew=True,       # Fix skewed text\n", "            clean=True,        # Clean up artifacts\n", "            optimize=1,        # Optimize output file size\n", "            pdf_renderer='hocr',  # Use hOCR for better text positioning\n", "            force_ocr=False,   # Only OCR pages that need it\n", "            skip_text=False,   # Don't skip existing text\n", "            redo_ocr=False,    # Don't redo existing OCR\n", "            progress_bar=False,\n", "            quiet=True\n", "        )\n", "        \n", "        processing_time = time.time() - start_time\n", "        logger.info(f\"OCRmyPDF completed in {processing_time:.2f} seconds\")\n", "        \n", "        return output_path\n", "        \n", "    except Exception as e:\n", "        logger.error(f\"OCRmyPDF preprocessing failed: {e}\")\n", "        # Return original path if OCR fails\n", "        return input_path"]}, {"cell_type": "markdown", "id": "cfaaea2f", "metadata": {}, "source": ["## PyMuPDF4LLM Header Detection Functions\n"]}, {"cell_type": "code", "execution_count": 8, "id": "28417dbb", "metadata": {}, "outputs": [], "source": ["def create_toc_header_logic(pdf_path: str) -> Dict[str, Any]:\n", "    \"\"\"\n", "    Create header extraction logic based on TOC information.\n", "    \n", "    Args:\n", "        pdf_path: Path to PDF file\n", "        \n", "    Returns:\n", "        Header info dictionary for PyMuPDF4LLM\n", "    \"\"\"\n", "    try:\n", "        doc = fitz.open(pdf_path)\n", "        toc = doc.get_toc()\n", "        \n", "        if not toc:\n", "            doc.close()\n", "            return None\n", "            \n", "        # Analyze TOC structure to create header logic\n", "        header_info = {}\n", "        level_fonts = {}\n", "        \n", "        # Extract font information for each TOC level\n", "        for level, title, page_num in toc:\n", "            if level not in level_fonts:\n", "                level_fonts[level] = []\n", "            \n", "            # Try to find the actual text block for this heading\n", "            if page_num <= len(doc):\n", "                page = doc[page_num - 1]  # PyMuPDF uses 0-based indexing\n", "                blocks = page.get_text(\"dict\")\n", "                \n", "                # Search for matching text in blocks\n", "                for block in blocks.get(\"blocks\", []):\n", "                    if \"lines\" in block:\n", "                        for line in block[\"lines\"]:\n", "                            for span in line.get(\"spans\", []):\n", "                                if title.lower() in span.get(\"text\", \"\").lower():\n", "                                    font_info = {\n", "                                        \"font\": span.get(\"font\", \"\"),\n", "                                        \"size\": span.get(\"size\", 0),\n", "                                        \"flags\": span.get(\"flags\", 0)\n", "                                    }\n", "                                    level_fonts[level].append(font_info)\n", "                                    break\n", "        \n", "        doc.close()\n", "        \n", "        # Create header detection logic\n", "        if level_fonts:\n", "            header_info = {\n", "                \"levels\": len(level_fonts),\n", "                \"font_mapping\": level_fonts,\n", "                \"use_toc\": True\n", "            }\n", "        \n", "        return header_info\n", "        \n", "    except Exception as e:\n", "        logger.error(f\"Error creating TOC header logic: {e}\")\n", "        return None\n"]}, {"cell_type": "code", "execution_count": 9, "id": "8110e593", "metadata": {}, "outputs": [], "source": ["def create_heuristic_header_logic(pdf_path: str) -> Dict[str, Any]:\n", "    \"\"\"\n", "    Create header detection logic based on font size and style heuristics.\n", "    \n", "    Args:\n", "        pdf_path: Path to PDF file\n", "        \n", "    Returns:\n", "        Header info dictionary for PyMuPDF4LLM\n", "    \"\"\"\n", "    try:\n", "        doc = fitz.open(pdf_path)\n", "        font_analysis = {}\n", "        \n", "        # Analyze first few pages to understand font patterns\n", "        sample_pages = min(3, len(doc))\n", "        \n", "        for page_num in range(sample_pages):\n", "            page = doc[page_num]\n", "            blocks = page.get_text(\"dict\")\n", "            \n", "            for block in blocks.get(\"blocks\", []):\n", "                if \"lines\" in block:\n", "                    for line in block[\"lines\"]:\n", "                        for span in line.get(\"spans\", []):\n", "                            font = span.get(\"font\", \"\")\n", "                            size = span.get(\"size\", 0)\n", "                            flags = span.get(\"flags\", 0)\n", "                            text = span.get(\"text\", \"\").strip()\n", "                            \n", "                            if text and size > 0:\n", "                                key = f\"{font}_{size}_{flags}\"\n", "                                if key not in font_analysis:\n", "                                    font_analysis[key] = {\n", "                                        \"font\": font,\n", "                                        \"size\": size,\n", "                                        \"flags\": flags,\n", "                                        \"is_bold\": bool(flags & 2**4),\n", "                                        \"is_italic\": bool(flags & 2**1),\n", "                                        \"count\": 0,\n", "                                        \"avg_length\": 0,\n", "                                        \"examples\": []\n", "                                    }\n", "                                \n", "                                font_analysis[key][\"count\"] += 1\n", "                                font_analysis[key][\"avg_length\"] += len(text)\n", "                                if len(font_analysis[key][\"examples\"]) < 3:\n", "                                    font_analysis[key][\"examples\"].append(text)\n", "        \n", "        doc.close()\n", "        \n", "        # Create header detection rules based on analysis\n", "        sorted_fonts = sorted(font_analysis.items(), \n", "                            key=lambda x: x[1][\"size\"], reverse=True)\n", "        \n", "        header_info = {\n", "            \"levels\": 3,  # Assume 3 header levels\n", "            \"font_rules\": [],\n", "            \"use_toc\": False\n", "        }\n", "        \n", "        # Define header levels based on font size and style\n", "        for i, (key, info) in enumerate(sorted_fonts[:3]):\n", "            if info[\"size\"] > 12 or info[\"is_bold\"]:  # Potential header\n", "                header_info[\"font_rules\"].append({\n", "                    \"level\": i + 1,\n", "                    \"font\": info[\"font\"],\n", "                    \"size_min\": info[\"size\"] - 1,\n", "                    \"size_max\": info[\"size\"] + 1,\n", "                    \"bold\": info[\"is_bold\"],\n", "                    \"italic\": info[\"is_italic\"]\n", "                })\n", "        \n", "        return header_info\n", "        \n", "    except Exception as e:\n", "        logger.error(f\"Error creating heuristic header logic: {e}\")\n", "        return None\n"]}, {"cell_type": "markdown", "id": "b139a064", "metadata": {}, "source": ["## Main Processing Pipeline"]}, {"cell_type": "code", "execution_count": 10, "id": "f1f4bf1c", "metadata": {}, "outputs": [], "source": ["def process_pdf_pipeline(input_path: str, output_dir: str = None) -> Dict[str, Any]:\n", "    \"\"\"\n", "    Main PDF processing pipeline that handles different PDF types and extracts structured content.\n", "    \n", "    Args:\n", "        input_path: Path to input PDF file\n", "        output_dir: Directory for output files (optional)\n", "        \n", "    Returns:\n", "        Dictionary containing processing results and metadata\n", "    \"\"\"\n", "    start_time = time.time()\n", "    \n", "    if not os.path.exists(input_path):\n", "        raise FileNotFoundError(f\"PDF file not found: {input_path}\")\n", "    \n", "    # Setup output directory\n", "    if output_dir is None:\n", "        output_dir = Path(input_path).parent / \"processed_output\"\n", "    \n", "    os.makedirs(output_dir, exist_ok=True)\n", "    \n", "    # Initialize results dictionary\n", "    results = {\n", "        \"input_path\": input_path,\n", "        \"output_dir\": output_dir,\n", "        \"processing_start\": start_time,\n", "        \"pdf_type\": None,\n", "        \"has_toc\": <PERSON><PERSON><PERSON>,\n", "        \"ocr_applied\": <PERSON><PERSON><PERSON>,\n", "        \"processed_pdf_path\": None,\n", "        \"extraction_method\": None,\n", "        \"header_info\": None,\n", "        \"extracted_content\": None,\n", "        \"metadata\": {},\n", "        \"processing_time\": None,\n", "        \"errors\": []\n", "    }\n", "    \n", "    try:\n", "        logger.info(f\"Starting PDF pipeline processing: {input_path}\")\n", "        \n", "        # Step 1: Analyze PDF type\n", "        logger.info(\"Step 1: Analyzing PDF type...\")\n", "        is_scanned = is_pdf_scanned(input_path)\n", "        has_toc = has_table_of_contents(input_path)\n", "        \n", "        results[\"pdf_type\"] = \"scanned\" if is_scanned else \"born_digital\"\n", "        results[\"has_toc\"] = has_toc\n", "        \n", "        # Step 2: Preprocess if needed\n", "        processed_pdf_path = input_path\n", "        \n", "        if is_scanned:\n", "            logger.info(\"Step 2: Applying OCRmyPDF preprocessing...\")\n", "            base_name = Path(input_path).stem\n", "            ocr_output_path = os.path.join(output_dir, f\"{base_name}_ocr_processed.pdf\")\n", "            processed_pdf_path = preprocess_with_ocrmypdf(input_path, ocr_output_path)\n", "            results[\"ocr_applied\"] = True\n", "            results[\"processed_pdf_path\"] = processed_pdf_path\n", "            \n", "            # Re-check TOC after OCR\n", "            has_toc = has_table_of_contents(processed_pdf_path)\n", "            results[\"has_toc\"] = has_toc\n", "        \n", "        # Step 3: Create header detection logic\n", "        logger.info(\"Step 3: Creating header detection logic...\")\n", "        header_info = None\n", "        \n", "        if has_toc:\n", "            logger.info(\"Using TOC-based header logic\")\n", "            header_info = create_toc_header_logic(processed_pdf_path)\n", "            results[\"extraction_method\"] = \"toc_based\"\n", "        else:\n", "            logger.info(\"Using heuristic header logic\")\n", "            header_info = create_heuristic_header_logic(processed_pdf_path)\n", "            results[\"extraction_method\"] = \"heuristic\"\n", "        \n", "        results[\"header_info\"] = header_info\n", "        \n", "        # Step 4: Extract content with PyMuPDF4LLM\n", "        logger.info(\"Step 4: Extracting content with PyMuPDF4LLM...\")\n", "        extraction_start = time.time()\n", "        \n", "        # Configure PyMuPDF4LLM parameters\n", "        pymupdf_params = {\n", "            \"page_chunks\": True,\n", "            \"write_images\": True,\n", "            \"image_path\": os.path.join(output_dir, \"images\"),\n", "            \"image_format\": \"png\",\n", "            \"extract_words\": True\n", "        }\n", "        \n", "        # Add header info if available\n", "        if header_info and header_info.get(\"use_toc\"):\n", "            # Use TOC-based extraction\n", "            extracted_content = pymupdf4llm.to_markdown(\n", "                processed_pdf_path,\n", "                **pymupdf_params\n", "            )\n", "        else:\n", "            # Use standard extraction\n", "            extracted_content = pymupdf4llm.to_markdown(\n", "                processed_pdf_path,\n", "                **pymupdf_params\n", "            )\n", "        \n", "        extraction_time = time.time() - extraction_start\n", "        results[\"extracted_content\"] = extracted_content\n", "        results[\"metadata\"][\"extraction_time\"] = extraction_time\n", "        \n", "        # Step 5: Save extracted content\n", "        logger.info(\"Step 5: Saving extracted content...\")\n", "        base_name = Path(input_path).stem\n", "        \n", "        # Save markdown content\n", "        markdown_path = os.path.join(output_dir, f\"{base_name}_extracted.md\")\n", "        with open(markdown_path, 'w', encoding='utf-8') as f:\n", "            if isinstance(extracted_content, list):\n", "                for chunk in extracted_content:\n", "                    f.write(str(chunk) + \"\\n\\n\")\n", "            else:\n", "                f.write(str(extracted_content))\n", "        \n", "        results[\"metadata\"][\"markdown_path\"] = markdown_path\n", "        \n", "        # Save processing metadata\n", "        metadata_path = os.path.join(output_dir, f\"{base_name}_metadata.json\")\n", "        with open(metadata_path, 'w', encoding='utf-8') as f:\n", "            # Create a serializable copy of results\n", "            serializable_results = {k: v for k, v in results.items() \n", "                                  if k not in ['extracted_content']}\n", "            json.dump(serializable_results, f, indent=2, default=str)\n", "        \n", "        results[\"metadata\"][\"metadata_path\"] = metadata_path\n", "        \n", "        # Calculate total processing time\n", "        total_time = time.time() - start_time\n", "        results[\"processing_time\"] = total_time\n", "        \n", "        logger.info(f\"Pipeline completed successfully in {total_time:.2f} seconds\")\n", "        \n", "        return results\n", "        \n", "    except Exception as e:\n", "        error_msg = f\"Pipeline processing failed: {str(e)}\"\n", "        logger.error(error_msg)\n", "        results[\"errors\"].append(error_msg)\n", "        results[\"processing_time\"] = time.time() - start_time\n", "        return results\n"]}, {"cell_type": "markdown", "id": "b6cc40b8", "metadata": {}, "source": ["## Batch Processing Function"]}, {"cell_type": "code", "execution_count": 11, "id": "0c3a32d2", "metadata": {}, "outputs": [], "source": ["def batch_process_pdfs(input_dir: str, output_dir: str = None) -> Dict[str, Any]:\n", "    \"\"\"\n", "    Process multiple PDFs in a directory.\n", "    \n", "    Args:\n", "        input_dir: Directory containing PDF files\n", "        output_dir: Directory for output files\n", "        \n", "    Returns:\n", "        Dictionary containing batch processing results\n", "    \"\"\"\n", "    if output_dir is None:\n", "        output_dir = os.path.join(input_dir, \"batch_processed\")\n", "    \n", "    os.makedirs(output_dir, exist_ok=True)\n", "    \n", "    pdf_files = list(Path(input_dir).glob(\"*.pdf\"))\n", "    \n", "    batch_results = {\n", "        \"input_dir\": input_dir,\n", "        \"output_dir\": output_dir,\n", "        \"total_files\": len(pdf_files),\n", "        \"processed_files\": 0,\n", "        \"failed_files\": 0,\n", "        \"results\": {},\n", "        \"summary\": {}\n", "    }\n", "    \n", "    logger.info(f\"Starting batch processing of {len(pdf_files)} PDF files\")\n", "    \n", "    for pdf_file in pdf_files:\n", "        try:\n", "            logger.info(f\"Processing: {pdf_file.name}\")\n", "            \n", "            # Create individual output directory for each PDF\n", "            pdf_output_dir = os.path.join(output_dir, pdf_file.stem)\n", "            \n", "            # Process the PDF\n", "            result = process_pdf_pipeline(str(pdf_file), pdf_output_dir)\n", "            \n", "            batch_results[\"results\"][pdf_file.name] = result\n", "            \n", "            if result.get(\"errors\"):\n", "                batch_results[\"failed_files\"] += 1\n", "            else:\n", "                batch_results[\"processed_files\"] += 1\n", "                \n", "        except Exception as e:\n", "            logger.error(f\"Failed to process {pdf_file.name}: {e}\")\n", "            batch_results[\"failed_files\"] += 1\n", "            batch_results[\"results\"][pdf_file.name] = {\n", "                \"error\": str(e),\n", "                \"processing_time\": None\n", "            }\n", "    \n", "    # Create summary\n", "    batch_results[\"summary\"] = {\n", "        \"success_rate\": batch_results[\"processed_files\"] / batch_results[\"total_files\"],\n", "        \"total_processing_time\": sum(\n", "            r.get(\"processing_time\", 0) for r in batch_results[\"results\"].values()\n", "            if r.get(\"processing_time\")\n", "        )\n", "    }\n", "    \n", "    # Save batch results\n", "    batch_summary_path = os.path.join(output_dir, \"batch_processing_summary.json\")\n", "    with open(batch_summary_path, 'w', encoding='utf-8') as f:\n", "        # Create serializable copy\n", "        serializable_batch = {k: v for k, v in batch_results.items() \n", "                             if k != 'results'}\n", "        serializable_batch['file_results'] = {\n", "            name: {k: v for k, v in result.items() if k != 'extracted_content'}\n", "            for name, result in batch_results['results'].items()\n", "        }\n", "        json.dump(serializable_batch, f, indent=2, default=str)\n", "    \n", "    logger.info(f\"Batch processing completed: {batch_results['processed_files']}/{batch_results['total_files']} successful\")\n", "    \n", "    return batch_results"]}, {"cell_type": "markdown", "id": "02887afe", "metadata": {}, "source": ["## Example Usage and Testing"]}, {"cell_type": "code", "execution_count": 13, "id": "5749bb0a", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:__main__:Starting PDF pipeline processing: pdfs/Allossogbe_et_al_2017_Mal_J.pdf\n", "INFO:__main__:Step 1: Analyzing PDF type...\n", "INFO:__main__:PDF analysis: 19485 chars, density: 0.008278, scanned: True\n", "INFO:__main__:Step 2: Applying OCRmyPDF preprocessing...\n", "INFO:__main__:Starting OCRmyPDF preprocessing: pdfs/Allossogbe_et_al_2017_Mal_J.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["=== Single PDF Processing Example ===\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:ocrmypdf.subprocess._windows:[WinError 2] The system cannot find the file specified\n", "ERROR:ocrmypdf.subprocess:\n", "The program 'unpaper' could not be executed or was not found on your\n", "system PATH.  This program is required when you use the\n", "--clean, --clean-final arguments.  You could try omitting these arguments, or install\n", "the package.\n", "\n", "INFO:ocrmypdf.subprocess:\n", "If not already installed, install the Chocolatey package manager. Then use\n", "a command prompt to install the missing package:\n", "    choco install unpaper\n", "\n", "ERROR:__main__:OCRmyPDF preprocessing failed: Could not find program 'unpaper' on the PATH\n", "INFO:__main__:Step 3: Creating header detection logic...\n", "INFO:__main__:Using TOC-based header logic\n", "INFO:__main__:Step 4: Extracting content with PyMuPDF4LLM...\n", "INFO:__main__:Step 5: Saving extracted content...\n", "INFO:__main__:Pipeline completed successfully in 18.14 seconds\n", "INFO:__main__:Starting batch processing of 3 PDF files\n", "INFO:__main__:Processing: 2014-Combining_organophosphate_treated_wall_linings_and_long-lasting_insecticidal_nets_for_improved_control_of_pyrethroi.pdf\n", "INFO:__main__:Starting PDF pipeline processing: pdfs\\2014-Combining_organophosphate_treated_wall_linings_and_long-lasting_insecticidal_nets_for_improved_control_of_pyrethroi.pdf\n", "INFO:__main__:Step 1: Analyzing PDF type...\n", "INFO:__main__:PDF analysis: 31353 chars, density: 0.012950, scanned: True\n", "INFO:__main__:Step 2: Applying OCRmyPDF preprocessing...\n", "INFO:__main__:Starting OCRmyPDF preprocessing: pdfs\\2014-Combining_organophosphate_treated_wall_linings_and_long-lasting_insecticidal_nets_for_improved_control_of_pyrethroi.pdf\n", "WARNING:ocrmypdf.subprocess._windows:[WinError 2] The system cannot find the file specified\n", "ERROR:ocrmypdf.subprocess:\n", "The program 'unpaper' could not be executed or was not found on your\n", "system PATH.  This program is required when you use the\n", "--clean, --clean-final arguments.  You could try omitting these arguments, or install\n", "the package.\n", "\n", "INFO:ocrmypdf.subprocess:\n", "If not already installed, install the Chocolatey package manager. Then use\n", "a command prompt to install the missing package:\n", "    choco install unpaper\n", "\n", "ERROR:__main__:OCRmyPDF preprocessing failed: Could not find program 'unpaper' on the PATH\n", "INFO:__main__:Step 3: Creating header detection logic...\n", "INFO:__main__:Using heuristic header logic\n", "INFO:__main__:Step 4: Extracting content with PyMuPDF4LLM...\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Processing completed!\n", "PDF Type: scanned\n", "Has TOC: True\n", "OCR Applied: True\n", "Extraction Method: toc_based\n", "Processing Time: 18.14 seconds\n", "\n", "=== Batch Processing Example ===\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:__main__:Step 5: Saving extracted content...\n", "INFO:__main__:Pipeline completed successfully in 15.16 seconds\n", "INFO:__main__:Processing: Allossogbe_et_al_2017_Mal_J.pdf\n", "INFO:__main__:Starting PDF pipeline processing: pdfs\\Allossogbe_et_al_2017_Mal_J.pdf\n", "INFO:__main__:Step 1: Analyzing PDF type...\n", "INFO:__main__:PDF analysis: 19485 chars, density: 0.008278, scanned: True\n", "INFO:__main__:Step 2: Applying OCRmyPDF preprocessing...\n", "INFO:__main__:Starting OCRmyPDF preprocessing: pdfs\\Allossogbe_et_al_2017_Mal_J.pdf\n", "WARNING:ocrmypdf.subprocess._windows:[WinError 2] The system cannot find the file specified\n", "ERROR:ocrmypdf.subprocess:\n", "The program 'unpaper' could not be executed or was not found on your\n", "system PATH.  This program is required when you use the\n", "--clean, --clean-final arguments.  You could try omitting these arguments, or install\n", "the package.\n", "\n", "INFO:ocrmypdf.subprocess:\n", "If not already installed, install the Chocolatey package manager. Then use\n", "a command prompt to install the missing package:\n", "    choco install unpaper\n", "\n", "ERROR:__main__:OCRmyPDF preprocessing failed: Could not find program 'unpaper' on the PATH\n", "INFO:__main__:Step 3: Creating header detection logic...\n", "INFO:__main__:Using TOC-based header logic\n", "INFO:__main__:Step 4: Extracting content with PyMuPDF4LLM...\n", "INFO:__main__:Step 5: Saving extracted content...\n", "INFO:__main__:Pipeline completed successfully in 19.38 seconds\n", "INFO:__main__:Processing: Somboon_et_al_1995_Trans_RSTMH.pdf\n", "INFO:__main__:Starting PDF pipeline processing: pdfs\\Somboon_et_al_1995_Trans_RSTMH.pdf\n", "INFO:__main__:Step 1: Analyzing PDF type...\n", "INFO:__main__:PDF analysis: 24369 chars, density: 0.010219, scanned: True\n", "INFO:__main__:Step 2: Applying OCRmyPDF preprocessing...\n", "INFO:__main__:Starting OCRmyPDF preprocessing: pdfs\\Somboon_et_al_1995_Trans_RSTMH.pdf\n", "WARNING:ocrmypdf.subprocess._windows:[WinError 2] The system cannot find the file specified\n", "ERROR:ocrmypdf.subprocess:\n", "The program 'unpaper' could not be executed or was not found on your\n", "system PATH.  This program is required when you use the\n", "--clean, --clean-final arguments.  You could try omitting these arguments, or install\n", "the package.\n", "\n", "INFO:ocrmypdf.subprocess:\n", "If not already installed, install the Chocolatey package manager. Then use\n", "a command prompt to install the missing package:\n", "    choco install unpaper\n", "\n", "ERROR:__main__:OCRmyPDF preprocessing failed: Could not find program 'unpaper' on the PATH\n", "INFO:__main__:Step 3: Creating header detection logic...\n", "INFO:__main__:Using heuristic header logic\n", "INFO:__main__:Step 4: Extracting content with PyMuPDF4LLM...\n", "INFO:__main__:Step 5: Saving extracted content...\n", "INFO:__main__:Pipeline completed successfully in 3.45 seconds\n", "INFO:__main__:Batch processing completed: 3/3 successful\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Batch processing completed!\n", "Total files: 3\n", "Processed: 3\n", "Failed: 0\n", "Success rate: 100.00%\n"]}], "source": ["def main():\n", "    \"\"\"\n", "    Example usage of the PDF processing pipeline.\n", "    \"\"\"\n", "    # Example 1: Process a single PDF\n", "    print(\"=== Single PDF Processing Example ===\")\n", "    \n", "    # Update this path to your actual PDF file\n", "    pdf_path = \"pdfs/Allossogbe_et_al_2017_Mal_J.pdf\"\n", "    \n", "    if os.path.exists(pdf_path):\n", "        try:\n", "            results = process_pdf_pipeline(pdf_path)\n", "            \n", "            print(f\"Processing completed!\")\n", "            print(f\"PDF Type: {results['pdf_type']}\")\n", "            print(f\"Has TOC: {results['has_toc']}\")\n", "            print(f\"OCR Applied: {results['ocr_applied']}\")\n", "            print(f\"Extraction Method: {results['extraction_method']}\")\n", "            print(f\"Processing Time: {results['processing_time']:.2f} seconds\")\n", "            \n", "            if results['errors']:\n", "                print(f\"Errors: {results['errors']}\")\n", "            \n", "        except Exception as e:\n", "            print(f\"Error processing PDF: {e}\")\n", "    else:\n", "        print(f\"PDF file not found: {pdf_path}\")\n", "    \n", "    # Example 2: Batch processing\n", "    print(\"\\n=== Batch Processing Example ===\")\n", "    \n", "    pdfs_dir = \"pdfs\"\n", "    if os.path.exists(pdfs_dir):\n", "        try:\n", "            batch_results = batch_process_pdfs(pdfs_dir)\n", "            \n", "            print(f\"Batch processing completed!\")\n", "            print(f\"Total files: {batch_results['total_files']}\")\n", "            print(f\"Processed: {batch_results['processed_files']}\")\n", "            print(f\"Failed: {batch_results['failed_files']}\")\n", "            print(f\"Success rate: {batch_results['summary']['success_rate']:.2%}\")\n", "            \n", "        except Exception as e:\n", "            print(f\"Error in batch processing: {e}\")\n", "    else:\n", "        print(f\"PDFs directory not found: {pdfs_dir}\")\n", "\n", "if __name__ == \"__main__\":\n", "    main()"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 5}