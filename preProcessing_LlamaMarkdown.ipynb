import os
import fitz    
import ocrmypdf
from pathlib import Path
import pymupdf4llm 
import logging
import re
import weaviate

# Set up logging for error tracking and debugging
logging.basicConfig(
    filename="extract_pipeline.log",
    level=logging.INFO,
    format="%(asctime)s | %(levelname)s | %(message)s"
)

# Create output directories if not exist
Path("temp_ocr").mkdir(exist_ok=True)
Path("output_chunk").mkdir(exist_ok=True)

LITSERVE_OCR_URL = "http://localhost:8001/ocr"  # we will use the prod url here
WEAVIATE_URL = "http://localhost:8080"  # same here

def preprocess_pdf(input_path, ocr_path, use_litserve=False, litserve_url=None):
    """
    Run OCRmyPDF (locally or via endpoint) only if the PDF is scanned.
    Returns the path to a PDF with selectable text.
    """
    try:
        doc = fitz.open(str(input_path))
        is_scanned = all(not page.get_text().strip() for page in doc)
        if is_scanned:
            logging.info(f"🧾 Detected scanned PDF → running OCR on {input_path}")
            # Optionally use LitServe for OCR endpoint
            if use_litserve and litserve_url:
                import requests
                with open(input_path, "rb") as f:
                    response = requests.post(litserve_url, files={"file": f})
                if response.status_code != 200:
                    raise RuntimeError("LitServe OCR failed")
                with open(ocr_path, "wb") as fout:
                    fout.write(response.content)
            else:
                ocrmypdf.ocr(
                    input_file=str(input_path),
                    output_file=str(ocr_path),
                    rotate_pages=True,
                    deskew=True,
                    force_ocr=True,
                    skip_text=True,
                    progress_bar=False
                )
            return ocr_path
        else:
            logging.info(f"📄 Born‑digital PDF → skipping OCR for {input_path}")
            return Path(input_path)
    except Exception as e:
        logging.error(f"Error during OCR preprocessing: {e}")
        raise

def remove_tables_from_markdown(md_text):
    """
    Remove markdown tables (pipe `|` blocks) from extracted markdown.
    """
    # Matches markdown tables: lines with pipes and then at least one header separator row
    pattern = r'(?:^.*?\|.*?\n)(?:^[ \t]*\|?[-:]+[\| -:]+[\n\r])(?:^.*?\|.*?\n?)+'
    cleaned = re.sub(pattern, '', md_text, flags=re.MULTILINE)
    return cleaned

def chunk_by_section_headers(md_text, min_length=20):
    """
    Chunk markdown by section headers, preserving header hierarchy for TOC-aware splits.
    Returns list of dicts: 'header', 'text', 'start_line', 'end_line'
    """
    lines = md_text.splitlines()
    chunks = []
    current_chunk = []
    current_header = None
    start_line = 0
    for i, line in enumerate(lines):
        header_match = re.match(r"^(#+)\s+(.+)", line)
        if header_match:
            if current_chunk:
                text = "\n".join(current_chunk).strip()
                if len(text) >= min_length:
                    chunks.append({
                        "header": current_header,
                        "text": text,
                        "start_line": start_line,
                        "end_line": i-1,
                    })
            current_chunk = [line]
            current_header = header_match.group(2)
            start_line = i
        else:
            current_chunk.append(line)
    if current_chunk:
        text = "\n".join(current_chunk).strip()
        if len(text) >= min_length:
            chunks.append({
                "header": current_header,
                "text": text,
                "start_line": start_line,
                "end_line": len(lines)-1,
            })
    return chunks

def extract_and_chunk(pdf_path, output_dir, chunk_mode='header', chunk_size=1024, chunk_overlap=128):
    """
    Extracts markdown (hierarchical, TOC-aware) from PDF, removes tables,
    chunks using either LlamaMarkdownReader (token-sized) or manual header splitting,
    and writes markdown chunks for inspection.
    Returns: list of LlamaIndexDoc-like dicts, each with .text and .metadata
    """
    try:
        logging.info(f"Extracting markdown from {pdf_path}")

        # Extract markdown with TOC/hierarchy
        # NOTE: Set extract_tables=False if your pymupdf4llm version supports it.
        try:
            md_text = pymupdf4llm.to_markdown(
                str(pdf_path),
                include_toc=True,
                write_images=False,
                # extract_tables=False
            )
        except TypeError:
            # For older versions w/o extract_tables param
            md_text = pymupdf4llm.to_markdown(
                str(pdf_path),
                include_toc=True,
                write_images=False
            )
        # Remove tables regardless (extra safety)
        md_text = remove_tables_from_markdown(md_text)
        # Optional: Write intermediate
        debug_md = Path(output_dir) / f"{Path(pdf_path).stem}_ALL.md"
        debug_md.write_text(md_text, encoding="utf-8")

        # Choose chunking mode:
        if chunk_mode == 'llama_token':
            # Use LlamaMarkdownReader's own size-based chunking with extra_info
            reader = pymupdf4llm.LlamaMarkdownReader(
                margins=(0, 50, 0, 30),
                max_levels=3,
                body_limit=11,
                chunk_size=chunk_size,
                chunk_overlap=chunk_overlap
            )
            docs = reader.load_data(pdf_path)
            chunks = [
                {
                    "text": remove_tables_from_markdown(doc.text),
                    "metadata": doc.extra_info
                } for doc in docs
            ]
        else:
            # Manual, semantically cleaner header-based chunking
            section_chunks = chunk_by_section_headers(md_text, min_length=30)
            chunks = []
            for i, chunk in enumerate(section_chunks):
                out_file = Path(output_dir) / f"{Path(pdf_path).stem}_section_{i}.md"
                out_file.write_text(chunk["text"], encoding="utf-8")
                # Metadata includes header, line range, source
                chunks.append({
                    "text": chunk["text"],
                    "metadata": {
                        "header": chunk["header"],
                        "source_pdf": str(pdf_path),
                        "chunk_index": i,
                        "start_line": chunk["start_line"],
                        "end_line": chunk["end_line"],
                    }
                })
        logging.info(f"Produced {len(chunks)} chunks from {pdf_path}")
        return chunks
    except Exception as e:
        logging.error(f"Extraction/chunking error: {e}")
        raise

def send_chunks_to_weaviate(chunks, weaviate_url=WEAVIATE_URL, class_name="SectionChunk"):
    """
    Send chunks to Weaviate vector DB (example; comment/uncomment as needed).
    """
    try:
        import weaviate
        client = weaviate.Client(weaviate_url)
        for chunk in chunks:
            # Set properties as per your class schema in Weaviate
            obj = {
                "text": chunk["text"],
                "header": chunk["metadata"].get("header", ""),
                "source_pdf": chunk["metadata"].get("source_pdf", ""),
                "chunk_index": chunk["metadata"].get("chunk_index", -1),
            }
            client.batch.add_data_object(obj, class_name)
        client.batch.flush()
        logging.info(f"Sent {len(chunks)} to Weaviate at {weaviate_url}")
    except Exception as e:
        logging.error(f"Weaviate upload error: {e}")
        print(f"Failed to upload to Weaviate: {e}")

def process_pdf_pipeline(
        pdf_path,
        chunk_mode='header',          # 'header' for semantic, 'llama_token' for token-based
        use_litserve=False,
        litserve_url=None,
        send_to_weaviate=False,
        weaviate_url=WEAVIATE_URL
        ):
    """
    Complete pipeline: PDF → OCR/preprocess → Extract markdown (no tables) → Chunk → (optional: Weaviate upload)
    """
    filename = Path(pdf_path).stem
    temp_ocr = Path("temp_ocr"); temp_ocr.mkdir(exist_ok=True)
    out_md   = Path("output_markdown"); out_md.mkdir(exist_ok=True)
    ocr_pdf = temp_ocr / f"{filename}_ocr.pdf"

    try:
        # Step 1: Preprocess (OCR if necessary)
        used_pdf = preprocess_pdf(pdf_path, ocr_pdf, use_litserve=use_litserve, litserve_url=litserve_url)

        # Step 2: Extract and chunk
        chunks = extract_and_chunk(used_pdf, out_md, chunk_mode=chunk_mode)

        print(f"Extracted {len(chunks)} chunks from {pdf_path}")
        logging.info(f"Chunks extracted from {pdf_path}")

        # Step 3: Send to Weaviate (if enabled)
        if send_to_weaviate and len(chunks) > 0:
            send_chunks_to_weaviate(chunks, weaviate_url=weaviate_url)
            print(f"Uploaded {len(chunks)} chunks to Weaviate at {weaviate_url}")

        return chunks

    except Exception as e:
        logging.error(f"Pipeline failed for {pdf_path}: {e}")
        print(f"Pipeline failed: {e}")
        return []

test_pdf = "pdfs/Allossogbe_et_al_2017_Mal_J.pdf"
# Choose chunk_mode='header' for section-aligned, or 'llama_token' for token-based chunking
chunks = process_pdf_pipeline(
    test_pdf,
    chunk_mode='header',            # Or 'llama_token' for alternate strategy
    use_litserve=False,             # Set True/use URL if using LitServe OCR endpoint
    litserve_url=None,              # Set to endpoint address if needed
    send_to_weaviate=False,         # Set True if Weaviate upload desired
    weaviate_url="http://localhost:8080"
)
print(f"\nExtracted {len(chunks)} LlamaIndexDoc-like chunks.")
if chunks:
    print("--- Example Chunk ---")
    print("Header:", chunks[0]['metadata'].get('header'))
    print("Text:", chunks[0]['text'][:400], "\n[...]")