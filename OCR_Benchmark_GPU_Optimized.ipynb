{"cells": [{"cell_type": "markdown", "metadata": {"id": "view-in-github", "colab_type": "text"}, "source": ["<a href=\"https://colab.research.google.com/github/Extralit/papers-ocr-benchmarks/blob/main/OCR_Benchmark_GPU_Optimized.ipynb\" target=\"_parent\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/></a>"]}, {"cell_type": "code", "execution_count": 9, "id": "ba763767", "metadata": {"id": "ba763767", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "339185bc-14b7-4c59-b8bb-22881631cd52"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["✅ All packages installed successfully!\n"]}], "source": ["# Cell 1: Install Dependencies and Setup GPU Environment\n", "\n", "# Install required packages\n", "!uv pip install -q \"PyMuPDF>=1.23.0\" \"pandas>=1.5.0\" \"numpy>=1.21.0\"\n", "!uv pip install -q \"matplotlib>=3.5.0\" \"seaborn>=0.11.0\" \"textdistance>=4.6.0\"\n", "!uv pip install -q torch torchvision torchaudio\n", "\n", "# Install OCR-specific packages\n", "!uv pip install -q marker-pdf  # Advanced ML-based OCR\n", "!uv pip install -q docling     # IBM's document processing\n", "\n", "print(\"✅ All packages installed successfully!\")"]}, {"cell_type": "code", "execution_count": 2, "id": "d271ed3d", "metadata": {"id": "d271ed3d", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "47004a2a-1cce-4f9c-818c-2e85107efd10"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["🚀 GPU ACCELERATION ENABLED\n", "   Device: Tesla T4\n", "   CUDA Version: 12.6\n", "   Total Memory: 15.8 GB\n", "\n", "🚀 OCR BENCHMARK FOR <PERSON>IENT<PERSON><PERSON> LITERATURE - GPU OPTIMIZED\n", "======================================================================\n", "Comparing 3 OCR Systems: <PERSON><PERSON>, <PERSON><PERSON>, PyMuPDF\n", "Dataset: Scientific papers from ./pdfs directory\n", "Compute Device: Tesla T4\n", "======================================================================\n"]}], "source": ["# Cell 2: Import Libraries and GPU Detection\n", "\n", "import os\n", "import time\n", "import pandas as pd\n", "import numpy as np\n", "from pathlib import Path\n", "import fitz  # PyMuPDF\n", "import textdistance\n", "import re\n", "from datetime import datetime\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import torch\n", "\n", "# GPU Detection and Setup\n", "def setup_gpu_environment():\n", "    \"\"\"Setup GPU environment and return device configuration\"\"\"\n", "    device_info = {\n", "        'cuda_available': torch.cuda.is_available(),\n", "        'device_count': 0,\n", "        'device_name': 'CPU',\n", "        'device': 'cpu'\n", "    }\n", "\n", "    if torch.cuda.is_available():\n", "        device_info.update({\n", "            'device_count': torch.cuda.device_count(),\n", "            'device_name': torch.cuda.get_device_name(0),\n", "            'device': 'cuda',\n", "            'cuda_version': torch.version.cuda,\n", "            'memory_total': torch.cuda.get_device_properties(0).total_memory / 1e9,\n", "            'memory_reserved': torch.cuda.memory_reserved(0) / 1e9,\n", "            'memory_allocated': torch.cuda.memory_allocated(0) / 1e9\n", "        })\n", "\n", "        print(\"🚀 GPU ACCELERATION ENABLED\")\n", "        print(f\"   Device: {device_info['device_name']}\")\n", "        print(f\"   CUDA Version: {device_info['cuda_version']}\")\n", "        print(f\"   Total Memory: {device_info['memory_total']:.1f} GB\")\n", "\n", "        # Set optimal GPU settings\n", "        torch.backends.cudnn.benchmark = True\n", "        torch.cuda.empty_cache()\n", "    else:\n", "        print(\"⚠️  GPU not available - using CPU\")\n", "        print(\"   For GPU acceleration, ensure CUDA is properly installed\")\n", "\n", "    return device_info\n", "\n", "# Initialize GPU environment\n", "device_info = setup_gpu_environment()\n", "\n", "print(\"\\n🚀 O<PERSON> BENCHMARK FOR SCIENTIF<PERSON> LITERATURE - GPU OPTIMIZED\")\n", "print(\"=\" * 70)\n", "print(\"Comparing 3 OCR Systems: <PERSON><PERSON>, <PERSON><PERSON>, PyMuPDF\")\n", "print(\"Dataset: Scientific papers from ./pdfs directory\")\n", "print(f\"Compute Device: {device_info['device_name']}\")\n", "print(\"=\" * 70)"]}, {"cell_type": "code", "execution_count": 3, "id": "560c42a5", "metadata": {"id": "560c42a5", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "b66f752a-9bf9-4a5b-9946-8ac63abc1475"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["✅ GPU-Optimized OCR System Classes defined\n"]}], "source": ["# Cell 3: GPU-Optimized OCR System Classes\n", "\n", "class GPUOptimizedOCRSystem:\n", "    \"\"\"GPU-optimized OCR system with automatic device detection\"\"\"\n", "    def __init__(self, name, device_info):\n", "        self.name = name\n", "        self.processing_time = 0\n", "        self.device_info = device_info\n", "        self.device = device_info['device']\n", "        self.initialize()\n", "\n", "    def initialize(self):\n", "        \"\"\"Initialize the OCR system with GPU optimization\"\"\"\n", "        if self.name == \"<PERSON><PERSON>\":\n", "            from docling.document_converter import DocumentConverter\n", "\n", "            # GPU-optimized Docling configuration\n", "            if self.device_info['cuda_available']:\n", "                # Configure for GPU if available\n", "                self.converter = DocumentConverter()\n", "                print(f\"✅ {self.name} initialized with GPU acceleration\")\n", "            else:\n", "                self.converter = DocumentConverter()\n", "                print(f\"✅ {self.name} initialized (CPU mode)\")\n", "\n", "        elif self.name == \"<PERSON>er\":\n", "            from marker.converters.pdf import PdfConverter\n", "            from marker.models import create_model_dict\n", "\n", "            # GPU-optimized Marker configuration\n", "            if self.device_info['cuda_available']:\n", "                # Force GPU usage for Marker models\n", "                os.environ['CUDA_VISIBLE_DEVICES'] = '0'\n", "                model_dict = create_model_dict()\n", "                self.converter = PdfConverter(\n", "                    artifact_dict=model_dict,\n", "                    processor_list=None,\n", "                    renderer=None\n", "                )\n", "                print(f\"✅ {self.name} initialized with GPU acceleration\")\n", "            else:\n", "                model_dict = create_model_dict()\n", "                self.converter = PdfConverter(\n", "                    artifact_dict=model_dict,\n", "                    processor_list=None,\n", "                    renderer=None\n", "                )\n", "                print(f\"✅ {self.name} initialized (CPU mode)\")\n", "\n", "        elif self.name == \"PyMuPDF\":\n", "            print(f\"✅ {self.name} initialized (CPU-based)\")\n", "\n", "    def extract_text(self, pdf_path):\n", "        \"\"\"Extract text from PDF with GPU optimization\"\"\"\n", "        start_time = time.time()\n", "\n", "        # Clear GPU cache before processing\n", "        if self.device_info['cuda_available']:\n", "            torch.cuda.empty_cache()\n", "\n", "        try:\n", "            if self.name == \"<PERSON><PERSON>\":\n", "                result = self.converter.convert(str(pdf_path))\n", "                text = result.document.export_to_markdown()\n", "\n", "            elif self.name == \"<PERSON>er\":\n", "                document = self.converter(str(pdf_path))\n", "                # Handle different Marker API versions\n", "                if hasattr(document, 'render'):\n", "                    text = document.render()\n", "                <PERSON><PERSON>(document, 'markdown'):\n", "                    text = document.markdown\n", "                else:\n", "                    text = str(document)\n", "\n", "            elif self.name == \"PyMuPDF\":\n", "                doc = fitz.open(str(pdf_path))\n", "                text = \"\"\n", "                for page_num in range(len(doc)):\n", "                    page = doc.load_page(page_num)\n", "                    text += f\"\\n=== Page {page_num + 1} ===\\n{page.get_text()}\\n\"\n", "                doc.close()\n", "\n", "            self.processing_time = time.time() - start_time\n", "\n", "            # Log GPU memory usage if available\n", "            if self.device_info['cuda_available']:\n", "                memory_used = torch.cuda.memory_allocated(0) / 1e9\n", "                print(f\"    🔥 GPU Memory Used: {memory_used:.2f} GB\")\n", "\n", "            return text, {\n", "                'status': 'success',\n", "                'processing_time': self.processing_time,\n", "                'device': self.device,\n", "                'gpu_memory_used': torch.cuda.memory_allocated(0) / 1e9 if self.device_info['cuda_available'] else 0\n", "            }\n", "\n", "        except Exception as e:\n", "            self.processing_time = time.time() - start_time\n", "            print(f\"    ❌ {self.name} error: {str(e)}\")\n", "            return f\"Error: {str(e)}\", {\n", "                'status': 'error',\n", "                'processing_time': self.processing_time,\n", "                'device': self.device,\n", "                'error': str(e)\n", "            }\n", "        finally:\n", "            # Clean up GPU memory\n", "            if self.device_info['cuda_available']:\n", "                torch.cuda.empty_cache()\n", "\n", "print(\"✅ GPU-Optimized OCR System Classes defined\")"]}, {"cell_type": "code", "execution_count": 4, "id": "28b363cc", "metadata": {"id": "28b363cc", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "36317d2d-6b2d-4088-ce57-034eb0f88d43"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["✅ Enhanced evaluation metrics functions defined\n"]}], "source": ["# Cell 4: Enhanced Evaluation Metrics\n", "\n", "def calculate_text_metrics(reference_text, candidate_text):\n", "    \"\"\"Calculate comprehensive text comparison metrics\"\"\"\n", "    if not reference_text or not candidate_text:\n", "        return {\n", "            'character_accuracy': 0.0,\n", "            'word_accuracy': 0.0,\n", "            'length_ratio': 0.0,\n", "            'word_count_ratio': 0.0,\n", "            'line_count_ratio': 0.0\n", "        }\n", "\n", "    # Clean texts\n", "    ref_clean = re.sub(r'\\s+', ' ', reference_text.strip())\n", "    cand_clean = re.sub(r'\\s+', ' ', candidate_text.strip())\n", "\n", "    # Character-level accuracy using Levens<PERSON>ein distance\n", "    char_distance = textdistance.levenshtein(ref_clean, cand_clean)\n", "    max_len = max(len(ref_clean), len(cand_clean))\n", "    char_accuracy = 1 - (char_distance / max_len) if max_len > 0 else 1.0\n", "\n", "    # Word-level accuracy\n", "    ref_words = reference_text.lower().split()\n", "    cand_words = candidate_text.lower().split()\n", "\n", "    if ref_words:\n", "        word_distance = textdistance.levenshtein(ref_words, cand_words)\n", "        word_accuracy = 1 - (word_distance / max(len(ref_words), len(cand_words)))\n", "    else:\n", "        word_accuracy = 1.0 if not cand_words else 0.0\n", "\n", "    # Additional metrics\n", "    length_ratio = len(candidate_text) / len(reference_text) if len(reference_text) > 0 else 0.0\n", "    word_count_ratio = len(cand_words) / len(ref_words) if len(ref_words) > 0 else 0.0\n", "\n", "    ref_lines = len(reference_text.split('\\n'))\n", "    cand_lines = len(candidate_text.split('\\n'))\n", "    line_count_ratio = cand_lines / ref_lines if ref_lines > 0 else 0.0\n", "\n", "    return {\n", "        'character_accuracy': max(0.0, char_accuracy),\n", "        'word_accuracy': max(0.0, word_accuracy),\n", "        'length_ratio': length_ratio,\n", "        'word_count_ratio': word_count_ratio,\n", "        'line_count_ratio': line_count_ratio\n", "    }\n", "\n", "def analyze_scientific_content(text):\n", "    \"\"\"Analyze scientific content preservation with enhanced patterns\"\"\"\n", "    # Enhanced patterns for scientific content\n", "    equations = len(re.findall(r'\\$.*?\\$|\\\\\\(.*?\\\\\\)|\\\\\\[.*?\\\\\\]|\\\\begin\\{equation\\}.*?\\\\end\\{equation\\}', text, re.DOTALL))\n", "    citations = len(re.findall(r'\\[[\\d,\\s-]+\\]|\\(\\w+\\s+et\\s+al\\.?,?\\s+\\d{4}\\)|\\(\\w+,?\\s+\\d{4}\\)', text))\n", "    figures = len(re.findall(r'[Ff]igure\\s+\\d+|[Ff]ig\\.?\\s+\\d+|Figure\\s+[A-Z]', text))\n", "    tables = len(re.findall(r'[Tt]able\\s+\\d+|Table\\s+[A-Z]', text))\n", "    formulas = len(re.findall(r'[A-Za-z]+\\s*=\\s*[A-Za-z0-9\\+\\-\\*/\\(\\)]+', text))\n", "\n", "    return {\n", "        'equations_count': equations,\n", "        'citations_count': citations,\n", "        'figures_count': figures,\n", "        'tables_count': tables,\n", "        'formulas_count': formulas,\n", "        'total_scientific_elements': equations + citations + figures + tables + formulas\n", "    }\n", "\n", "print(\"✅ Enhanced evaluation metrics functions defined\")"]}, {"cell_type": "code", "execution_count": 5, "id": "b2502e6e", "metadata": {"id": "b2502e6e", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "6e946855-a6d3-4da8-b3cb-d4dc5b99f01a"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["✅ GPU-optimized benchmark runner function defined\n"]}], "source": ["# Cell 5: GPU-Optimized Benchmark Runner\n", "\n", "def run_gpu_optimized_benchmark():\n", "    \"\"\"Run the complete OCR benchmark with GPU optimization\"\"\"\n", "\n", "    # Initialize GPU-optimized OCR systems\n", "    systems = {\n", "        'Docling': GPUOptimizedOCRSystem('Docling', device_info),\n", "        'Marker': GPUOptimizedOCRSystem('Marker', device_info),\n", "        'PyMuPDF': GPUOptimizedOCRSystem('PyMuPDF', device_info)\n", "    }\n", "\n", "    # Find PDFs\n", "    pdf_dir = Path('./pdfs')\n", "    pdf_files = list(pdf_dir.glob('*.pdf'))\n", "\n", "    if not pdf_files:\n", "        print(\"❌ No PDFs found in ./pdfs directory!\")\n", "        return None, None\n", "\n", "    print(f\"\\n📚 Found {len(pdf_files)} PDFs:\")\n", "    for pdf in pdf_files:\n", "        print(f\"  • {pdf.name}\")\n", "\n", "    # Create output directory in results folder\n", "    timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "    output_dir = Path(f\"./results/gpu_benchmark_{timestamp}\")\n", "    output_dir.mkdir(parents=True, exist_ok=True)\n", "\n", "    # Also ensure main results directory exists\n", "    Path(\"./results\").mkdir(exist_ok=True)\n", "\n", "    # Log system information\n", "    system_info_file = output_dir / 'system_info.txt'\n", "    with open(system_info_file, 'w') as f:\n", "        f.write(\"GPU-Optimized OCR Benchmark System Information\\n\")\n", "        f.write(\"=\" * 50 + \"\\n\")\n", "        f.write(f\"Timestamp: {datetime.now()}\\n\")\n", "        f.write(f\"CUDA Available: {device_info['cuda_available']}\\n\")\n", "        f.write(f\"Device: {device_info['device_name']}\\n\")\n", "        if device_info['cuda_available']:\n", "            f.write(f\"CUDA Version: {device_info['cuda_version']}\\n\")\n", "            f.write(f\"GPU Memory: {device_info['memory_total']:.1f} GB\\n\")\n", "        f.write(f\"PyTorch Version: {torch.__version__}\\n\")\n", "\n", "    # Run extractions with GPU monitoring\n", "    all_extractions = {}\n", "\n", "    for pdf_path in pdf_files:\n", "        pdf_name = pdf_path.stem\n", "        print(f\"\\n📖 Processing: {pdf_name}\")\n", "        print(\"-\" * 60)\n", "\n", "        extractions = {}\n", "\n", "        for system_name, system in systems.items():\n", "            print(f\"🔄 {system_name}...\")\n", "\n", "            # Monitor GPU memory before processing\n", "            if device_info['cuda_available']:\n", "                memory_before = torch.cuda.memory_allocated(0) / 1e9\n", "                print(f\"    📊 GPU Memory Before: {memory_before:.2f} GB\")\n", "\n", "            text, metadata = system.extract_text(pdf_path)\n", "\n", "            # Save extracted text with metadata\n", "            output_file = output_dir / f\"{pdf_name}_{system_name}.txt\"\n", "            with open(output_file, 'w', encoding='utf-8') as f:\n", "                f.write(f\"OCR System: {system_name}\\n\")\n", "                f.write(f\"PDF: {pdf_name}\\n\")\n", "                f.write(f\"Processing Time: {metadata.get('processing_time', 0):.2f}s\\n\")\n", "                f.write(f\"Device: {metadata.get('device', 'unknown')}\\n\")\n", "                f.write(f\"GPU Memory Used: {metadata.get('gpu_memory_used', 0):.2f} GB\\n\")\n", "                f.write(f\"Status: {metadata.get('status', 'unknown')}\\n\")\n", "                f.write(\"=\" * 60 + \"\\n\\n\")\n", "                f.write(text)\n", "\n", "            extractions[system_name] = {\n", "                'text': text,\n", "                'metadata': metadata\n", "            }\n", "\n", "            print(f\"    ⏱️  Time: {metadata.get('processing_time', 0):.2f}s\")\n", "            print(f\"    📝 Length: {len(text):,} chars\")\n", "            print(f\"    💾 Saved: {output_file.name}\")\n", "\n", "        all_extractions[pdf_name] = extractions\n", "\n", "    return all_extractions, output_dir\n", "\n", "print(\"✅ GPU-optimized benchmark runner function defined\")"]}, {"cell_type": "code", "metadata": {"id": "d0a571d8", "outputId": "9bf39c96-c1b1-4068-cf3e-114e5bc163ad", "colab": {"base_uri": "https://localhost:8080/"}}, "source": ["# Cell 9: Download PDFs from GitHub\n", "\n", "!git clone https://github.com/Extralit/papers-ocr-benchmarks.git /tmp/papers-ocr-benchmarks\n", "!mkdir -p ./pdfs\n", "!cp /tmp/papers-ocr-benchmarks/pdfs/* ./pdfs/\n", "!rm -rf /tmp/papers-ocr-benchmarks\n", "\n", "print(\"✅ PDFs downloaded to ./pdfs directory!\")"], "id": "d0a571d8", "execution_count": 7, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Cloning into '/tmp/papers-ocr-benchmarks'...\n", "remote: Enumerating objects: 77, done.\u001b[K\n", "remote: Counting objects: 100% (77/77), done.\u001b[K\n", "remote: Compressing objects: 100% (50/50), done.\u001b[K\n", "remote: Total 77 (delta 30), reused 67 (delta 25), pack-reused 0 (from 0)\u001b[K\n", "Receiving objects: 100% (77/77), 2.27 MiB | 23.99 MiB/s, done.\n", "Resolving deltas: 100% (30/30), done.\n", "✅ PDFs downloaded to ./pdfs directory!\n"]}]}, {"cell_type": "code", "execution_count": 8, "id": "3feda8cc", "metadata": {"id": "3feda8cc", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "431064bf-d640-4730-e0eb-594f444bd62f"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["🚀 Starting GPU-Optimized OCR Benchmark...\n", "✅ Docling initialized with GPU acceleration\n", "✅ Marker initialized with GPU acceleration\n", "✅ PyMuPDF initialized (CPU-based)\n", "\n", "📚 Found 3 PDFs:\n", "  • Allossogbe_et_al_2017_Mal_J.pdf\n", "  • Somboon_et_al_1995_Trans_RSTMH.pdf\n", "  • 2014-Combining_organophosphate_treated_wall_linings_and_long-lasting_insecticidal_nets_for_improved_control_of_pyrethroi.pdf\n", "\n", "📖 Processing: Allossogbe_et_al_2017_Mal_J\n", "------------------------------------------------------------\n", "🔄 Docling...\n", "    📊 GPU Memory Before: 2.46 GB\n"]}, {"output_type": "stream", "name": "stderr", "text": ["WARNING:easyocr.easyocr:Downloading detection model, please wait. This may take several minutes depending upon your network connection.\n", "WARNING:easyocr.easyocr:Downloading recognition model, please wait. This may take several minutes depending upon your network connection.\n", "/usr/local/lib/python3.11/dist-packages/huggingface_hub/utils/_auth.py:94: UserWarning: \n", "The secret `HF_TOKEN` does not exist in your Colab secrets.\n", "To authenticate with the Hugging Face Hub, create a token in your settings tab (https://huggingface.co/settings/tokens), set it as secret in your Google Colab and restart your session.\n", "You will be able to reuse this secret in all of your notebooks.\n", "Please note that authentication is recommended but still optional to access public models or datasets.\n", "  warnings.warn(\n"]}, {"output_type": "stream", "name": "stdout", "text": ["    🔥 GPU Memory Used: 2.97 GB\n", "    ⏱️  Time: 35.12s\n", "    📝 Length: 53,171 chars\n", "    💾 Saved: Allossogbe_et_al_2017_Mal_J_Docling.txt\n", "🔄 Marker...\n", "    📊 GPU Memory Before: 2.97 GB\n"]}, {"output_type": "stream", "name": "stderr", "text": ["Recognizing layout: 100%|██████████| 1/1 [00:02<00:00,  2.07s/it]\n", "Running OCR Error Detection: 100%|██████████| 1/1 [00:00<00:00, 26.21it/s]\n", "Detecting bboxes: 0it [00:00, ?it/s]\n", "Detecting bboxes: 0it [00:00, ?it/s]\n", "Recognizing tables: 100%|██████████| 1/1 [00:01<00:00,  1.11s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["    🔥 GPU Memory Used: 2.99 GB\n", "    ⏱️  Time: 8.58s\n", "    📝 Length: 54,803 chars\n", "    💾 Saved: Allossogbe_et_al_2017_Mal_J_Marker.txt\n", "🔄 PyMuPDF...\n", "    📊 GPU Memory Before: 2.99 GB\n", "    🔥 GPU Memory Used: 2.99 GB\n", "    ⏱️  Time: 0.21s\n", "    📝 Length: 46,345 chars\n", "    💾 Saved: Allossogbe_et_al_2017_Mal_J_PyMuPDF.txt\n", "\n", "📖 Processing: Somboon_et_al_1995_Trans_RSTMH\n", "------------------------------------------------------------\n", "🔄 Docling...\n", "    📊 GPU Memory Before: 2.99 GB\n", "    🔥 GPU Memory Used: 2.99 GB\n", "    ⏱️  Time: 82.26s\n", "    📝 Length: 43,194 chars\n", "    💾 Saved: Somboon_et_al_1995_Trans_RSTMH_Docling.txt\n", "🔄 Marker...\n", "    📊 GPU Memory Before: 2.99 GB\n"]}, {"output_type": "stream", "name": "stderr", "text": ["Recognizing layout: 100%|██████████| 1/1 [00:01<00:00,  1.30s/it]\n", "Running OCR Error Detection: 100%|██████████| 1/1 [00:00<00:00, 36.27it/s]\n", "Detecting bboxes: 0it [00:00, ?it/s]\n", "Detecting bboxes: 0it [00:00, ?it/s]\n", "Recognizing tables: 100%|██████████| 1/1 [00:00<00:00,  1.56it/s]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["    🔥 GPU Memory Used: 2.99 GB\n", "    ⏱️  Time: 6.16s\n", "    📝 Length: 41,318 chars\n", "    💾 Saved: Somboon_et_al_1995_Trans_RSTMH_Marker.txt\n", "🔄 PyMuPDF...\n", "    📊 GPU Memory Before: 2.99 GB\n", "    🔥 GPU Memory Used: 2.99 GB\n", "    ⏱️  Time: 0.04s\n", "    📝 Length: 41,276 chars\n", "    💾 Saved: Somboon_et_al_1995_Trans_RSTMH_PyMuPDF.txt\n", "\n", "📖 Processing: 2014-Combining_organophosphate_treated_wall_linings_and_long-lasting_insecticidal_nets_for_improved_control_of_pyrethroi\n", "------------------------------------------------------------\n", "🔄 Docling...\n", "    📊 GPU Memory Before: 2.99 GB\n", "    🔥 GPU Memory Used: 2.99 GB\n", "    ⏱️  Time: 19.29s\n", "    📝 Length: 61,550 chars\n", "    💾 Saved: 2014-Combining_organophosphate_treated_wall_linings_and_long-lasting_insecticidal_nets_for_improved_control_of_pyrethroi_Docling.txt\n", "🔄 Marker...\n", "    📊 GPU Memory Before: 2.99 GB\n"]}, {"output_type": "stream", "name": "stderr", "text": ["Recognizing layout: 100%|██████████| 1/1 [00:01<00:00,  1.66s/it]\n", "Running OCR Error Detection: 100%|██████████| 1/1 [00:00<00:00, 29.70it/s]\n", "Detecting bboxes: 0it [00:00, ?it/s]\n", "Recognizing Text: 100%|██████████| 3/3 [00:00<00:00,  3.01it/s]\n", "Detecting bboxes: 0it [00:00, ?it/s]\n", "Recognizing tables: 100%|██████████| 1/1 [00:01<00:00,  1.23s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["    🔥 GPU Memory Used: 3.01 GB\n", "    ⏱️  Time: 8.19s\n", "    📝 Length: 60,054 chars\n", "    💾 Saved: 2014-Combining_organophosphate_treated_wall_linings_and_long-lasting_insecticidal_nets_for_improved_control_of_pyrethroi_Marker.txt\n", "🔄 PyMuPDF...\n", "    📊 GPU Memory Before: 3.01 GB\n", "    🔥 GPU Memory Used: 3.01 GB\n", "    ⏱️  Time: 0.04s\n", "    📝 Length: 52,363 chars\n", "    💾 Saved: 2014-Combining_organophosphate_treated_wall_linings_and_long-lasting_insecticidal_nets_for_improved_control_of_pyrethroi_PyMuPDF.txt\n", "\n", "✅ Benchmark completed!\n", "📁 Results saved to: results/gpu_benchmark_20250703_055218\n"]}], "source": ["# Cell 6: Run the GPU-Optimized Benchmark\n", "\n", "print(\"🚀 Starting GPU-Optimized OCR Benchmark...\")\n", "extractions, output_dir = run_gpu_optimized_benchmark()\n", "\n", "if extractions:\n", "    print(f\"\\n✅ Benchmark completed!\")\n", "    print(f\"📁 Results saved to: {output_dir}\")\n", "else:\n", "    print(\"❌ Benchmark failed!\")"]}, {"cell_type": "code", "source": ["!cat /content/results/gpu_benchmark_20250703_055218/Somboon_et_al_1995_Trans_RSTMH_PyMuPDF.txt"], "metadata": {"id": "3mc9lgAe2490", "outputId": "fa883507-3c8b-4eb9-b756-161e529db32f", "colab": {"base_uri": "https://localhost:8080/"}}, "id": "3mc9lgAe2490", "execution_count": 13, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["OCR System: PyMuPDF\n", "PDF: Somboon_et_al_1995_Trans_RSTMH\n", "Processing Time: 0.04s\n", "Device: cuda\n", "GPU Memory Used: 2.99 GB\n", "Status: success\n", "============================================================\n", "\n", "\n", "=== Page 1 ===\n", "248 \n", "TRANSACTIONS \n", "OF THE ROYAL SOCIETY OF TROPICAL MEDICINE AND H<PERSON>GIENE (1995) 89, 248-254 \n", "Entomological \n", "evaluation \n", "of community-wide \n", "use of lambdacyhalothrin- \n", "impregnated \n", "bed nets against \n", "malaria \n", "in a border area of north-west \n", "Thailand \n", "Pradya Somboon’, <PERSON>2, <PERSON><PERSON><PERSON>3, <PERSON><PERSON>pra<PERSON>4, Somsak Prajakwong4 and <PERSON><PERSON><PERSON> \n", "Khamboonrua& \n", "‘Department of Parasitology, Faculty of Medicine, Chiang Mai University, Chiang Mai 50200, \n", "Thailand; 2Department of Medical Parasitology, London School of Hygiene and Tropical Medicine, Keppel Street, London, \n", "WCIE \n", "7HT, UK; 3Department of Family Medicine, Faculty of Medicine, Chiang Mai University, Chiang Mai 50200, \n", "Thailand; 4Malaria Centre, Region 2, Chiang Mai 50200, Thailand; SResearch Institute for Health Sciences, Chiang Mai \n", "University, Chiang Mai 50200, Thailand \n", "Abstract \n", "This paper reports 2 studies. (i) After a year of baseline data collection, lambdacyhalothrin-treated bed nets \n", "were introduced into 3 of 5 villages in north-west Thailand, the remaining 2 being treated with placebo. \n", "Human bait collections were carried out in each village on 2 nights per month, for 8 months of each year, \n", "and the biting densities were compared between the first year and the second year. The treated bed nets did \n", "not have any significant impact on the density or parous rates of <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> maculatus \n", "S.S. populations. The results for A. dirus s.1. were not conclusive because of the low number caught. Signifi- \n", "cant reductions in biting and parous rates of A. minimus species A were observed in only one of the 3 treated \n", "villages, and there was no overall difference between treated and control groups. However, the trial suffered \n", "from the washing of nets by villagers and the low rate of reimpregnation. (ii) A short-term study involved 4 \n", "villages in a cross-over design, and lasted 48 d. For the first 24 d, residents of 2 villages were given new \n", "treated nets while the other 2 villages retained their own untreated nets. For the second 24 d, this situation \n", "was reversed. Daily light-trapping revealed no significant difference in the indoor densities or parous rates \n", "of A. minimus species A between the periods with treated or untreated nets. Both studies, especially the sec- \n", "ond., suggested that the community-wide use of treated bed nets did not generally reduce the vectorial ca- \n", "pacity of vectors in this area, probably because of the biting behaviour of the mosquitoes \n", "Keywords: insecticide-impregnated \n", "bed nets, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> minimus, biting rate, \n", "paious rate, Thailand \n", "Introduction \n", "Malaria is a major public health problem in Thailand, \n", "especially in forest and border areas, Control of malaria \n", "transmission by DDT house spraying, although success- \n", "ful in interru \n", "plains areas o 4 \n", "ting malaria transmission in most of the \n", "the country, has experienced many prob- \n", "lems elsewhere, including the exophilic behaviour of all \n", "main vector species (i.e., Anopheles minimus s.l., <PERSON><PERSON> dirus \n", "s.l., and <PERSON><PERSON> maculatus s.l.), the increasingly poor accept- \n", "ance by communities of the spraying, and the movement \n", "of people to intense foci of transmission both inside and \n", "outside the country (KETRANGSEE \n", "et al., 1991). \n", "One possible alternative method for control of malaria \n", "is the use of pyrethroid-treated bed nets (ROZENDAAL, \n", "1989; WHO, 1989; CURTIS et al., 1990), which have \n", "shown benefits in reducing the number of bites on hu- \n", "mans sleeping within them, even if there are holes in \n", "them, or on humans nearby because of the deterrent and \n", "excite-repellent effects and feeding inhibition of the in- \n", "secticide (<PERSON><PERSON><PERSON> et al., 1987). When used by a whole \n", "community, the treated nets might be expected to have a \n", "‘mass effect’, reducing the density and longevity of vec- \n", "tor populations, and thus providing an additional benefit \n", "above the improved personal protection provided by in- \n", "dividual use. However, of the numerous trials reviewed \n", "by CURTIS (1992) or reported more recently (SAMA- \n", "RAWICKREMA \n", "et al., 1992; B<PERSON><PERSON> et al., 1993; DAS et al., \n", "1993; <PERSON><PERSON> et al., 1993; <PERSON><PERSON><PERSON> et al., 1993; <PERSON><PERSON><PERSON> et al., \n", "1993; LINDSAY et al., 1993a, 1993b), many were not de- \n", "signed to allow detection of a mass effect, and in others \n", "no mass effect was seen. The clearest evidence for a mass \n", "effect has come from trials with anthropophilic and endo- \n", "philic A. gam<PERSON> in Africa (MAGESA et al., 1991; RO- \n", "BERT & CARNEVALE, 1991; <PERSON><PERSON><PERSON> et al., 1993), with en- \n", "dophilic populations of A. \n", "minimus s.1. in India \n", "(JANA-KARA et al., in press), and with <PERSON><PERSON> in the \n", "Solomon Islands (<PERSON><PERSON><PERSON> et al., 1993). <PERSON><PERSON>, evidence \n", "for the absence of a mass effect with A. gambtae s.1. was \n", "reported from The Gambia; this may have been due to \n", "the mixing of mosquito populations between villages \n", "with treated and untreated bed nets (LINDSA<PERSON> et al., \n", "1993b). In the A. tn4nctulatus complex in Papua New \n", "Guinea, there was a-reduction in th; human blood index \n", "and a sluft in the peak time of biting from late to early in \n", "the night, but no evidence for a reduction in density or \n", "longevity (MILLEN, \n", "1986; CHARLWOOD & GRAVES, \n", "1987; <PERSON><PERSON><PERSON><PERSON> et al., 1987). \n", "Starting in late 1989, a large-scale epidemiological trial \n", "of lambdacyhalothrin-treated bed nets was carried out in \n", "north-west Thailand. This paper presents the entomo- \n", "logical evaluation, which consisted of a long-term and a \n", "short-term study. The impact on malaria incidence will \n", "be presented separately by <PERSON><PERSON> et al. (paper \n", "in preparation). \n", "Materials and Methods \n", "The long-term evaluation \n", "Study area. The study was carried out in Karen vil- \n", "lages located in forest and forest fringe areas in Mae Sa- \n", "rianp: district. Mae Hong Son movince. north-west Thai- \n", "land: adjacent to the Mianmar horde;. The province is \n", "extremely mountainous and forested, with very limited \n", "plains areas. Forest fringe villa es are typically situated \n", "in foothills surrounded by rice dds, scrub and second- \n", "H \n", "ary forest, with streams running through or alongside \n", "them. Farm huts, the temporary shelters to which vil- \n", "lagers move during ploughing and harvesting periods, \n", "are scattered in rice fields m the valleys and on the moun- \n", "tains. Some villages are situated deeper in the hills and in \n", "the forest. Cattle and pigs are common in most Karen \n", "settlements. \n", "Four forest fringe villages, Mae Han, Mae Chon, Mae \n", "Top Nua and Mae Salap, and a deep forest village, Huai \n", "Neu. were selected for the studv. These villages were \n", "aGong the 24 units of a arasitolo&cal evaluation(AR.4~- \n", "RATTANA, 1993). Mae f!I alap and <PERSON><PERSON> are isolated \n", "villages. At Mae Top Nua there is a neighbouring village \n", "about 500 m away, separated by 2 hills. Mae Han and \n", "Mae Chon are separated from each other by about 800 m \n", "and a hill. The annual malaria Darasite incidence in 1989 \n", "varied from 80.7-279.7 per 1060 persons. Details of the \n", "population and bed net usage are given in Table 1. De- \n", "tection of malaria sporozoites by enzyme-linked immu- \n", "nosorbent assay (<PERSON><PERSON><PERSON><PERSON> et al., 1984; <PERSON><PERSON><PERSON><PERSON> et al., \n", "1987) has suggested that A. minimus species A, <PERSON><PERSON> sawad- \n", "<PERSON><PERSON><PERSON><PERSON> (formerly A. maculatus species A), <PERSON><PERSON> macu- \n", "latus S.S. and <PERSON><PERSON> dirus s.1. are the potential vectors in the \n", "area (SOMBOON, 1993). \n", "Downloaded from https://academic.oup.com/trstmh/article-abstract/89/3/248/1885449 by Columbia University user on 24 April 2020\n", "\n", "\n", "=== Page 2 ===\n", "249 \n", "Table 1. Population and numbers of bed nets in the 5 study villages, Mae Han, Mae <PERSON>, Mae <PERSON>, Mae \n", "<PERSON><PERSON>, and <PERSON><PERSON>, in the long-term study \n", "No. of households \n", "Populationa \n", "No. of nets currently usedb \n", "No. of persons/net \n", "No. of nets treated \n", "First treatmentC \n", "Second treatmentd \n", "Control villa es \n", "<PERSON> \n", "ii ae Top \n", "<PERSON> \n", "Treatitzllages \n", "Han \n", "<PERSON><PERSON> \n", "<PERSON>n \n", "&lap \n", "149 \n", "719 \n", "lIf!l \n", ";: \n", "2:: \n", "1;: \n", "304 \n", "2437 \n", "2:: \n", "118 \n", "2.4 \n", "2.2 \n", "2% \n", "55 (18.1%) \n", "36 (83.7%) \n", "31 (81.5%) \n", "96 (81.4%) \n", "47 (97.9%) \n", "18 (5.9%) \n", "12 (27.9%) \n", "14 (36.8%) \n", "37 (31.4%) \n", "30 (62.5%) \n", "aData from Mae Sariang Malaria Sector (1990). \n", "bSurveyed in September-November 1990. \n", "$ebruary/March 1991. \n", "October/November 1991. \n", "Since the coverage of DDT spraying in the study vil- \n", "lages had been very poor for a nhberbf \n", "years and there \n", "was little evidence that this method was still effective, \n", "spraying was suspended within a radius of 5 km from \n", "October 1989 until the end of the study. Other routine \n", "anti-malaria activities continued. \n", "Study design. The study was divided into 2 phases, the \n", "first (baseline phase) during the high transmission season \n", "from May to December 1990 and the second (interven- \n", "tion phase) during the same months in 1991. In Fe- \n", "bruary/March 1991, bed nets in Mae Chon, Mae &lap \n", "and <PERSON><PERSON> were treated with lambdacyhalothrin (10 \n", "mg/m2) while those in Mae Han and Mae Top Nua were \n", "treated with placebo. The design permitted pre- and \n", "post-intervention comparisons in each village, and con- \n", "temporary comparisons between treated and untreated \n", "villages. \n", "Net census, impregnation and distribution. Most people \n", "in the area used cotton or cotton-polyester nets. In an at- \n", "tempt to obtain a high rate of net &age, a census was car- \n", "ried out in earlv 1990 and a number of additional nets \n", "were distributed at a subsidized price. Over 80% of nets \n", "in all the entomological study villages were treated in the \n", "first round of impregnation; except for Mae Han where \n", "the coverage was verv low (see Table 11. In the second \n", "round of Geatment in October/ November 1991, rela- \n", "tively low coverage was found over the whole study area. \n", "Details of net impregnation and distribution will be de- \n", "scribed by <PERSON><PERSON> et al. (paper in preparation). \n", "Entomological data collection and analysis. Mosquitoes \n", "were collected on 2 human baits indoors and 2 outdoors \n", "for 2 nights each month from May to December 1990 \n", "(before intervention1 and Mav to December 1991 (after \n", "intervention). The indoor collections were perfo&ed \n", "outside the sleeping room of villagers throughout the \n", "night, but the outdoor catches (5-10 m away) were made \n", "only until midnight. Similar collections were carried out \n", "simultaneously at the farm hut areas of each forest fringe \n", "village (located 2-3 km away). After morphological \n", "identification, the ovaries of mosquitoes were examined \n", "for parity (DETINOVA, 1962). \n", "In each catching site each year, the monthly counts of \n", "mosquitoes, indoors plus outdoors, were transformed as \n", "log (n+l) to stabilize the variance. Paired t tests were \n", "used to corn \n", "numbers be ore and after intervention on a monthly \n", "P \n", "are the difference between the mean log \n", "basis. Mantel-Haenszel x2 tests were used to compare the \n", "proportions of nulliparous and parous mosquitoes in \n", "ia& catchin \n", "EPISTAT@ so P \n", "site, stiatifying for corresponding months. \n", "tware (GUSTAFSON. 1989) was used to calcu- \n", "late and analyse da& \n", ", \n", ", \n", "The short-term evaluation \n", "Study design and study area. Since recruitment to mos- \n", "quito populations fluctuates widely in the short term, \n", "more intensive measurements of mosquito density and \n", "parous rates on a daily basis may give a more reliable pic- \n", "ture than less intensive occasional sampling over longer \n", "periods, as was done in the long-term study (CHARL- \n", "WO<PERSON> et al., \n", "1985; CHARLWOOD & GRAVES, 1987; \n", "HOLMES & BIRLEY, 1987). Daily sampling increases the \n", "sample size in relation to variability so that small \n", "changes in the mosquito density and parous rate may be \n", "detected. A short-term study of the effect of treated bed \n", "nets on vector populations was therefore carried out in \n", "order to complement the long-term study. A cross-over \n", "design permitted each village to act as both control and \n", "treated units, thus allowing for the permanent effects of \n", "environmental differences. Washing of nets was un- \n", "likely to have taken place over the relatively short study \n", "period and was easy to check. \n", "The study was conducted in 4 communities, 2 from \n", "the previous study (<PERSON> and <PERSON>), and 2 \n", "others: <PERSON> and the Wild Animal Conserva- \n", "tion Centre. The human populations surveyed in May \n", "1992 were 92, 99, 75 and 57, respectively. <PERSON> \n", "Klang is another Karen village separated from Mae Top \n", "Nua by about 500 m and 2 hills (see above), and was in- \n", "volved-in the previous epidemiolbgical study as a control \n", "(untreated) unit in 1990-1991. The Wild Animal Conser- \n", "iation Ceitre is a government office with permanently \n", "resident staff living in Karen style houses. It is about 8 \n", "km north of Mae Top Nua, and surrounded by relatively \n", "dense forest. \n", "Shortly before the study began, malaria workers had \n", "treated the bed nets in all the neiahbourina villages as \n", "part of regional malaria control effoyts. DDTspraying in \n", "these study sites had been suspended since October 1989 \n", "(see above). \n", "Intervention method. House-to-house surveys were car- \n", "ried out one month before the intervention began. The \n", "purpose of the experiment was explained to the residents. \n", "Since <PERSON> and Mae <PERSON> Klang are close to \n", "each other, and there may have been some overlapping of \n", "their mosquito populations, they were treated in the \n", "same period. On 1 July 1992 new cotton-polyester bed \n", "nets treated individually with lambdacyhalothrin (10 \n", "mglm2) were distributed to everyone in Mae Top Nua \n", "and <PERSON> \n", "P \n", "Klang except the occupants of 3 sentinel \n", "rooms used or mosquito trapping. The bed nets of resi- \n", "dents who already hid them tier; temporarily stored to \n", "ensure that thev used onlv the treated ones. On 25 Tulv \n", "1992 the treated nets were replaced with the occup&t? \n", "own bed nets, which were untreated. In the other 2 com- \n", "munities, <PERSON>n and the Wild Animal Conservation \n", "Centre, this pattern was reversed. At the end of the study \n", "all the treated bed nets were given free to the villagers. \n", "The villagers’ bed nets were also treated as requested. In \n", "Mae Chon, where 36% of nets had been treated in late \n", "1991 in the long-term study (about 6 months before this \n", "study began), people were encouraged to wash their nets \n", "before the study. \n", "Downloaded from https://academic.oup.com/trstmh/article-abstract/89/3/248/1885449 by Columbia University user on 24 April 2020\n", "\n", "\n", "=== Page 3 ===\n", "E \n", "lean log(n+l)/mol \n", "UNTREATED \n", "0 \n", "MH \n", "MTN OVERALL MC \n", "MSL \n", "HN OVERALL \n", "B \n", ", 2Mean log(n+lMnonth \n", ". \n", "UNTREATED \n", "ntl h \n", "I \n", "2.6Mean log(n+l)/month \n", "I \n", "TREATED \n", "I \n", "UNTREATED \n", "F \n", "- \n", "MH \n", "MTN OVERALL MC \n", "MSL \n", "HN OVERALL \n", "MTN OKRA1 \n", "MTN WERAI \n", "h \n", "MC \n", "MSL \n", "HN OVERALL \n", ". MC \n", "MSL \n", "HN OVERALL \n", "2 Mean log(n+B/month \n", "UNTREATED \n", "MC \n", "MSL \n", "OVERALL \n", "G \n", "Mean loa~n+D/month \n", "UNTREATED \n", "MC \n", "MSL \n", "OVERALL \n", "H \n", ", . 4Mean log(n+P/month \n", "1 \n", "UNTREATED \n", "1.2 _.. \n", "I \n", ", _.. \n", "MC \n", "MSL \n", "OVERALL \n", "Fig. 1. Mean log (n+l) numbers of mosquitoes per month collected on human bait, 2 indoors overnight and 2 outdoors until midnight, in the villages (A- \n", "D) and farm huts (E-H) of <PERSON> (MH), <PERSON> (MTN), <PERSON> (MC), <PERSON> (MSL) and <PERSON><PERSON> (HN), before (May to December \n", "1990, solid bats) and after (May to December 1991, hatched bars) intervention: A and E show fgures for A. minimus species A, B and F for A. mwdwmg- \n", "porni, C and G for A. maculatw s.s., and D and H for A. dims s.1. Significaot differences (P~0.05, paired f test) are indicated by asterisks (‘). \n", "Downloaded from https://academic.oup.com/trstmh/article-abstract/89/3/248/1885449 by Columbia University user on 24 April 2020\n", "\n", "\n", "=== Page 4 ===\n", "251 \n", "Mosquito collection. CDC light-trap catches were used \n", "for daily collection of mosquitoes, hung beside occupied \n", "untreated bed nets (to avoid the deterrent and excito- \n", "repellent effects of the insecticide) in each of 3 houses in \n", "each community. They were connected with rechar- \n", "geable batteries in the evening and disconnected in the \n", "morning by the project team. This method has shown a \n", "fairly good correlation with indoor human-biting catches \n", "in sampling A. minimus species A, which was the most \n", "abundant indoor biting species (ISMAIL et al., 1982; SOM- \n", "BO<PERSON>, 1993). To reduce mortality of trapped mosquitoes \n", "caused by the wind from the trap’s fan, the necks of the \n", "collection bags were extended to 35.5 cm, and the top of \n", ",ooD Total number caught \n", "3 \n", "Mae <PERSON> \n", "TREATED \n", "’ \n", "UNTREATED \n", "1 !,,,‘,,,,,,,,,,,,,,,,,,,!,,,,,,,,,.,,,,,,,,,,,,I \n", "1 \n", "6 \n", "9 \n", "13 \n", "17 \n", "21 26 \n", "29 \n", "2 \n", "6 \n", "10 \n", "14 \n", "1 \n", "6 \n", "9 \n", "13 \n", "17 \n", "21 36 \n", "29 \n", "2 \n", "6 \n", "10 \n", "14 \n", "JULY \n", "AUQUST \n", "JULY \n", "AUQUST \n", "Total number caught \n", "1oooq \n", "I \n", "Wild Animal Conservation \n", "Centre \n", "I \n", "1 \n", "6 \n", "9 \n", "13 \n", "17 \n", "21 26 \n", "29 \n", "2 \n", "6 \n", "10 \n", "14 \n", "1 \n", "6 \n", "9 \n", "13 \n", "17 \n", "21 26 \n", "29 \n", "2 \n", "6 \n", "10 \n", "14 \n", "JULY \n", "AUGUST \n", "JULY \n", "AUGUST \n", "each bag was covered with a piece of wet cotton cloth. \n", "The mosquitoes collected from the 3 light traps in each \n", "community per night were pooled and identified to \n", "species. Most of them were dissected for parity. \n", "The daily counts were log-transformed to reduce the \n", "variance. Comparisons of the geometric means between \n", "the periods when treated and untreated nets were used in \n", "each community were carried out by using Student’s t \n", "tests. Analysis of variance (ANOVA) was carried out to \n", "partition variation into treatments and communities. The \n", "proportions of parous and nulliparous mos 9 uitoes be- \n", "tween the 2 periods were compared by using x tests. \n", "looo Total number caught \n", "1 \n", "Mae Top Klang \n", "100 \n", "ia ....... \n", "...... \n", "....... \n", "I \n", ". \n", "TREATED \n", "l \n", "UNTREATED \n", "I \n", "looo Total number caught \n", "<PERSON> \n", "Fig. 2. Numbers of A. minimus species A females collected daily by 3 CDC light traps hung beside occupied untreated bed nets in houses in the periods \n", "when both treated and untreated nets were installed in 4 communities. \n", "Table 2. Comparisons of the parous rates of A. minimus species A before (1990) and after (1991) intervention \n", "No. dissected \n", "1990 \n", "1991 \n", "Parous rate (% \n", "1990 \n", "3 \n", "1 91 \n", "Diffeenc \n", "a \n", "Y2a \n", ". \n", ", \n", "I” \n", "Un;tr~e$lllages \n", "57.1 \n", "-0.2 \n", "0.01 \n", "<PERSON><PERSON> \n", "Tr;~Cvi~;ges \n", "ErZ” \n", "1E \n", "3;: \n", "67.3 \n", "E \n", "231 \n", "3T; \n", "417 \n", "102 \n", "65.8 \n", "68.4 \n", "7019 \n", "53.9 \n", "+6.3 \n", "2.89 \n", "- +5.1 \n", "14.5 \n", "2.71 \n", "7.19* \n", "74.1 \n", "87.5 \n", "+13*4 \n", "0.10 \n", "180 \n", "29: \n", "62.8 \n", "**** \n", "0.63 \n", "514 \n", "404 \n", "66.7 \n", ";::: \n", "-0.1 \n", "0.76 \n", "Farm huts \n", "<PERSON> \n", "f$: ‘@WNua \n", "E&Z1ap \n", "527 \n", ":2 \n", "223 \n", "100 \n", "1663 \n", "2015 \n", "925 \n", "718 \n", "272 \n", "if; \n", "35.5 \n", "54.7 \n", "-17.1 \n", "**** \n", "30.10*** \n", "51:5 \n", "59.2 \n", "**** \n", ":i:; \n", "46.3 \n", "51.0 \n", "-4.2 \n", "-4.4 \n", "aPaired parous rates compared by Mantel-Haenszel X‘ tests, stratifying for corresponding mgnths; co~@sris;fs000f0verall \n", "parous rates were stratified by month and village. Significant differences are indicated thus: P<O.Ol, \n", ". \n", ". \n", "Downloaded from https://academic.oup.com/trstmh/article-abstract/89/3/248/1885449 by Columbia University user on 24 April 2020\n", "\n", "\n", "=== Page 5 ===\n", "252 \n", "Results \n", "The long-term evaluation \n", "Effect on mosquito densities. Mean monthly mosquito \n", "densities in 1990 and 1991 are shown by species in Fig. \n", "1. From 1990 to 1991, a statistically significant reduction \n", "in the biting density of A. minimus species A was ob- \n", "served in the village collection in Mae Chon, but this ef- \n", "fect was not seen in the other treated villages or in either \n", "untreated village. \n", "There was no statistically significant change in the vil- \n", "lage biting densities of <PERSON><PERSON> and <PERSON><PERSON>- \n", "latus S.S. m any of the treated villages; a significant in- \n", "crease in A. maculatus S.S. biting rate was observed in the \n", "untreated village Mae Top Nua. The density of A. dirus \n", "s.1. was significantly decreased in Mae Chon, but not \n", "elsewhere. \n", "When the numbers collected each month were pooled \n", "between villages in the treated and untreated groups, \n", "there was no significant change from 1990 to 1991 in \n", "either group. \n", "In the farm hut areas, where no intervention was car- \n", "ried out, there were statistically significant changes \n", "(mostly decreases) in the densities of each of the 4 species \n", "in some sites. However, the overall densities of only A. \n", "maculatus S.S. and <PERSON>. dirus s.1. were significantly reduced. \n", "Effect on parous rates. Changes in parous rates were \n", "broadly similar to the changes in density. The results are \n", "shown in detail only for A. minimus species A (Table 2); \n", "those for the other species were similar. Although the \n", "total numbers caught were reasonably large, the sample \n", "size of a given species in a given village was usually quite \n", "small, and few of the year-on-year comparisons were sig- \n", "nificant. Overall, positive changes in parous rate out- \n", ",oo Parous rate&) \n", "l- 5- \n", "Q- \n", "13- \n", "17- \n", "21- \n", "25- \n", "29- \n", "2- \n", "6- \n", "lo- \n", "14 \n", "1oo Parous rate (%I \n", "808 \n", "l- \n", "S- \n", "Q- \n", "13- \n", "17- \n", "21- \n", "25- \n", "29- \n", "2- \n", "e- \n", "IO- \n", "14 \n", "JULY \n", "AUGUST \n", "(1992) \n", "Fig. 3. Changes in the parous rate of A. minimus species A. Numbers of \n", "nulliparous and parous females collected in every four-night period were \n", "pooled. Broken lines show values when untreated bed nets were installed \n", "and the solid lines show those for treated bed nets. MTN=Mae Top Nua, \n", "MTK-Mae \n", "Top Klang, WAC=Wild Animal Conservation Centre, and \n", "MC=<PERSON>. \n", "numbered negative ones in treated as well as untreated \n", "villages. The only result that could possibly be inter- \n", "preted as evidence for an effect of the nets was seen with \n", "A. minimus species A in Mae Chon, where there was a \n", "significant decrease in both density and parous rate. No \n", "calculation was made for A. dirus s.1. collected in the vil- \n", "lages because of the very low numbers caught; at the \n", "farm huts, no significant change of the parous rate was \n", "observed. \n", "The short-term evaluation \n", "Bednet usage. Excellent community participation was \n", "observed in all 4 communities. All residents used nets, \n", "except for a couple in Mae Top Nua who had never slept \n", "under a net and refused to use it. The proportion of resi- \n", "dents who accepted the treated nets to replace their pre- \n", "vious untreated nets ranged from 93% to 98%, excluding \n", "those in houses where light traps were used. Washing of \n", "a treated net was not observed during the study period. \n", "Light trap catches. The numbers of A. minimus species \n", "A females collected each night from each community are \n", "shown in Fig. 2 and the patterns of the parous rates in \n", "Fig. 3. There were great fluctuations of both mosquito \n", "density and parous rate. Heavy rains followed by flood- \n", "ing occurred in the middle of the first period and at the \n", "end of July to early August. This seemed to be associated \n", "with a large reduction in the mosquito densities in most \n", "sites, which were observed 4-5 d after flooding. \n", "Table 3. Analysis of variance on the log-transformed \n", "numbers of A. minimus species A collected \n", "by CDC \n", "light-traps \n", "hung beside occupied untreated bed nets in \n", "the periods \n", "when both treated \n", "and untreated \n", "were \n", "installed \n", "Source of \n", "Sum of Degrees Mean \n", "variation \n", "squares of freedom square \n", "F \n", "P \n", "Community \n", "2.2334 \n", "3 \n", "0~0001 \n", "Treated net \n", "0.2855 \n", "1 \n", "yn \n", ". \n", "7.31 \n", "2.81 0.096 \n", "Community X \n", "treated net \n", "0.4678 \n", "3 \n", "0.1559 1.53 0.2 \n", "Residual \n", "Total \n", "18.7378 \n", ":i: \n", "0.1018 \n", "- \n", "- \n", "21.7245 \n", "- \n", "- \n", "- \n", "The ANOVA results (Table 3) showed that there was \n", "significant variation of A. minimus species A densities \n", "among the study communities. The effect of treated bed \n", "nets was of borderline significance (P=O*O96). During \n", "the second half of the study period? there was a declining \n", "trend in densities in 3 of the 4 villages, including 2 of \n", "those with treated nets. However, if this had been \n", "caused, at least in those 2 villages, by the intervention of \n", "treated nets, there should have been a corresponding de- \n", "cline in parous rates. In fact, there was a slight increasing \n", "trend, suggesting that the decline in density was related \n", "to a decline in recruitment. \n", "Discussion \n", "The 2 studies described here differed not only in their \n", "timing but also in the intensity of entomological monitor- \n", "ing. The long-term study, which covered 8 months of the \n", "year, yielded adequate numbers of <PERSON><PERSON> \n", "and A. maculatus S.S. as well as A. minimus species A, but \n", "suffered from the washing of about 40% of the nets by \n", "villagers after the first impregnation, and from the low \n", "rate of reimpregnation. These problems were probably \n", "associated with the net washing habit of villagers, side- \n", "effects of the insecticide and the timing of reimpregna- \n", "tion during the period of harvest, when most people were \n", "in the rice fields all day. The short-term study was more \n", "focused and did not suffer from the problems of net \n", "washing and low coverage. Washing nets is known to re- \n", "duce greatly the insecticidal activity of treated nets (MIL- \n", "<PERSON><PERSON> et al., 1991). \n", "When the samples collected in the long-term study \n", "Downloaded from https://academic.oup.com/trstmh/article-abstract/89/3/248/1885449 by Columbia University user on 24 April 2020\n", "\n", "\n", "=== Page 6 ===\n", "253 \n", "were compared between 1990 (pre-intervention) and \n", "1991 (post-intervention), for each species and village, a \n", "broadly similar pattern was seen in both densities and \n", "parous rates. The great majority of year-on-year compari- \n", "sons showed no significant change, and there was no hint \n", "that densities and parous rates in treated villages were \n", "more likely to decrease, or less likely to increase, than in \n", "untreated villages. Presumably, therefore, the significant \n", "changes that were seen were due to environmental factors \n", "rather than the intervention. \n", "The fact that there was no apparent impact of the nets \n", "on populations of <PERSON><PERSON> and <PERSON><PERSON> maculatus \n", "S.S. is not surprising. These 2 species are largely exo- \n", "phagic and zoophilic (UPATHAM \n", "et al., 1988), and would \n", "not be expected to be greatly affected, at the population \n", "level, by the introduction of treated nets, even in condi- \n", "tions of high coverage. Neither was it very surprising \n", "that there was no consistent evidence for an effect of \n", "treated nets on any species in the farm hut setting. Al- \n", "though some villagers did take their treated nets with \n", "them on visits to the farm huts, many did not, and the \n", "proportion of potential hosts under treated nets was al- \n", "ways much lower at the farm huts than in the villages. \n", "One might have expected greater impact on the more \n", "anthropophilic A. minimus species A and A. dirus s.l., but \n", "there was little evidence for this. The strongest evidence \n", "came from the village Mae Chon; in the long-term study, \n", "the densities and/or parous rat& of these-species we& \n", "significantly lower in 1991 than 1990. Was this because \n", "the nets did have an effect in Mae Chon, but for some \n", "reason did not do so in Mae Salap and Huai Ngu, or were \n", "the decreases in Mae Chon merely a reflection of local en- \n", "vironmental changes? There can be no conclusive answer \n", "to this question, but evidence in favour of the latter ex- \n", "planation, in the case of A. minimus species A, is avail- \n", "able from the short-term study. \n", "In the short-term study, densities of A. minimus \n", "soecies A in Mae Chon and in the Wild Animal Conser- \n", "vation Centre did decrease in the second half of the \n", "study, following the introduction of treated nets. How- \n", "ever, there are 3 reasons for believing that this was not \n", "due to the treated nets. Firstly, a very similar pattern was \n", "seen at the same time in Mae Top Nua, where treated \n", "nets had been in use during the first half of the study and \n", "had just been removed. Secondly, the decrease in density \n", "was accompanied by a slight increase in parous rates, as \n", "would be expected if it were due to a decrease in recruit- \n", "ment, and <he opposite of what would be expected if it \n", "were due to an increase in mortalitv. Thirdlv, the decline \n", "in numbers in both Mae Chon and-the Wild-Animal Con- \n", "servation Centre began 6-10 d after the introduction of \n", "treated nets. and continued for another 10-12 d. This is \n", "not the pat&n that would be expected if it were an effect \n", "of treated nets. Assuming that the parous rates observed \n", "in this study (50-70%) are a reasonable guide to rates of \n", "survival per gonotrophic cycle, few of the mosquitoes \n", "alive on a given day can be expected to remain alive one \n", "week later. Any killing effect by treated nets should \n", "therefore be manifest within a few days and be very close \n", "to its maximum within 10 d of intervention. \n", "For these reasons, we conclude that the treated nets \n", "did not have a mass killing effect on A. minimus species \n", "A. The results agree with-2 other studies in Tak-prov- \n", "ince. about 200 km south of Mae Sariana, bv C. <PERSON> \n", "(unpublished data) and M<PERSON> &ersonal com- \n", "munication) . \n", "The absence of a detectable mass killing effect of \n", "treated nets on A. minimus species A contrasts with the \n", "results of JANA-KARA \n", "et al. (in press) in Assam, India. \n", "This is presumably related to the biting behaviour of \n", "mosquitoes. A. minimus species A in Thailand is exo- \n", "phaglc, exophilic, and readily bites humans and animals \n", "ihr&ghoutihenight \n", "(ISMAIL et al., ~~~~;RATANATHAM \n", "et al.. 1988: P. <PERSON>. unoublished data). A. minimus \n", "in A&am, 6y contrast, is’endbphagic, endophilic and an- \n", "thropophilic with a biting peak after midnight (RAo, \n", "1984; JANA-KARA \n", "et al., in press); similar behaviour \n", "used to be observed in Thailand before extensive DDT \n", "house spraying. The morphological features of A. \n", "minimus in Assam (RAo. 1984) and <PERSON>. minimus soecies A \n", "in Thailand (SUCH~RIT \n", "et al., i988) are similar. - \n", "In addition, the existence of domestic animals in a \n", "human residential area can diminish the mosquito attack \n", "rate on humans (COLUZZI, 1984; BURKOT \n", "et al., 1989). \n", "In most Karen communities, cattle are very common and \n", "can provide alternative blood meals. They are normally \n", "kept under a shelter away from houses. Blood meal \n", "identification of blood-fed <PERSON>. minimus s ties A females \n", "collected by indoor light traps showed t r at about 50% of \n", "them (n=89) were from animal sources (P. <PERSON>, \n", "unpublished data). \n", "The numbers of A. dims s.2. were too low to permit a \n", "conclusive result. However, <PERSON><PERSON> (unpublished data) \n", "observed in a small trial in Tak province that treated nets \n", "had a significant impact on A. dirus species A, but not \n", "species D. Further investigation on this species group is \n", "needed. \n", "In conclusion, these results indicate that community- \n", "wide use of pyrethroid-treated nets does not enerally re- \n", "duce the vectorial capacity of populations o i: \n", "‘. \n", "<PERSON><PERSON> mtmmus \n", "species A and the A. macularus group in north-west Thai- \n", "land. This does not mean that treated nets are ineffective \n", "in protecting against malaria; an individual sleeping \n", "under a treated net should nevertheless gain good per- \n", "sonal protection against vector biting. However, the lack \n", "of a mass effect does mean that there is no protection for \n", "people outside nets. From the point of view of an indi- \n", "vidual who does sleep under a treated net, it does not \n", "matter whether or not others in the same community do \n", "so as well. From the public health point of view, it means \n", "that there is no advantage (in terms of efficacy) in orga- \n", "nizing the impregnation of nets on a community-by-com- \n", "munity basis, rather than household-by-household, \n", "which may often be easier in practice. \n", "Acknowledgements \n", "We are most grateful to MS <PERSON>, Mr <PERSON><PERSON> \n", "Wong<PERSON><PERSON><PERSON>, and their insect collection teams for collecting \n", "mosquitoes. Many thanks also to the staff of Mae Sariang <PERSON>- \n", "ria Sector and the residents of Mae Han, Mae Chon, Mae Top \n", "N<PERSON>, Mae Top Klang, Mae Salaq, Huiu <PERSON> and the Wdd Am- \n", "ma1 Conservation Centre for then warm co-operation. The in- \n", "secticide was supplied by Zeneca, UK, via <PERSON> <PERSON>. \n", "This study received financial support from the UNDP/World \n", "Bar&WHO Special Programme for Research and Training in \n", "Tropical Diseases and the Faculty of Medicine, Chiang Mai \n", "University. \n", "References \n", "<PERSON><PERSON><PERSON>, <PERSON><PERSON> (1993). Effectiveness of a lambda-cyhalothrin \n", "bednet impregnation againsi forestlbord& malaria in north west \n", "Thailand. PhD Thesis. Universitv of London. \n", "Beach, R. F., Ruebush; T. K., Sixton, J. D., Bright, P. L., \n", "Hightower, A. W., Breman, J. G., Mount, D. L. & 0100, A. \n", "<PERSON><PERSON> (1993). Effectiveness of permethrin-impregnated \n", "bed nets \n", "and currains for malaria control in a holoendemic area of \n", "western Kenya. American Journal of Tropical Medicine and \n", "Hygieq 49,290-300. \n", "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, J. <PERSON> & <PERSON>, I. (1984). Identifi- \n", "cation of Plasmodium falci arum-infected \n", "double antibody enzyme-lin i: \n", "mosquitoes by a \n", "ed immunosorbent assay. AmerG \n", "canyoumal of Tropical Medicine and Hygiene, 33,783-788. \n", "<PERSON><PERSON><PERSON>, T. R., <PERSON>, C. & Graves, P. M. (1989). An analysis of \n", "some factors determining the sporozoite rates, human blood \n", "indexes, and biting rates of members of the Anophelespunctu- \n", "latus corn lex in Pa ua New Guinea. American 3ournal of \n", "Tropical d- \n", "B \n", "e&me an Hygzene, 40,229-234. \n", "<PERSON><PERSON><PERSON>, J. D. & Graves, P. M. (1987). The effect of per- \n", "methrin-impregnatd bed nets on a population of A \n", "Yhelfsfa- \n", "rauti in coastal Papua New Guinea. Medical and \n", "etennary \n", "Entomology, 1,319-327. \n", "<PERSON><PERSON><PERSON>, J. D., <PERSON><PERSON><PERSON>, M. <PERSON>., Da<PERSON>, H., Paru, R. & \n", "Home, <PERSON><PERSON> <PERSON><PERSON> (1985). Assessing survival rates of Am&&es fa- \n", "rauti (fiiptera:‘ Culicidae) fromPapua New Guinea. journal of \n", "AnimalEcolop \n", "54,1003101$., \n", "<PERSON><PERSON><PERSON>, <PERSON><PERSON> (19 4). Heterogeneltles of the malaria vectorial sys- \n", "Downloaded from https://academic.oup.com/trstmh/article-abstract/89/3/248/1885449 by Columbia University user on 24 April 2020\n", "\n", "\n", "=== Page 7 ===\n", "254 \n", "tern in tropical Africa and their significance in malaria epi- \n", "demiology and control. Bulletin of the World Health Organiaa- \n", "tiun, 62, supplement, 107-113. \n", "<PERSON>, <PERSON><PERSON> <PERSON><PERSON> (1992). Personal protection methods against vec- \n", "tors of disease. Review of Medical and Veterinary Entomology, \n", "80,543-553. \n", "<PERSON>, C<PERSON>, Lines, J. D., Carnevale, P., <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, \n", "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, \n", "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, J. <PERSON>, Graves? P. <PERSON>, <PERSON><PERSON><PERSON>,, M. \n", "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON> ZUZ<PERSON>, <PERSON>, <PERSON><PERSON>, \n", "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, K. <PERSON>, <PERSON><PERSON>, T. J., \n", "<PERSON>, R. W. & <PERSON>, S. <PERSON>. (1990). Impregnated bed nets \n", "and curtains against malaria mosquitoes. In: Appropriate Tech- \n", "nology in Vector Control, Curtis, <PERSON><PERSON> (editor). Boca Raton, \n", "Florida: CRC Press. \n", "<PERSON>, <PERSON><PERSON> <PERSON><PERSON>, <PERSON>, L. <PERSON>, Parida, S. K., Patra, K. P. & Jambul- \n", "<PERSON><PERSON><PERSON>, <PERSON><PERSON> (1993). Lambdacyhalothrin treated bed nets as an \n", "alternative method of malaria control in tribal villages of Ko- \n", "raput District, Orissa State, India. Southeast AsianJournal \n", "of \n", "Tropical Medicine and Public Health? 24,5 13-52 1. \n", "<PERSON><PERSON><PERSON>, T. S. (1962). Age Grou m Methods in Di tera of \n", "Medical Importance: with Special f;f \n", "J \n", "e erence w Some ecwrs \n", "of \n", "Malaria. \n", "Geneva: World Health Organization, monograph \n", "series, no. 47. \n", "<PERSON>, P. M<PERSON>, <PERSON><PERSON>, B. J., Charlwood? J. D., Burkot, T. R., \n", "<PERSON><PERSON>, J. <PERSON>, <PERSON>, M<PERSON>, <PERSON>, J<PERSON>, <PERSON>, F. D. & Alpers, \n", "<PERSON><PERSON> <PERSON><PERSON> (1987). Reduction in Plusmodium falciparum incidence \n", "and prevalence in children under five by permethrin impreg- \n", "nation of mosquito nets. Bulletin of the World Health Organiz- \n", "ation,65,869-877. \n", "Gus&on, \n", "<PERSON><PERSON> <PERSON><PERSON> (1989). True EPrsTAThfand, \n", "3rd edition. Ri- \n", "chardson, Texas: Epistat. \n", "<PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON>, <PERSON>, L<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, S. K. P. <PERSON> \n", "T. <PERSON><PERSON> <PERSON>, R<PERSON> <PERSON><PERSON> (19937. Impact of permethr&npre& \n", "nated mosquito nets compared with DDT house-spraying \n", "against malaria transmission by <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> \n", "uric- \n", "tulatus in the Solomon Islands. Medical and Veterinary $ nto- \n", "mology, 7,333-338. \n", "<PERSON>, P. <PERSON> & <PERSON>, M. <PERSON> (1987). An improved method \n", "for survival rate analysis from time series of haematophagous \n", "dipteran populations.Journul ofAnimal Ecology, 56,427-440. \n", "<PERSON>, <PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON><PERSON>, V<PERSON> & <PERSON>, J<PERSON> (1974). Studies \n", "on malaria and responses of Anop Re les balabacensis bala- \n", "bace+s and <PERSON><PERSON><PERSON><PERSON> mini,mus to DDT residual spraying in \n", "~~~~s$. Part I. Pre-spraying observations. Acta Troprca, 31, \n", "<PERSON>, <PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, U<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, \n", "<PERSON><PERSON> <PERSON>, <PERSON><PERSON> (1982). Trials with CDC and Monks Wood \n", "light-traps for sampling malaria vectors in Thailand. Geneva: \n", "World \n", "Health \n", "Organization, mimeographed document \n", "WHONB(X32.864. \n", "<PERSON><PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, C. F. \n", "& <PERSON>, V. P. (in press). Deltamethrin impregnated bed \n", "nets against Anophilesmin&us \n", "transmitted maiariain Assam, \n", "India. 3amnal of Tropical Medicine and Hygiene. \n", "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, Z., <PERSON>, J. J. & \n", "<PERSON>, <PERSON><PERSON> (1993). Moustiquaires impregn&s contre le pa- \n", "ludisme au Zaire. Annules de la So&e! Belge de Mhdecine \n", "<PERSON><PERSON>, 73,37-53. \n", "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, A. <PERSON>. & <PERSON>, W. A. \n", "(1993). The effect of permethrin impregnated bednets on the \n", "incidence of Plasmodium falciparum in children of north Gua- \n", "dalcanal, Solomon Islands. Southeast AsianJournal \n", "of Tropical \n", "Medicine and Public Health, 24,130-137. \n", "<PERSON><PERSON><PERSON><PERSON>, S., <PERSON><PERSON>, S., <PERSON><PERSON>, K<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, \n", "<PERSON><PERSON> <PERSON>, <PERSON> (1991). Malaria situation in Thailand with \n", "special reference to forest related malaria. In: Forest Malaria \n", "in Southeast Asia. Proceedings of an Informal Consultative Meet- \n", "<PERSON>, <PERSON>, <PERSON><PERSON> & <PERSON>, <PERSON><PERSON> (editors). New \n", "Delhi: World Health Organization/Medical Research Council. \n", "<PERSON>, <PERSON><PERSON> <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, J. R. \n", "M<PERSON>, He<PERSON>way, J., <PERSON>, P. J., Shenton, F. C. & Green- \n", "<PERSON>, <PERSON><PERSON> <PERSON><PERSON> (1993a). A malaria control trial using insecticide- \n", "Tropical Medicine and Hygiene, 87, supplement 2,1%23. \n", "<PERSON>, <PERSON><PERSON> <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, J. R. \n", "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, J., <PERSON>, J. H., Shenton, F. C., \n", "<PERSON><PERSON><PERSON>, M. & Greenwood, B. M. (1993b). A malaria control \n", "trial using insecticide-treated bed nets and targeted chemo- \n", "prophylaxis in a rural area of The Gambia, West Africa. 7. \n", "Impact of permethrin-impregnated bed nets on malaria vec- \n", "tors. Transactions of the Royal Society of Tropical Medicine and \n", "Hygiene, 87, supplement 2,45-5 1. \n", "Lines, J. D., Myamba, J. & Curtis, C. F. (1987). Experimental \n", "hut trials of permethrin impregnated nets and curtains against \n", "malaria vectors in Tanzania. Medical and Vetetinaty Entm \n", "logy, 1,37-51. \n", "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, A. E. <PERSON>., Njun<PERSON>, K. \n", "<PERSON><PERSON>, My<PERSON>, J., Kivu<PERSON>, M. D. P., Hill., N., Lines, J. D. & \n", "<PERSON>, <PERSON><PERSON> <PERSON><PERSON> (1991). Trial of pyrethroid unpregnated bednets \n", "in an area of Tanzania holoendemic for malaria. Part 2. Ef- \n", "fects on the malaria vector population. Acta Tropica, 49, 97- \n", "10X \n", "_““. \n", "<PERSON>+ <PERSON><PERSON> (1986). Alternative methods of personal protection \n", "agarnst the vectors of malaria in lowland Papua New Guinea \n", "with em<PERSON><PERSON><PERSON> on the evaluation of permethrin-impregnated bed- \n", "<PERSON>. <PERSON><PERSON>, Simon <PERSON>. \n", "<PERSON>, <PERSON><PERSON> <PERSON><PERSON>, <PERSON>, <PERSON><PERSON> & <PERSON>, J. R. <PERSON> (1991). \n", "Experimental hut trials of bednets impregnated with synthetic \n", "pyrethroid or organophosphate insecticide for mosquito con- \n", "tr;127The \n", "Gambia. Medical and Veterinay <PERSON>gv, 5, \n", "<PERSON>, <PERSON><PERSON><PERSON><PERSON> (1984). <PERSON><PERSON> of India, revised edition. \n", "New Delhi: Malaria Research Centre, Indian Council of \n", "Medical Research. \n", "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, \n", "W., Teerasil \n", "Bionomics o P \n", ", <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, V. (1988). \n", "Anopheles minimus and its role in malaria trans- \n", "mission in Thailand. Southeast Asian 3ownal of Tropical Me- \n", "dicine and Public Health, 19,283-289. \n", "<PERSON>, <PERSON>, P<PERSON> (1991). Influence of deitamethrin \n", "treatment of bed nets on malaria transmission in the Kou val- \n", "ley, Burkina Faso. Bulletin of the World Health Organization. \n", "69,735-740. \n", "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON> (1989). Impregnated mosquito nets and cur- \n", "tains for self-protection and vector control. Tropical Diseases \n", "Bulletin, 86, supplement, I-41. \n", "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON>, <PERSON>, A. <PERSON>., Kere? N. & Gala, \n", "0. (1992). Seasonal abundance and biting behavlour ofAnop \n", "k<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> k<PERSON>ensis in Malaita Province, Solomon \n", "Islands, and a trial of permethrin impregnated bednets \n", "against malaria transmission. Medical and Veterinary Entomo- \n", "logy, 6,371-378. \n", "<PERSON><PERSON><PERSON>, <PERSON><PERSON> (1993). Forest malaria vectors in northwest Thailand \n", "and a trial of control with mrethroid-treated \n", "bednets. PhD \n", "The<PERSON>, Univeisity of London: * \n", "<PERSON><PERSON><PERSON>, S., Komalani<PERSON>ra, N., Lee<PERSON>t, S., Apiwathna- \n", "<PERSON>, <PERSON><PERSON>, S<PERSON> (1988). Population genetic \n", "studies on the Anopheles minimus complex in Thailand. South- \n", "east Asian Journal of Tropical Medicine and Public Health, 19, \n", "717-723. \n", "<PERSON><PERSON><PERSON><PERSON>, E. S., <PERSON><PERSON><PERSON><PERSON>, C., <PERSON><PERSON>, S., Green, C. A., \n", "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>ong<PERSON><PERSON>, \n", "A<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, R. <PERSON> (1988). \n", "Bionomics of Anopkeles macukztus complex and their role in \n", "malaria transmission in Thailand. Southeast Asian Journal of \n", "Tropical Medicine and Public Health, 19,259-269. \n", "WHO (1989). The use of impregnated bednets and other materials \n", "for vector-borne disease control. Geneva: World Health Organiz- \n", "ation, mimeo ra hed document WHONBC/89.981. \n", "<PERSON><PERSON><PERSON>, R. A., BE \n", "<PERSON>r <PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON>, <PERSON>, P. M. & Andre, R. G. \n", "(1987). Field evaluation of enzyme-linked immunosorbent as- \n", "says for Plasmodium falciparum and Plasmodium vivax sporo- \n", "zoites in mosquitoes (Diptera: Culicidae) from Papua New \n", "Guinea.<PERSON><PERSON><PERSON> of Medical Entomology, 24,433-437. \n", "treated bed nets and targeted chemoprophylaxis in a rural \n", "area of The Gambia, West Africa. 3. Entomological charac- \n", "teristics of the study area. Transactions of the Royal Society of \n", "Received 5 July \n", "1994; revised 12 December 1994; accepted \n", "fm publication \n", "13 December 1994 \n", "Downloaded from https://academic.oup.com/trstmh/article-abstract/89/3/248/1885449 by Columbia University user on 24 April 2020\n", "\n"]}]}, {"cell_type": "code", "execution_count": 14, "id": "7ef792d8", "metadata": {"id": "7ef792d8", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "444f6951-c193-44e3-b2a9-afb18aeecfd3"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["💾 Files saved:\n", "  📄 Detailed results: results/gpu_benchmark_20250703_055218/gpu_benchmark_results.csv\n", "  📊 Summary: results/gpu_benchmark_20250703_055218/gpu_benchmark_summary.csv\n", "  🖥️  System info: results/gpu_benchmark_20250703_055218/system_info.txt\n", "  📋 Latest results also copied to: ./results/latest_benchmark_*.csv\n"]}], "source": ["# Cell 7: Enhanced Metrics Calculation\n", "\n", "def calculate_enhanced_metrics(extractions):\n", "    \"\"\"Calculate enhanced comparison metrics with GPU performance data\"\"\"\n", "\n", "    results = []\n", "\n", "    for pdf_name, pdf_extractions in extractions.items():\n", "        if 'PyMuPDF' not in pdf_extractions:\n", "            continue\n", "\n", "        baseline_text = pdf_extractions['PyMuPDF']['text']\n", "        baseline_scientific = analyze_scientific_content(baseline_text)\n", "\n", "        for system_name, extraction in pdf_extractions.items():\n", "            if system_name == 'PyMuPDF':\n", "                continue  # Skip baseline comparison with itself\n", "\n", "            if extraction['metadata']['status'] != 'success':\n", "                continue\n", "\n", "            # Text comparison metrics\n", "            text_metrics = calculate_text_metrics(baseline_text, extraction['text'])\n", "\n", "            # Scientific content analysis\n", "            scientific_metrics = analyze_scientific_content(extraction['text'])\n", "\n", "            result = {\n", "                'PDF': pdf_name,\n", "                'System': system_name,\n", "                'Character_Accuracy': text_metrics['character_accuracy'],\n", "                'Word_Accuracy': text_metrics['word_accuracy'],\n", "                'Length_Ratio': text_metrics['length_ratio'],\n", "                'Word_Count_Ratio': text_metrics['word_count_ratio'],\n", "                'Processing_Time': extraction['metadata']['processing_time'],\n", "                'Text_Length': len(extraction['text']),\n", "                'Device': extraction['metadata'].get('device', 'unknown'),\n", "                'GPU_Memory_Used': extraction['metadata'].get('gpu_memory_used', 0),\n", "                'Equations_Found': scientific_metrics['equations_count'],\n", "                'Citations_Found': scientific_metrics['citations_count'],\n", "                'Figures_Found': scientific_metrics['figures_count'],\n", "                'Tables_Found': scientific_metrics['tables_count'],\n", "                'Formulas_Found': scientific_metrics['formulas_count'],\n", "                'Scientific_Elements_Total': scientific_metrics['total_scientific_elements'],\n", "                'Status': extraction['metadata']['status']\n", "            }\n", "\n", "            results.append(result)\n", "\n", "    return pd.DataFrame(results)\n", "\n", "# Calculate enhanced metrics\n", "if extractions:\n", "    results_df = calculate_enhanced_metrics(extractions)\n", "\n", "    # Save results\n", "    results_file = output_dir / 'gpu_benchmark_results.csv'\n", "    results_df.to_csv(results_file, index=False)\n", "\n", "    # Create enhanced summary with GPU metrics\n", "    summary_df = results_df.groupby('System').agg({\n", "        'Character_Accuracy': ['mean', 'std'],\n", "        'Word_Accuracy': ['mean', 'std'],\n", "        'Processing_Time': ['mean', 'std'],\n", "        'Text_Length': 'mean',\n", "        'GPU_Memory_Used': 'mean',\n", "        'Scientific_Elements_Total': 'mean'\n", "    }).round(3)\n", "\n", "    summary_file = output_dir / 'gpu_benchmark_summary.csv'\n", "    summary_df.to_csv(summary_file)\n", "\n", "    # Copy latest results to main results folder\n", "    import shutil\n", "    main_results_dir = Path(\"./results\")\n", "    shutil.copy2(results_file, main_results_dir / \"latest_benchmark_results.csv\")\n", "    shutil.copy2(summary_file, main_results_dir / \"latest_benchmark_summary.csv\")\n", "\n", "    print(\"💾 Files saved:\")\n", "    print(f\"  📄 Detailed results: {results_file}\")\n", "    print(f\"  📊 Summary: {summary_file}\")\n", "    print(f\"  🖥️  System info: {output_dir / 'system_info.txt'}\")\n", "    print(f\"  📋 Latest results also copied to: ./results/latest_benchmark_*.csv\")"]}, {"cell_type": "code", "execution_count": 15, "id": "447ad1ea", "metadata": {"id": "447ad1ea", "colab": {"base_uri": "https://localhost:8080/", "height": 1000}, "outputId": "3fb27a2a-f85d-4ff1-ee84-665803e584a3"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["📊 GPU-OPTIMIZED BENCHMARK RESULTS\n", "======================================================================\n"]}, {"output_type": "display_data", "data": {"text/plain": ["                                                 PDF   System  \\\n", "0                        Allossogbe_et_al_2017_<PERSON>_J  <PERSON>   \n", "1                        Allossogbe_et_al_2017_<PERSON>_<PERSON>   \n", "2                     Somboon_et_al_1995_Trans_RSTMH  Docling   \n", "3                     Somboon_et_al_1995_Trans_RSTMH   Marker   \n", "4  2014-Combining_organophosphate_treated_wall_li...  Docling   \n", "5  2014-Combining_organophosphate_treated_wall_li...   <PERSON><PERSON>   \n", "\n", "   Character_Accuracy  Word_Accuracy  Length_Ratio  Word_Count_Ratio  \\\n", "0            0.818706       0.701115      1.147287          1.047733   \n", "1            0.777607       0.697241      1.182501          1.036187   \n", "2            0.775622       0.720273      1.046468          0.968109   \n", "3            0.868682       0.809567      1.001018          0.968717   \n", "4            0.844456       0.755238      1.175448          1.069417   \n", "5            0.846334       0.783034      1.146879          1.050686   \n", "\n", "   Processing_Time  Text_Length Device  GPU_Memory_Used  Equations_Found  \\\n", "0        35.123128        53171   cuda         2.965459                0   \n", "1         8.580534        54803   cuda         2.987022               11   \n", "2        82.261602        43194   cuda         2.987022                3   \n", "3         6.155208        41318   cuda         2.989021                3   \n", "4        19.289043        61550   cuda         2.989021                0   \n", "5         8.186339        60054   cuda         3.006288                6   \n", "\n", "   Citations_Found  Figures_Found  Tables_Found  Formulas_Found  \\\n", "0               32             12            14               0   \n", "1               39              0             3               1   \n", "2                7              6             7               5   \n", "3                4              5             7               5   \n", "4               31             12            21              11   \n", "5               29             12            21              11   \n", "\n", "   Scientific_Elements_Total   Status  \n", "0                         58  success  \n", "1                         54  success  \n", "2                         28  success  \n", "3                         24  success  \n", "4                         75  success  \n", "5                         79  success  "], "text/html": ["\n", "  <div id=\"df-ba001c24-cf0b-41ac-96e7-fbe421be19f4\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>PDF</th>\n", "      <th>System</th>\n", "      <th>Character_Accuracy</th>\n", "      <th>Word_Accuracy</th>\n", "      <th>Length_Ratio</th>\n", "      <th>Word_Count_Ratio</th>\n", "      <th>Processing_Time</th>\n", "      <th>Text_Length</th>\n", "      <th>Device</th>\n", "      <th>GPU_Memory_Used</th>\n", "      <th>Equations_Found</th>\n", "      <th>Citations_Found</th>\n", "      <th>Figures_Found</th>\n", "      <th>Tables_Found</th>\n", "      <th>Formulas_Found</th>\n", "      <th>Scientific_Elements_Total</th>\n", "      <th>Status</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Allossogbe_et_al_2017_Mal_J</td>\n", "      <td>Docling</td>\n", "      <td>0.818706</td>\n", "      <td>0.701115</td>\n", "      <td>1.147287</td>\n", "      <td>1.047733</td>\n", "      <td>35.123128</td>\n", "      <td>53171</td>\n", "      <td>cuda</td>\n", "      <td>2.965459</td>\n", "      <td>0</td>\n", "      <td>32</td>\n", "      <td>12</td>\n", "      <td>14</td>\n", "      <td>0</td>\n", "      <td>58</td>\n", "      <td>success</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Allossogbe_et_al_2017_Mal_J</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>0.777607</td>\n", "      <td>0.697241</td>\n", "      <td>1.182501</td>\n", "      <td>1.036187</td>\n", "      <td>8.580534</td>\n", "      <td>54803</td>\n", "      <td>cuda</td>\n", "      <td>2.987022</td>\n", "      <td>11</td>\n", "      <td>39</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>1</td>\n", "      <td>54</td>\n", "      <td>success</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Somboon_et_al_1995_Trans_RSTMH</td>\n", "      <td>Docling</td>\n", "      <td>0.775622</td>\n", "      <td>0.720273</td>\n", "      <td>1.046468</td>\n", "      <td>0.968109</td>\n", "      <td>82.261602</td>\n", "      <td>43194</td>\n", "      <td>cuda</td>\n", "      <td>2.987022</td>\n", "      <td>3</td>\n", "      <td>7</td>\n", "      <td>6</td>\n", "      <td>7</td>\n", "      <td>5</td>\n", "      <td>28</td>\n", "      <td>success</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Somboon_et_al_1995_Trans_RSTMH</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>0.868682</td>\n", "      <td>0.809567</td>\n", "      <td>1.001018</td>\n", "      <td>0.968717</td>\n", "      <td>6.155208</td>\n", "      <td>41318</td>\n", "      <td>cuda</td>\n", "      <td>2.989021</td>\n", "      <td>3</td>\n", "      <td>4</td>\n", "      <td>5</td>\n", "      <td>7</td>\n", "      <td>5</td>\n", "      <td>24</td>\n", "      <td>success</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2014-Combining_organophosphate_treated_wall_li...</td>\n", "      <td>Docling</td>\n", "      <td>0.844456</td>\n", "      <td>0.755238</td>\n", "      <td>1.175448</td>\n", "      <td>1.069417</td>\n", "      <td>19.289043</td>\n", "      <td>61550</td>\n", "      <td>cuda</td>\n", "      <td>2.989021</td>\n", "      <td>0</td>\n", "      <td>31</td>\n", "      <td>12</td>\n", "      <td>21</td>\n", "      <td>11</td>\n", "      <td>75</td>\n", "      <td>success</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>2014-Combining_organophosphate_treated_wall_li...</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>0.846334</td>\n", "      <td>0.783034</td>\n", "      <td>1.146879</td>\n", "      <td>1.050686</td>\n", "      <td>8.186339</td>\n", "      <td>60054</td>\n", "      <td>cuda</td>\n", "      <td>3.006288</td>\n", "      <td>6</td>\n", "      <td>29</td>\n", "      <td>12</td>\n", "      <td>21</td>\n", "      <td>11</td>\n", "      <td>79</td>\n", "      <td>success</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-ba001c24-cf0b-41ac-96e7-fbe421be19f4')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-ba001c24-cf0b-41ac-96e7-fbe421be19f4 button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-ba001c24-cf0b-41ac-96e7-fbe421be19f4');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "    <div id=\"df-d9feff68-9464-4da0-9696-05ac4ec585a2\">\n", "      <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-d9feff68-9464-4da0-9696-05ac4ec585a2')\"\n", "                title=\"Suggest charts\"\n", "                style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "      </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "      <script>\n", "        async function quickchart(key) {\n", "          const quickchartButtonEl =\n", "            document.querySelector('#' + key + ' button');\n", "          quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "          quickchartButtonEl.classList.add('colab-df-spinner');\n", "          try {\n", "            const charts = await google.colab.kernel.invokeFunction(\n", "                'suggest<PERSON><PERSON>s', [key], {});\n", "          } catch (error) {\n", "            console.error('Error during call to suggest<PERSON>harts:', error);\n", "          }\n", "          quickchartButtonEl.classList.remove('colab-df-spinner');\n", "          quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "        }\n", "        (() => {\n", "          let quickchartButtonEl =\n", "            document.querySelector('#df-d9feff68-9464-4da0-9696-05ac4ec585a2 button');\n", "          quickchartButtonEl.style.display =\n", "            google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "        })();\n", "      </script>\n", "    </div>\n", "\n", "  <div id=\"id_ffac1280-5570-4843-8b0b-1ec634bac66f\">\n", "    <style>\n", "      .colab-df-generate {\n", "        background-color: #E8F0FE;\n", "        border: none;\n", "        border-radius: 50%;\n", "        cursor: pointer;\n", "        display: none;\n", "        fill: #1967D2;\n", "        height: 32px;\n", "        padding: 0 0 0 0;\n", "        width: 32px;\n", "      }\n", "\n", "      .colab-df-generate:hover {\n", "        background-color: #E2EBFA;\n", "        box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "        fill: #174EA6;\n", "      }\n", "\n", "      [theme=dark] .colab-df-generate {\n", "        background-color: #3B4455;\n", "        fill: #D2E3FC;\n", "      }\n", "\n", "      [theme=dark] .colab-df-generate:hover {\n", "        background-color: #434B5C;\n", "        box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "        filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "        fill: #FFFFFF;\n", "      }\n", "    </style>\n", "    <button class=\"colab-df-generate\" onclick=\"generateWithVariable('results_df')\"\n", "            title=\"Generate code using this dataframe.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "       width=\"24px\">\n", "    <path d=\"M7,19H8.4L18.45,9,17,7.55,7,17.6ZM5,21V16.75L18.45,3.32a2,2,0,0,1,2.83,0l1.4,1.43a1.91,1.91,0,0,1,.58,1.4,1.91,1.91,0,0,1-.58,1.4L9.25,21ZM18.45,9,17,7.55Zm-12,3A5.31,5.31,0,0,0,4.9,8.1,5.31,5.31,0,0,0,1,6.5,5.31,5.31,0,0,0,4.9,4.9,5.31,5.31,0,0,0,6.5,1,5.31,5.31,0,0,0,8.1,4.9,5.31,5.31,0,0,0,12,6.5,5.46,5.46,0,0,0,6.5,12Z\"/>\n", "  </svg>\n", "    </button>\n", "    <script>\n", "      (() => {\n", "      const buttonEl =\n", "        document.querySelector('#id_ffac1280-5570-4843-8b0b-1ec634bac66f button.colab-df-generate');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      buttonEl.onclick = () => {\n", "        google.colab.notebook.generateWithVariable('results_df');\n", "      }\n", "      })();\n", "    </script>\n", "  </div>\n", "\n", "    </div>\n", "  </div>\n"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "dataframe", "variable_name": "results_df", "summary": "{\n  \"name\": \"results_df\",\n  \"rows\": 6,\n  \"fields\": [\n    {\n      \"column\": \"PDF\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 3,\n        \"samples\": [\n          \"Allossogbe_et_al_2017_Mal_J\",\n          \"Somboon_et_al_1995_Trans_RSTMH\",\n          \"2014-Combining_organophosphate_treated_wall_linings_and_long-lasting_insecticidal_nets_for_improved_control_of_pyrethroi\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"System\",\n      \"properties\": {\n        \"dtype\": \"category\",\n        \"num_unique_values\": 2,\n        \"samples\": [\n          \"Marker\",\n          \"Docling\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Character_Accuracy\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.03849167457444716,\n        \"min\": 0.7756218905472637,\n        \"max\": 0.868681592039801,\n        \"num_unique_values\": 6,\n        \"samples\": [\n          0.8187060266368311,\n          0.7776068652849741\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Word_Accuracy\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.045905111408599646,\n        \"min\": 0.6972414730262264,\n        \"max\": 0.8095671981776765,\n        \"num_unique_values\": 6,\n        \"samples\": [\n          0.7011154414729204,\n          0.6972414730262264\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Length_Ratio\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.07475606635460579,\n        \"min\": 1.0010175404593469,\n        \"max\": 1.1825008091487754,\n        \"num_unique_values\": 6,\n        \"samples\": [\n          1.1472866544395297,\n          1.1825008091487754\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Word_Count_Ratio\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.043965832013534,\n        \"min\": 0.9681093394077449,\n        \"max\": 1.0694172380019589,\n        \"num_unique_values\": 6,\n        \"samples\": [\n          1.0477330329484653,\n          1.0361869895804\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Processing_Time\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 29.346418228702408,\n        \"min\": 6.155208110809326,\n        \"max\": 82.26160192489624,\n        \"num_unique_values\": 6,\n        \"samples\": [\n          35.123127698898315,\n          8.580533981323242\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Text_Length\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 8440,\n        \"min\": 41318,\n        \"max\": 61550,\n        \"num_unique_values\": 6,\n        \"samples\": [\n          53171,\n          54803\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Device\",\n      \"properties\": {\n        \"dtype\": \"category\",\n        \"num_unique_values\": 1,\n        \"samples\": [\n          \"cuda\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"GPU_Memory_Used\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.01298961094538948,\n        \"min\": 2.965458944,\n        \"max\": 3.006287872,\n        \"num_unique_values\": 4,\n        \"samples\": [\n          2.987022336\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Equations_Found\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 4,\n        \"min\": 0,\n        \"max\": 11,\n        \"num_unique_values\": 4,\n        \"samples\": [\n          11\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Citations_Found\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 14,\n        \"min\": 4,\n        \"max\": 39,\n        \"num_unique_values\": 6,\n        \"samples\": [\n          32\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Figures_Found\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 4,\n        \"min\": 0,\n        \"max\": 12,\n        \"num_unique_values\": 4,\n        \"samples\": [\n          0\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Tables_Found\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 7,\n        \"min\": 3,\n        \"max\": 21,\n        \"num_unique_values\": 4,\n        \"samples\": [\n          3\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Formulas_Found\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 4,\n        \"min\": 0,\n        \"max\": 11,\n        \"num_unique_values\": 4,\n        \"samples\": [\n          1\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Scientific_Elements_Total\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 23,\n        \"min\": 24,\n        \"max\": 79,\n        \"num_unique_values\": 6,\n        \"samples\": [\n          58\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Status\",\n      \"properties\": {\n        \"dtype\": \"category\",\n        \"num_unique_values\": 1,\n        \"samples\": [\n          \"success\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    }\n  ]\n}"}}, "metadata": {}}, {"output_type": "stream", "name": "stdout", "text": ["\n", "📈 SUMMARY STATISTICS\n", "======================================================================\n"]}, {"output_type": "display_data", "data": {"text/plain": ["        Character_Accuracy        Word_Accuracy        Processing_Time  \\\n", "                      mean    std          mean    std            mean   \n", "System                                                                   \n", "Docling              0.813  0.035         0.726  0.027          45.558   \n", "Marker               0.831  0.047         0.763  0.059           7.641   \n", "\n", "                Text_Length GPU_Memory_Used Scientific_Elements_Total  \n", "            std        mean            mean                      mean  \n", "System                                                                 \n", "Docling  32.757   52638.333           2.981                    53.667  \n", "Marker    1.301   52058.333           2.994                    52.333  "], "text/html": ["\n", "  <div id=\"df-f85c7264-4756-465c-9297-3d55c1e31b9d\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead tr th {\n", "        text-align: left;\n", "    }\n", "\n", "    .dataframe thead tr:last-of-type th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr>\n", "      <th></th>\n", "      <th colspan=\"2\" halign=\"left\">Character_Accuracy</th>\n", "      <th colspan=\"2\" halign=\"left\">Word_Accuracy</th>\n", "      <th colspan=\"2\" halign=\"left\">Processing_Time</th>\n", "      <th>Text_Length</th>\n", "      <th>GPU_Memory_Used</th>\n", "      <th>Scientific_Elements_Total</th>\n", "    </tr>\n", "    <tr>\n", "      <th></th>\n", "      <th>mean</th>\n", "      <th>std</th>\n", "      <th>mean</th>\n", "      <th>std</th>\n", "      <th>mean</th>\n", "      <th>std</th>\n", "      <th>mean</th>\n", "      <th>mean</th>\n", "      <th>mean</th>\n", "    </tr>\n", "    <tr>\n", "      <th>System</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th><PERSON><PERSON></th>\n", "      <td>0.813</td>\n", "      <td>0.035</td>\n", "      <td>0.726</td>\n", "      <td>0.027</td>\n", "      <td>45.558</td>\n", "      <td>32.757</td>\n", "      <td>52638.333</td>\n", "      <td>2.981</td>\n", "      <td>53.667</td>\n", "    </tr>\n", "    <tr>\n", "      <th><PERSON>er</th>\n", "      <td>0.831</td>\n", "      <td>0.047</td>\n", "      <td>0.763</td>\n", "      <td>0.059</td>\n", "      <td>7.641</td>\n", "      <td>1.301</td>\n", "      <td>52058.333</td>\n", "      <td>2.994</td>\n", "      <td>52.333</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-f85c7264-4756-465c-9297-3d55c1e31b9d')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-f85c7264-4756-465c-9297-3d55c1e31b9d button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-f85c7264-4756-465c-9297-3d55c1e31b9d');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "    <div id=\"df-557bf307-26f9-4bac-958c-3a6830e41a5d\">\n", "      <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-557bf307-26f9-4bac-958c-3a6830e41a5d')\"\n", "                title=\"Suggest charts\"\n", "                style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "      </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "      <script>\n", "        async function quickchart(key) {\n", "          const quickchartButtonEl =\n", "            document.querySelector('#' + key + ' button');\n", "          quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "          quickchartButtonEl.classList.add('colab-df-spinner');\n", "          try {\n", "            const charts = await google.colab.kernel.invokeFunction(\n", "                'suggest<PERSON><PERSON>s', [key], {});\n", "          } catch (error) {\n", "            console.error('Error during call to suggest<PERSON>harts:', error);\n", "          }\n", "          quickchartButtonEl.classList.remove('colab-df-spinner');\n", "          quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "        }\n", "        (() => {\n", "          let quickchartButtonEl =\n", "            document.querySelector('#df-557bf307-26f9-4bac-958c-3a6830e41a5d button');\n", "          quickchartButtonEl.style.display =\n", "            google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "        })();\n", "      </script>\n", "    </div>\n", "\n", "  <div id=\"id_4deec18a-ae1b-4d00-9800-a08531cc9ec8\">\n", "    <style>\n", "      .colab-df-generate {\n", "        background-color: #E8F0FE;\n", "        border: none;\n", "        border-radius: 50%;\n", "        cursor: pointer;\n", "        display: none;\n", "        fill: #1967D2;\n", "        height: 32px;\n", "        padding: 0 0 0 0;\n", "        width: 32px;\n", "      }\n", "\n", "      .colab-df-generate:hover {\n", "        background-color: #E2EBFA;\n", "        box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "        fill: #174EA6;\n", "      }\n", "\n", "      [theme=dark] .colab-df-generate {\n", "        background-color: #3B4455;\n", "        fill: #D2E3FC;\n", "      }\n", "\n", "      [theme=dark] .colab-df-generate:hover {\n", "        background-color: #434B5C;\n", "        box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "        filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "        fill: #FFFFFF;\n", "      }\n", "    </style>\n", "    <button class=\"colab-df-generate\" onclick=\"generateWithVariable('summary_df')\"\n", "            title=\"Generate code using this dataframe.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "       width=\"24px\">\n", "    <path d=\"M7,19H8.4L18.45,9,17,7.55,7,17.6ZM5,21V16.75L18.45,3.32a2,2,0,0,1,2.83,0l1.4,1.43a1.91,1.91,0,0,1,.58,1.4,1.91,1.91,0,0,1-.58,1.4L9.25,21ZM18.45,9,17,7.55Zm-12,3A5.31,5.31,0,0,0,4.9,8.1,5.31,5.31,0,0,0,1,6.5,5.31,5.31,0,0,0,4.9,4.9,5.31,5.31,0,0,0,6.5,1,5.31,5.31,0,0,0,8.1,4.9,5.31,5.31,0,0,0,12,6.5,5.46,5.46,0,0,0,6.5,12Z\"/>\n", "  </svg>\n", "    </button>\n", "    <script>\n", "      (() => {\n", "      const buttonEl =\n", "        document.querySelector('#id_4deec18a-ae1b-4d00-9800-a08531cc9ec8 button.colab-df-generate');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      buttonEl.onclick = () => {\n", "        google.colab.notebook.generateWithVariable('summary_df');\n", "      }\n", "      })();\n", "    </script>\n", "  </div>\n", "\n", "    </div>\n", "  </div>\n"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "dataframe", "variable_name": "summary_df", "summary": "{\n  \"name\": \"summary_df\",\n  \"rows\": 2,\n  \"fields\": [\n    {\n      \"column\": [\n        \"System\",\n        \"\"\n      ],\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 2,\n        \"samples\": [\n          \"Marker\",\n          \"Docling\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": [\n        \"Character_Accuracy\",\n        \"mean\"\n      ],\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.012727922061357866,\n        \"min\": 0.813,\n        \"max\": 0.831,\n        \"num_unique_values\": 2,\n        \"samples\": [\n          0.831,\n          0.813\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": [\n        \"Character_Accuracy\",\n        \"std\"\n      ],\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.008485281374238568,\n        \"min\": 0.035,\n        \"max\": 0.047,\n        \"num_unique_values\": 2,\n        \"samples\": [\n          0.047,\n          0.035\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": [\n        \"Word_Accuracy\",\n        \"mean\"\n      ],\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.02616295090390228,\n        \"min\": 0.726,\n        \"max\": 0.763,\n        \"num_unique_values\": 2,\n        \"samples\": [\n          0.763,\n          0.726\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": [\n        \"Word_Accuracy\",\n        \"std\"\n      ],\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.022627416997969517,\n        \"min\": 0.027,\n        \"max\": 0.059,\n        \"num_unique_values\": 2,\n        \"samples\": [\n          0.059,\n          0.027\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": [\n        \"Processing_Time\",\n        \"mean\"\n      ],\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 26.811367822250322,\n        \"min\": 7.641,\n        \"max\": 45.558,\n        \"num_unique_values\": 2,\n        \"samples\": [\n          7.641,\n          45.558\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": [\n        \"Processing_Time\",\n        \"std\"\n      ],\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 22.242750909004037,\n        \"min\": 1.301,\n        \"max\": 32.757,\n        \"num_unique_values\": 2,\n        \"samples\": [\n          1.301,\n          32.757\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": [\n        \"Text_Length\",\n        \"mean\"\n      ],\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 410.1219330881976,\n        \"min\": 52058.333,\n        \"max\": 52638.333,\n        \"num_unique_values\": 2,\n        \"samples\": [\n          52058.333,\n          52638.333\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": [\n        \"GPU_Memory_Used\",\n        \"mean\"\n      ],\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.009192388155425361,\n        \"min\": 2.981,\n        \"max\": 2.994,\n        \"num_unique_values\": 2,\n        \"samples\": [\n          2.994,\n          2.981\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": [\n        \"Scientific_Elements_Total\",\n        \"mean\"\n      ],\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.9432804461028567,\n        \"min\": 52.333,\n        \"max\": 53.667,\n        \"num_unique_values\": 2,\n        \"samples\": [\n          52.333,\n          53.667\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    }\n  ]\n}"}}, "metadata": {}}, {"output_type": "stream", "name": "stderr", "text": ["/tmp/ipython-input-15-3235018623.py:41: UserWarning: Glyph 128640 (\\N{ROCKET}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/tmp/ipython-input-15-3235018623.py:41: UserWarning: Glyph 128221 (\\N{MEMO}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/tmp/ipython-input-15-3235018623.py:41: UserWarning: Glyph 128293 (\\N{FIRE}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/tmp/ipython-input-15-3235018623.py:41: UserWarning: Glyph 128300 (\\N{MICROSCOPE}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/usr/local/lib/python3.11/dist-packages/IPython/core/pylabtools.py:151: UserWarning: Glyph 128640 (\\N{ROCKET}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/usr/local/lib/python3.11/dist-packages/IPython/core/pylabtools.py:151: UserWarning: Glyph 128221 (\\N{MEMO}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/usr/local/lib/python3.11/dist-packages/IPython/core/pylabtools.py:151: UserWarning: Glyph 128293 (\\N{FIRE}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/usr/local/lib/python3.11/dist-packages/IPython/core/pylabtools.py:151: UserWarning: Glyph 128300 (\\N{MICROSCOPE}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n"]}, {"output_type": "display_data", "data": {"text/plain": ["<Figure size 1500x1200 with 4 Axes>"], "image/png": "iVBORw0KGgoAAAANSUhEUgAABdEAAASlCAYAAABHkZBpAAAAOnRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjEwLjAsIGh0dHBzOi8vbWF0cGxvdGxpYi5vcmcvlHJYcgAAAAlwSFlzAAAPYQAAD2EBqD+naQAA9ttJREFUeJzs3Xl8TPf+x/H3hGQSicSe2Hdip6ERe4mmai2lVG9spa1dSttU7UvQFnVtvarUtdaut7fUXm2Dooral4oiCUVCkJCc3x+95meaHFKGiXg9H495PDrf8z3f8zlH1Pe8c+Y7FsMwDAEAAAAAAAAAgFRcnF0AAAAAAAAAAAAZFSE6AAAAAAAAAAAmCNEBAAAAAAAAADBBiA4AAAAAAAAAgAlCdAAAAAAAAAAATBCiAwAAAAAAAABgghAdAAAAAAAAAAAThOgAAAAAAAAAAJggRAcAAAAAAAAAwAQhOgA8hAYNGqhBgwbOLsPUb7/9JovForlz5zq7FJtixYqpWbNmzi4DAAAg02jQoIEqVqzo7DLwFLFYLOrdu7ezywCAx4YQHcBDuRPS3nllyZJFRYoU0UsvvaS9e/c6u7xMafjw4XbX3OyVkcP9x+WPP/7QoEGDVLZsWbm7uytXrlwKCQnRf/7zH9N94uPjNWLECFWpUkVeXl7y8PBQxYoV9e677+rcuXO2fp07d7a73larVWXKlNHQoUN18+bNdNWXkpKiefPmKTAwULly5VL27NlVpkwZhYaGavv27Q99/mlZuHChJk+e/EjGBgAAjpXeecmTbOzYsVq1apVTjn3lyhW5u7vLYrHo0KFDTqkB9xcVFaU333xTxYoVk9VqVb58+dSqVSv98MMPpvvExMRo4MCB8vf3V7Zs2eTp6amAgACNHj1aV65csfVr0KCB3Zzew8NDlStX1uTJk5WSkpKu+pKSkvTJJ5+oWrVq8vb2Vo4cOVShQgX16NFDhw8fftjTT9P06dMz1INSwNMgq7MLAOBcv/76q6pVqyY3N7c0tyclJenQoUMqWbLkPcfp0KGDXnzxRSUnJ+vQoUOaMWOGvvnmG23fvl1Vq1Z9BJVnDN9+++1jP2br1q1VqlQp2/tr167prbfe0ksvvaTWrVvb2n19fVW0aFHduHFDrq6uj71OZzty5IgaNWqkCxcuqEuXLqpevbquXLmiBQsWqHnz5ho4cKA+/PBDu31Onjyp4OBgRUVFqW3bturRo4fc3Ny0b98+zZ49WytXrtTRo0dt/a1Wqz777DNJUlxcnFavXq1Ro0bpxIkTWrBgwX1r7Nu3r6ZNm6aWLVuqY8eOypo1q44cOaJvvvlGJUqUUM2aNR17UfRniH7gwAH179/f4WMDAIA/OWKO/XfnJU+qsWPH6uWXX1arVq0e+7GXLl0qi8UiPz8/LViwQKNHj37sNeDefvjhB7344ouSpNdff13ly5dXdHS05s6dq7p16+qTTz5Rnz597Pb56aef9OKLL+ratWt67bXXFBAQIEnatWuXxo0bp++++87uPq5QoUKKiIiQJF28eFELFy7UgAEDdOHCBY0ZM+a+NbZp00bffPONOnTooO7du+vWrVs6fPiw/vOf/6hWrVry9/d31OWwmT59uvLkyaPOnTs7fGwAJgwAT7X9+/cbtWvXNt0eGBhoHDt2zHT7qVOnDEnGhx9+aNe+Zs0aQ5LRo0cP032vXbv29wtGKhcuXDAkGcOGDXN2KelStGhRo2nTpo/0GElJSUbFihWNbNmyGdu3b7fbdvv2beOVV14xJBmLFy+2td+6dcuoUqWKkS1bNmPbtm2pxoyLizPef/992/tOnToZnp6edn1SUlKMmjVrGhaLxYiOjr5njdHR0YbFYjG6d++ealtKSooRExOTrnP9u5o2bWoULVr0kYwNAAD+9LBz7L87L6lfv75RoUKFhyv6Pm7dumUkJiY6fFxPT0+jU6dODh3zxo0bRnJy8n371atXz2jdurUxYMAAo3jx4g6twZHSez6PkySjV69ej/QYly5dMvz8/AxfX1/j+PHjdtuuX79u1K1b13BxcTF++OEHW/vly5eNggULGr6+vsahQ4dSjRkdHW2MGjXK9j6tvzs3btwwihYtamTPnt24ffv2PWvcuXOnIckYM2ZMqm23b982Ll68mK5z/bsqVKhg1K9f/5GMDSBtLOcC4JFo2LChJOnUqVOSpLlz58pisWjr1q3q2bOn8uXLp0KFCtn6T58+XRUqVJDValWBAgXUq1cvu4/Z3bFjxw69+OKLypkzpzw9PVW5cmV98skndn0OHz6sl19+Wbly5ZK7u7uqV6+uNWvW2PW5deuWRowYodKlS8vd3V25c+dWnTp1tH79eluf6OhodenSRYUKFZLValX+/PnVsmVL/fbbb7Y+f10TfcuWLbJYLPryyy81ZswYFSpUSO7u7mrUqJGOHz+e6nymTZumEiVKyMPDQ88++6y2bdvm0HXW01oTvXPnzvLy8lJUVJSaNWsmLy8vFSxYUNOmTZMk7d+/Xw0bNpSnp6eKFi2qhQsXphr3ypUr6t+/vwoXLiyr1apSpUpp/Pjx6f7Io/TnU/xVq1aVu7u7ypcvrxUrVti2nTx5UhaLRZMmTUq1348//iiLxaJFixaZjr18+XIdOHBA7733ngIDA+22ZcmSRZ9++qly5Mih4cOH2+3zyy+/aPDgwapTp06qMb29ve/7JIrFYlGdOnVkGIZOnjx5z76nTp2SYRiqXbt2muPky5dP0t+7FlevXlX//v3tPurauHFj7dmzR9KfP69ff/21Tp8+bfvIarFixWzjJSYmatiwYSpVqpSsVqsKFy6sd955R4mJianq6927t5YuXary5cvLw8NDQUFB2r9/vyTp008/ValSpeTu7q4GDRrY/Z0BAAD396DzkoMHD+q5555TtmzZVLBgQU2YMMFue1JSkoYOHaqAgAD5+PjI09NTdevW1ebNm+363ZlDfvTRR5o8ebJKliwpq9WqgwcPpnsM6c+l6z755BNVqlRJ7u7uyps3r1544QXt2rVL0p9zioSEBH3xxRe2ucndT9aePXtWXbt2la+vr6xWqypUqKDPP//c7hh35t+LFy/WBx98oIIFCypbtmyKj4+/5zWOiorStm3b1L59e7Vv316nTp3Sjz/+mGbf+fPn69lnn1W2bNmUM2dO1atXL9UnUr/55hvVr19f2bNnl7e3t2rUqGE3jy5WrFiaTw2b3U+kdT6XLl3SwIEDValSJXl5ecnb21tNmjTRL7/8kmrcmzdvavjw4SpTpozc3d2VP39+tW7dWidOnJBhGCpWrJhatmyZ5n4+Pj5644037nn97liwYIFt6cSAgAB99913tm2bN2+WxWLRypUrU+23cOFCWSwWRUZGmo796aefKjo6Wh9++GGqT214eHjYfm5Gjhxpt8/Zs2c1ceLENJ8A9/X11QcffHDPc3J3d1eNGjV09epVxcbG3rPviRMnJCnNOX2WLFmUO3duSX/vWtzvPrRYsWL69ddftXXr1jSX8kzPvdrdf8fv3JNmy5ZNzz//vM6cOSPDMDRq1CgVKlRIHh4eatmypS5dunTPawFkdiznAuCRuDOZuDNpuKNnz57Kmzevhg4dqoSEBEl/rvE9YsQIBQcH66233tKRI0c0Y8YM/fTTT/rhhx9sS5GsX79ezZo1U/78+dWvXz/5+fnp0KFD+s9//qN+/fpJ+vOjs7Vr11bBggX13nvvydPTU19++aVatWql5cuX66WXXrIdMyIiQq+//rqeffZZxcfHa9euXdqzZ48aN24s6c+P5f3666/q06ePihUrptjYWK1fv15RUVF2wWNaxo0bJxcXFw0cOFBxcXGaMGGCOnbsqB07dtj6zJgxQ71791bdunU1YMAA/fbbb2rVqpVy5sxp9wuGRyE5OVlNmjRRvXr1NGHCBC1YsEC9e/eWp6enBg8erI4dO6p169aaOXOmQkNDFRQUpOLFi0uSrl+/rvr16+vs2bN64403VKRIEf34448KDw/X+fPn07Xe9rFjx/TKK6/ozTffVKdOnTRnzhy1bdtWa9euVePGjVWiRAnVrl1bCxYs0IABA+z2XbBggbJnz57mpP+Or776SpIUGhqa5nYfHx+1bNlSX3zxhY4fP65SpUrZftHyj3/8Iz2X0NSdyW3OnDnv2a9o0aKS/vwYcdu2bZUtW7Y0+/2da/Hmm29q2bJl6t27t8qXL68//vhD33//vQ4dOqRnnnlGgwcPVlxcnH7//XdbKO/l5SXpz5vcFi1a6Pvvv1ePHj1Urlw57d+/X5MmTdLRo0dTrVW6bds2rVmzRr169ZIkRUREqFmzZnrnnXc0ffp09ezZU5cvX9aECRPUtWtXbdq0Kf0XEQCAp9yDzEsuX76sF154Qa1bt1a7du20bNkyvfvuu6pUqZKaNGki6c811j/77DPbshNXr17V7NmzFRISop07d6ZahnHOnDm6efOmevToIavVqly5cv2tMbp166a5c+eqSZMmev3113X79m1t27ZN27dvV/Xq1fXvf//bNh/v0aOHJNnC0piYGNWsWdP2y/u8efPqm2++Ubdu3RQfH59qabpRo0bJzc1NAwcOVGJioulSOncsWrRInp6eatasmTw8PFSyZEktWLBAtWrVsus3YsQIDR8+XLVq1dLIkSPl5uamHTt2aNOmTXr++ecl/fnAUNeuXVWhQgWFh4crR44c+vnnn7V27Vq9+uqr6f4zvN/5HDx4UKtWrVLbtm1VvHhxxcTE6NNPP1X9+vV18OBBFShQQNKfc/1mzZpp48aNat++vfr166erV69q/fr1OnDggEqWLKnXXntNEyZM0KVLl5QrVy7bcb/66ivFx8frtddeu2+NW7du1ZIlS9S3b19ZrVZNnz5dL7zwgnbu3KmKFSuqQYMGKly4sBYsWGC7D7tjwYIFKlmypIKCgkzH/+qrr+Tu7q527dqlub148eKqU6eONm3apBs3bsjDw0Nr1qyRh4eHXn755fRcZlN3QuYcOXLcs9+dOf2CBQtUu3ZtZc2adsz2d67F/e5DJ0+erD59+sjLy0uDBw+W9OcvB6S/f6+2YMECJSUlqU+fPrp06ZImTJigdu3aqWHDhtqyZYveffddHT9+XP/85z81cODAVL/EAp4qzn0QHoCzOWo5lxEjRhgXLlwwoqOjjS1bthjVqlUzJBnLly83DMMw5syZY0gy6tSpY/eRuNjYWMPNzc14/vnn7T6iOHXqVEOS8fnnnxuG8edH4YoXL24ULVrUuHz5sl0NKSkptv9u1KiRUalSJePmzZt222vVqmWULl3a1lalSpV7Lily+fLlNJep+av69evbfYxu8+bNhiSjXLlydh93/eSTTwxJxv79+w3DMIzExEQjd+7cRo0aNYxbt27Z+s2dO9eQ9Lc+mnev5Vzu/PnMmTPH1tapUydDkjF27Fi78/Xw8DAsFovdEieHDx9ONfaoUaMMT09P4+jRo3bHeu+994wsWbIYUVFR96y3aNGidj8bhvHnR5Lz589vVKtWzdb26aefGpLsPoaZlJRk5MmT574f+a1atarh4+Nzzz4TJ040JBlr1qwxDMMwqlWrdt997nZnOZcLFy4YFy5cMI4fP2589NFHhsViMSpWrGj3c2kmNDTUkGTkzJnTeOmll4yPPvoozY+dpvda+Pj43PdjtWbLufz73/82XFxcUn1kfObMmYYku4/JSjKsVqtx6tSpVDX6+fkZ8fHxtvbw8HBDkl1fAAAyu4edY//deUn9+vUNSca8efNsbYmJiYafn5/Rpk0bW9vt27dTLcly+fJlw9fX1+jataut7c4c0tvb24iNjbXrn94xNm3aZEgy+vbtm6reu+dJZsu5dOvWzcifP3+q5TDat29v+Pj4GNevXzcM4//n3yVKlLC1pUelSpWMjh072t6///77Rp48eezm5seOHTNcXFyMl156KdVyKnfO4cqVK0b27NmNwMBA48aNG6bnWbRo0TTP0+x+Iq3zuXnzZqo6Tp06ZVitVmPkyJG2ts8//9yQZEycODHV8e7UdOTIEUOSMWPGDLvtLVq0MIoVK3bfuawkQ5Kxa9cuW9vp06cNd3d346WXXrK1hYeHG1ar1bhy5YqtLTY21siaNet9l6PMkSOHUaVKlXv26du3ryHJ2Ldvn2EYhpEzZ8777nO3+vXrG/7+/rY5/eHDh41BgwYZktK1BGVKSort75+vr6/RoUMHY9q0acbp06dT9U3PtUjvfajZci7pvVe783c8b968dvXcmbtXqVLF7u9Chw4dDDc3N7v7bOBpw3IuABxi2LBhyps3r/z8/NSgQQOdOHFC48ePt/uiS0nq3r27smTJYnu/YcMGJSUlqX///nJxcbHr5+3tra+//lqS9PPPP+vUqVPq379/qqcBLBaLJOnSpUvatGmT2rVrp6tXr+rixYu6ePGi/vjjD4WEhOjYsWM6e/asJClHjhz69ddfdezYsTTPx8PDQ25ubtqyZYsuX778t69Hly5d7J5+qVu3riTZlvjYtWuX/vjjD3Xv3t3uaYWOHTve9wlmR3n99ddt/50jRw6VLVtWnp6edk96lC1bVjly5LBbmmTp0qWqW7eucubMabvGFy9eVHBwsJKTk+0+wmmmQIECdk9geHt7KzQ0VD///LOio6MlSe3atZO7u7vdF3SuW7dOFy9evO+TMVevXlX27Nnv2efO9jsf9Y2Pj7/vPn+VkJCgvHnzKm/evCpVqpQGDhyo2rVra/Xq1bafy3uZM2eOpk6dquLFi2vlypUaOHCgypUrp0aNGtl+VqX0X4scOXJox44dOnfu3N86D+nPP9dy5crJ39/f7s/1ztJMf/2IdqNGjew+kXFn2Zw2bdrYXcc77fdb3gYAAPy/B5mXeHl52c0L3Nzc9Oyzz9r9G5wlSxbbHDUlJUWXLl3S7du3Vb16ddvyb3dr06aN8ubNa9eW3jGWL18ui8WiYcOGpRr3fvMkwzC0fPlyNW/eXIZh2M1NQkJCFBcXl6reTp06ycPD457j3rFv3z7t379fHTp0sLV16NBBFy9e1Lp162xtq1atUkpKioYOHWp3r3L3Oaxfv15Xr17Ve++9J3d39791nveS1vlYrVZbHcnJyfrjjz/k5eWlsmXLprr2efLkSfWFm3fXVKZMGQUGBtrNLy9duqRvvvlGHTt2TFftQUFBti/tlKQiRYqoZcuWWrdunZKTkyX9+cnQxMRELVu2zNZvyZIlun37doaZ0x8+fNg2p/f399eHH36oFi1a2C2JacZisWjdunUaPXq0cubMqUWLFqlXr14qWrSoXnnlFbslStNzLR72PvTv3qu1bdtWPj4+tvd35u6vvfaa3X1qYGCgkpKS7O5RgKcNIToAh+jRo4fWr1+vjRs3avfu3YqNjdU777yTqt+dJUHuOH36tKQ/w9q7ubm5qUSJErbtd5aHqVixomkNx48fl2EYGjJkiG0SdOd1Z/J+Z027kSNH6sqVKypTpowqVaqkQYMGad++fbaxrFarxo8fr2+++Ua+vr62ZU/uBLz3U6RIEbv3d4LxOxOhO+dVqlQpu35Zs2a971IxjnBnTcq7+fj4qFChQqkmzD4+PnYTuGPHjmnt2rWprnFwcLAk3XfdQOnP8/7rccqUKSPp/5dDyZEjh5o3b263luSCBQtUsGBBW7BrJnv27Lp69eo9+9zZfmeS7e3tfd99/srd3V3r16/X+vXrNWfOHJUrV06xsbHpvoFzcXFRr169tHv3bl28eFGrV69WkyZNtGnTJrVv397WL73XYsKECTpw4IAKFy6sZ599VsOHD093eH3s2DH9+uuvqf5c7/y5/PXP9a8/43cm34ULF06z/UFuAgAAeFo9yLwkrXlczpw5U/0b/MUXX6hy5cq27wXKmzevvv76a8XFxaUa869z978zxokTJ1SgQAG7pULS68KFC7py5Yr+9a9/pZqbdOnSRVLquYlZrWmZP3++PD09VaJECR0/flzHjx+Xu7u7ihUrZhcqnzhxQi4uLipfvrzpWOm5T3kQaZ1PSkqKJk2apNKlS8tqtSpPnjzKmzev9u3bl+raly1b1nRpkTtCQ0P1ww8/2O5Nli5dqlu3bqV7GaHSpUunaitTpoyuX7+uCxcuSJL8/f1Vo0YNu+u6YMEC1axZM9W90F89rjl9sWLFtH79eq1bt07Tp09XwYIFdeHChVS/FDFjtVo1ePBgHTp0SOfOndOiRYtUs2ZNffnll+rdu7etX3quxcPeh/7dezXm9ED6sSY6AIcoXbq07R/me0lvuPgg7nxRysCBAxUSEpJmnzuTk3r16unEiRNavXq1vv32W3322WeaNGmSZs6caXtCu3///mrevLlWrVqldevWaciQIYqIiNCmTZtUrVq1e9Zy99P2dzMM40FPz6HM6ktP3SkpKWrcuHGavySR/j8Md4TQ0FAtXbpUP/74oypVqqQ1a9aoZ8+eqZ4E+qty5cpp7969ioqKSjUxvOPOL03u3BT5+/vr559/1pkzZ1JNGs1kyZLF7uc+JCRE/v7+euONN1J9me395M6dWy1atFCLFi3UoEEDbd26VadPn7ats5iea9GuXTvVrVtXK1eu1LfffqsPP/xQ48eP14oVK2xroZpJSUlRpUqVNHHixDS3//WaPMzPEAAAuLcHnZek5e5/g+fPn6/OnTurVatWGjRokPLly6csWbIoIiLCFgbfLa25+98d40Hcmde/9tpr6tSpU5p9KleufN9a02IYhhYtWqSEhIQ0w/HY2Fhdu3bN9r0xjmL2ZHdycnKaf3Zpnc/YsWM1ZMgQde3aVaNGjVKuXLnk4uKi/v37231pZHq1b99eAwYM0IIFC/T+++9r/vz5ql69eqoHnB5WaGio+vXrp99//12JiYnavn27pk6det/9ypUrp59//lmJiYmyWq1p9tm3b59cXV1tgb6/v7/27t2rpKSk+66Lf4enp6fdnL527dp65pln9P7772vKlCnpGuOO/Pnzq3379mrTpo0qVKigL7/8UnPnzrX9QiM91+Jh7kP/7r0ac3og/QjRATjVnYDwyJEjKlGihK09KSlJp06dsk1m7nzB0IEDB0zD+jv7u7q6pivQz5Url7p06aIuXbro2rVrqlevnoYPH263zEnJkiX19ttv6+2339axY8dUtWpVffzxx5o/f/6DnfD/3Dnv48eP67nnnrO13759W7/99luqm4KMpGTJkrp27Vq6rrGZO58auPtm4ujRo5Jk9yT+Cy+8oLx582rBggUKDAzU9evX0/VkTLNmzbRo0SLNmzdPH3zwQart8fHxWr16tfz9/W2/WGnevLkWLVqk+fPnKzw8/IHOK3/+/BowYIBGjBih7du3q2bNmg80TvXq1bV161adP3/e9rOS3muRP39+9ezZUz179lRsbKyeeeYZjRkzxhaim93AlSxZUr/88osaNWr0UB89BgAAD88R85K0LFu2TCVKlNCKFSvs/r1Pa8mVhx2jZMmSWrduXaovrvyrtOYdefPmVfbs2ZWcnPxQc860bN26Vb///rtGjhypcuXK2W27fPmyevTooVWrVum1115TyZIllZKSooMHD6b60tU77r5PudeT1Tlz5rRb2uOO06dP290H3cuyZcv03HPPafbs2XbtV65cUZ48eexq2rFjh27duiVXV1fT8XLlyqWmTZtqwYIF6tixo3744YdUXzx5L2ktjXn06FFly5bN7lOv7du3V1hYmBYtWqQbN27I1dVVr7zyyn3Hb9asmSIjI7V06dI0l3757bfftG3bNgUHB9t+6dC8eXNFRkZq+fLldsv1/B2VK1fWa6+9pk8//VQDBw40fSjnXlxdXVW5cmUdO3ZMFy9elJ+fn6T0X4v73Yfea07/sPdqANLGci4AnCo4OFhubm6aMmWK3W+1Z8+erbi4ODVt2lSS9Mwzz6h48eKaPHlyqsnnnf3y5cunBg0a6NNPP9X58+dTHevORwol6Y8//rDb5uXlpVKlSikxMVHSn99qfvPmTbs+JUuWVPbs2W19Hkb16tWVO3duzZo1S7dv37a1L1iwIMN/RK5du3aKjIy0Wy/yjitXrtidj5lz585p5cqVtvfx8fGaN2+eqlataptgSn8ub9OhQwfbExyVKlVK1y8YXn75ZZUvX17jxo3Trl277LalpKTorbfe0uXLl+1u9l5++WVVqlRJY8aMUWRkZKoxr169qsGDB9/32H369FG2bNk0bty4e/aLjo7WwYMHU7UnJSVp48aNcnFxsbsRu9+1SE5OTvUx7Hz58qlAgQJ2P7Oenp5pfly7Xbt2Onv2rGbNmpVq240bN5SQkHDP8wEAAI7jqHnJX915uvTuefeOHTvSPMbDjtGmTRsZhqERI0akGuPufT09PVPN77NkyaI2bdpo+fLlOnDgQKr9757X/113lnIZNGiQXn75ZbtX9+7dVbp0adtyG61atZKLi4tGjhyZ6knvO+fw/PPPK3v27IqIiEh1/3D3eZYsWVLbt29XUlKSre0///mPzpw5k+7as2TJkupJ4KVLl6Zap7pNmza6ePFimk97/3X/f/zjHzp48KAGDRqkLFmy2C0peD+RkZF2a7GfOXNGq1ev1vPPP2/3JHOePHnUpEkTzZ8/XwsWLNALL7xgF/qbeeONN5QvXz4NGjQo1RKFN2/eVJcuXWQYhoYOHWprf/PNN5U/f369/fbbtod07hYbG6vRo0ff99jvvPOObt26ZfopzTuOHTumqKioVO1XrlxRZGSkcubMafcLhftdi/Teh6b190ZyzL0agLTxJDoAp8qbN6/Cw8M1YsQIvfDCC2rRooWOHDmi6dOnq0aNGrYnDlxcXDRjxgw1b95cVatWVZcuXZQ/f34dPnxYv/76q22SMG3aNNWpU0eVKlVS9+7dVaJECcXExCgyMlK///67fvnlF0l/LuHRoEEDBQQEKFeuXNq1a5eWLVtmW7Pu6NGjatSokdq1a6fy5csra9asWrlypWJiYv7WxNKMm5ubhg8frj59+qhhw4Zq166dfvvtN82dO1clS5bM0E8CDxo0SGvWrFGzZs3UuXNnBQQEKCEhQfv379eyZcv022+/3XdSXKZMGXXr1k0//fSTfH199fnnnysmJkZz5sxJ1Tc0NFRTpkzR5s2bNX78+HTV6ObmpmXLlqlRo0aqU6eOunTpourVq+vKlStauHCh9uzZo7ffftvuz9LV1VUrVqxQcHCw6tWrp3bt2ql27dpydXXVr7/+qoULFypnzpwaM2bMPY+dO3dudenSRdOnT9ehQ4dSPeF0x++//65nn31WDRs2VKNGjeTn56fY2FgtWrRIv/zyi/r375/qOt7rWly9elWFChXSyy+/rCpVqsjLy0sbNmzQTz/9pI8//tjWLyAgQEuWLFFYWJhq1KghLy8vNW/eXP/4xz/05Zdf6s0339TmzZtVu3ZtJScn6/Dhw/ryyy+1bt06Va9ePV3XHwAAPBxHzUv+qlmzZlqxYoVeeuklNW3aVKdOndLMmTNVvnx5Xbt2zaFjPPfcc/rHP/6hKVOm6NixY3rhhReUkpKibdu26bnnnrPNuwMCArRhwwZNnDhRBQoUUPHixRUYGKhx48Zp8+bNCgwMVPfu3VW+fHldunRJe/bs0YYNG3Tp0qW/de6SlJiYqOXLl6tx48am6123aNFCn3zyiWJjY1WqVCkNHjxYo0aNUt26ddW6dWtZrVb99NNPKlCggCIiIuTt7a1Jkybp9ddfV40aNfTqq68qZ86c+uWXX3T9+nV98cUXkqTXX39dy5Yt0wsvvKB27drpxIkTmj9/vu1J9vRe+5EjR6pLly6qVauW9u/frwULFqR6kj00NFTz5s1TWFiYdu7cqbp16yohIUEbNmxQz5491bJlS1vfpk2bKnfu3Fq6dKmaNGmifPnypbueihUrKiQkRH379pXVatX06dMlKc1fnISGhurll1+WJI0aNSpd4+fOnVvLli1T06ZN9cwzz+j1119X+fLlFR0drblz5+r48eP65JNPVKtWLds+OXPm1MqVK/Xiiy+qatWqeu2112xffrpnzx4tWrRIQUFB9z12+fLl9eKLL+qzzz7TkCFDlDt37jT7/fLLL3r11VfVpEkT1a1bV7ly5dLZs2f1xRdf6Ny5c5o8eXKqpVHudS3Sex8aEBCgGTNmaPTo0SpVqpTy5cunhg0bOuReDYAJA8BTbf/+/Ubt2rVNtwcGBhrHjh0z3X7q1ClDkvHhhx/e8zhz5swxJBk//fRTmtunTp1q+Pv7G66uroavr6/x1ltvGZcvX07V7/vvvzcaN25sZM+e3fD09DQqV65s/POf/7Trc+LECSM0NNTw8/MzXF1djYIFCxrNmjUzli1bZuszevRo49lnnzVy5MhheHh4GP7+/saYMWOMpKQkwzAM4+LFi0avXr0Mf39/w9PT0/Dx8TECAwONL7/80u5Y9evXN+rXr297v3nzZkOSsXTp0jSv05w5c+zap0yZYhQtWtSwWq3Gs88+a/zwww9GQECA8cILL9zzet7twoULhiRj2LBhqbalddxOnToZnp6eqfrWr1/fqFChQqr2okWLGk2bNrVru3r1qhEeHm6UKlXKcHNzM/LkyWPUqlXL+Oijj2zX0Myd8datW2dUrlzZsFqthr+/f6prdrcKFSoYLi4uxu+//37Psf8qNjbWCAsLM0qVKmVYrVYjR44cRnBwsLFmzRrTfS5fvmwMHTrUqFSpkpEtWzbD3d3dqFixohEeHm6cP3/e1s/sOhrGnz+DWbJkMTp16mR6nPj4eOOTTz4xQkJCjEKFChmurq5G9uzZjaCgIGPWrFlGSkpKmvuZXYvExERj0KBBRpUqVWx/P6pUqWJMnz7drt+1a9eMV1991ciRI4chyShatKhtW1JSkjF+/HijQoUKhtVqNXLmzGkEBAQYI0aMMOLi4mz9JBm9evWyG9fs/wVmfycAAMjMHnaOfUd65yVm87hOnTrZ/VufkpJijB071jb/rFatmvGf//wnVb97zfHTO4ZhGMbt27eNDz/80PD39zfc3NyMvHnzGk2aNDF2795t63P48GGjXr16hoeHhyHJbv4UExNj9OrVyyhcuLDh6upq+Pn5GY0aNTL+9a9/2fr8nbnG8uXLDUnG7NmzTfts2bLFkGR88skntrbPP//cqFatmm1+VL9+fWP9+vV2+61Zs8aoVauW4eHhYXh7exvPPvussWjRIrs+H3/8sVGwYEHDarUatWvXNnbt2pXu+wnDMIybN28ab7/9tpE/f37Dw8PDqF27thEZGZlqDMMwjOvXrxuDBw82ihcvbrt2L7/8snHixIlU4/bs2dOQZCxcuPBel8/Onfng/PnzjdKlS9t+FjZv3pxm/8TERCNnzpyGj4+PcePGjXQfxzD+/Hns3r27UaRIEcPV1dXIkyeP0aJFC2Pbtm2m+5w7d84YMGCAUaZMGcPd3d3Ili2bERAQYIwZM8ZuXmv2d8cw/v9nIa37rDtiYmKMcePGGfXr1zfy589vZM2a1ciZM6fRsGFDu/vPu93rWqT3PjQ6Otpo2rSpkT17dkOS3Z9/eu7V/u7c/X7388DTwGIYfCsA8DQ7cOCA3nzzTX3//fdpbq9Zs6bmz59/329Oh2OkpKQob968at26dZrLajytqlWrply5cmnjxo3OLsXpuBYAAGR8zLHxJBkwYIBmz56t6OhoZcuW7ZEc4/bt2ypQoICaN2+eak33pw3XAngysSY6ADjJzZs3U61JOG/ePF26dEkNGjRwTlEZ0K5du7R3716FhoY6uxSn41oAAADAkW7evKn58+erTZs2jyxAl6RVq1bpwoULzGPFtQCeVKyJDkDbt29Xjhw50tyW3rUR8fdt375dAwYMUNu2bZU7d27t2bNHs2fPVsWKFdW2bVtnl+d0Bw4c0O7du/Xxxx8rf/78aX5r/dOCawEAwJOHOTYystjYWG3YsEHLli3TH3/8oX79+j2S4+zYsUP79u3TqFGjVK1aNdWvX/+RHOdJwLUAnmyE6MBTrmLFinxDt5MUK1ZMhQsX1pQpU3Tp0iXlypVLoaGhGjdunNzc3JxdntMtW7ZMI0eOVNmyZbVo0SLTL396GnAtAAB4sjDHRkZ38OBBdezYUfny5dOUKVNUtWrVR3KcGTNmaP78+apatarmzp37SI7xpOBaAE821kQHAAAAoO+++04ffvihdu/erfPnz2vlypVq1arVPffZsmWLwsLC9Ouvv6pw4cL64IMP1Llz58dSLwAAAPC4sCY6AAAAACUkJKhKlSqaNm1auvqfOnVKTZs21XPPPae9e/eqf//+ev3117Vu3bpHXCkAAADweD11T6KnpKTo3Llzyp49uywWi7PLAQAAwFPGMAxdvXpVBQoUkItLxnymxWKx3PdJ9HfffVdff/21Dhw4YGtr3769rly5orVr16a5T2JiohITE23vU1JSdOnSJeXOnZu5OQAAAB679M7Nn7o10c+dO6fChQs7uwwAAAA85c6cOaNChQo5u4wHFhkZqeDgYLu2kJAQ9e/f33SfiIgIjRgx4hFXBgAAAPw995ubP3Uhevbs2SX9eWG8vb2dXA0AAACeNvHx8SpcuLBtXvqkio6Olq+vr12br6+v4uPjdePGDXl4eKTaJzw8XGFhYbb3cXFxKlKkCHNzAAAAOEV65+ZPXYh+52Oi3t7eTNQBAADgNE/j8iVWq1VWqzVVO3NzAAAAONP95uYZcxFGAAAAABman5+fYmJi7NpiYmLk7e2d5lPoAAAAwJOKEB0AAADA3xYUFKSNGzfata1fv15BQUFOqggAAAB4NAjRAQAAAOjatWvau3ev9u7dK0k6deqU9u7dq6ioKEl/rmceGhpq6//mm2/q5MmTeuedd3T48GFNnz5dX375pQYMGOCM8gEAAIBHhhAdAAAAgHbt2qVq1aqpWrVqkqSwsDBVq1ZNQ4cOlSSdP3/eFqhLUvHixfX1119r/fr1qlKlij7++GN99tlnCgkJcUr9AAAAwKNiMQzDcHYRj1N8fLx8fHwUFxfHlxcBAADgsWM++v+4FgAAAHCm9M5HeRIdAAAAAAAAAAAThOgAAAAAAAAAAJggRAcAAAAAAAAAwAQhOgAAAAAAAAAAJgjRAQAAAAAAAAAwQYgOAAAAAAAAAIAJQnQAAAAAAAAAAEwQogMAAAAAAAAAYIIQHQAAAAAAAAAAE4ToAAAAAAAAAACYIEQHAAAAAAAAAMAEIToAAAAAAAAAACYI0QEAAAAAAAAAMEGIDgAAAAAAAACACUJ0AAAAAAAAAABMZHV2AQD+n2EYSkhIsL339PSUxWJxYkUAAAAAAADA040QHchAEhIS1LJlS9v71atXy8vLy4kVAQAAAAAAAE83lnMBAAAAAAAAAMAEIToAAAAAAAAAACYI0QEAAAAAAAAAMEGIDgAAAAAAAACACUJ0AAAAAAAAAABMEKIDAAAAAAAAAGCCEB0AAAAAAAAAABOE6AAAAAAAAAAAmCBEBwAAAAAAAADABCE6AAAAAAAAAAAmMlSInpycrCFDhqh48eLy8PBQyZIlNWrUKBmGYetjGIaGDh2q/Pnzy8PDQ8HBwTp27JgTqwYAAAAAAAAAZFYZKkQfP368ZsyYoalTp+rQoUMaP368JkyYoH/+85+2PhMmTNCUKVM0c+ZM7dixQ56engoJCdHNmzedWDkAAAAAAAAAIDPK6uwC7vbjjz+qZcuWatq0qSSpWLFiWrRokXbu3Cnpz6fQJ0+erA8++EAtW7aUJM2bN0++vr5atWqV2rdvn2rMxMREJSYm2t7Hx8c/hjMBAAAAAAAAAGQGGepJ9Fq1amnjxo06evSoJOmXX37R999/ryZNmkiSTp06pejoaAUHB9v28fHxUWBgoCIjI9McMyIiQj4+PrZX4cKFH/2JAAAAAAAAAAAyhQz1JPp7772n+Ph4+fv7K0uWLEpOTtaYMWPUsWNHSVJ0dLQkydfX124/X19f27a/Cg8PV1hYmO19fHw8QToAAAAAAAAAIF0yVIj+5ZdfasGCBVq4cKEqVKigvXv3qn///ipQoIA6der0QGNarVZZrVYHVwoAAAAAAAAAeBpkqBB90KBBeu+992xrm1eqVEmnT59WRESEOnXqJD8/P0lSTEyM8ufPb9svJiZGVatWdUbJAAAAAAAAAIBMLEOtiX79+nW5uNiXlCVLFqWkpEiSihcvLj8/P23cuNG2PT4+Xjt27FBQUNBjrRUAAAAAAAAAkPllqCfRmzdvrjFjxqhIkSKqUKGCfv75Z02cOFFdu3aVJFksFvXv31+jR49W6dKlVbx4cQ0ZMkQFChRQq1atnFs8AAAAAAAAACDTyVAh+j//+U8NGTJEPXv2VGxsrAoUKKA33nhDQ4cOtfV55513lJCQoB49eujKlSuqU6eO1q5dK3d3dydWDgAAAAAAAADIjCyGYRjOLuJxio+Pl4+Pj+Li4uTt7e3scgA7165dU8uWLW3vV69eLS8vLydWBAAAHI356P/jWgAAAMCZ0jsfzVBrogMAAAAAAAAAkJEQogMAAAAAAAAAYIIQHQAAAAAAAAAAE4ToAAAAAAAAAACYIEQHAAAAAAAAAMAEIToAAAAAAAAAACYI0QEAAAAAAAAAMEGIDgAAAAAAAACACUJ0AAAAAAAAAABMEKIDAAAAAAAAAGCCEB0AAAAAAAAAABOE6AAAAAAAAAAAmCBEBwAAAAAAAADABCE6AAAAAAAAAAAmCNEBAAAAAAAAADBBiA4AAAAAAAAAgAlCdAAAAAAAAAAATBCiAwAAAAAAAABgghAdAAAAAAAAAAAThOgAAAAAAAAAAJjI6uwCAAAAAAAAAGQuhmEoISHB9t7T01MWi8WJFQEPjhAdAAAAAAAAgEMlJCSoZcuWtverV6+Wl5eXEysCHhzLuQAAAAAAAAAAYIIQHQAAAAAAAAAAE4ToAAAAAAAAAACYIEQHAAAAAAAAAMAEIToAAAAAAAAAACYI0QEAAAAAAAAAMEGIDgAAAAAAAACACUJ0AAAAAAAAAABMEKIDAAAAAAAAAGCCEB0AAAAAAAAAABNZnV0AAAAAAAAZjWEYSkhIsL339PSUxWJxYkUAAMBZCNEBAAAAAPiLhIQEtWzZ0vZ+9erV8vLycmJFAADAWVjOBQAAAAAAAAAAE4ToAAAAAAAAAACYIEQHAAAAAAAAAMAEIToAAAAAAAAAACYI0QEAAAAAAAAAMEGIDgAAAAAAAACACUJ0AAAAAAAAAABMZHV2AQAAAAAAAHiyBQya5+wSkMFYbifJ5673DYYslpHVzWn1IOPZ/WGos0tIN55EBwAAAAAAAADABCE6AAAAAAAAAAAmCNEBAAAAAAAAADBBiA4AAAAAAAAAgAlCdAAAAAAAAAAATBCiAwAAAAAAAABgIkOF6MWKFZPFYkn16tWrlyTp5s2b6tWrl3Lnzi0vLy+1adNGMTExTq4aAAAAAAAAAJBZZagQ/aefftL58+dtr/Xr10uS2rZtK0kaMGCAvvrqKy1dulRbt27VuXPn1Lp1a2eWDAAAAAAAAADIxLI6u4C75c2b1+79uHHjVLJkSdWvX19xcXGaPXu2Fi5cqIYNG0qS5syZo3Llymn79u2qWbNmmmMmJiYqMTHR9j4+Pv7RnQAAAACAJ1LAoHnOLgEZjOV2knzuet9gyGIZWd2cVg8ynt0fhjq7BADAY5KhnkS/W1JSkubPn6+uXbvKYrFo9+7dunXrloKDg219/P39VaRIEUVGRpqOExERIR8fH9urcOHCj6N8AAAAAAAAAEAmkGFD9FWrVunKlSvq3LmzJCk6Olpubm7KkSOHXT9fX19FR0ebjhMeHq64uDjb68yZM4+wagAAAAAAAABAZpJhQ/TZs2erSZMmKlCgwEONY7Va5e3tbfcCAAAAkNq0adNUrFgxubu7KzAwUDt37rxn/8mTJ6ts2bLy8PBQ4cKFNWDAAN28efMxVQsAAAA8HhkyRD99+rQ2bNig119/3dbm5+enpKQkXblyxa5vTEyM/Pz8HnOFAAAAQOayZMkShYWFadiwYdqzZ4+qVKmikJAQxcbGptl/4cKFeu+99zRs2DAdOnRIs2fP1pIlS/T+++8/5soBAACARytDhuhz5sxRvnz51LRpU1tbQECAXF1dtXHjRlvbkSNHFBUVpaCgIGeUCQAAAGQaEydOVPfu3dWlSxeVL19eM2fOVLZs2fT555+n2f/HH39U7dq19eqrr6pYsWJ6/vnn1aFDh/s+vQ4AAAA8aTJciJ6SkqI5c+aoU6dOypo1q63dx8dH3bp1U1hYmDZv3qzdu3erS5cuCgoKUs2aNZ1YMQAAAPBkS0pK0u7duxUcHGxrc3FxUXBwsCIjI9Pcp1atWtq9e7ctND958qT++9//6sUXXzQ9TmJiouLj4+1eAAAAQEaX9f5dHq8NGzYoKipKXbt2TbVt0qRJcnFxUZs2bZSYmKiQkBBNnz7dCVUCAAAAmcfFixeVnJwsX19fu3ZfX18dPnw4zX1effVVXbx4UXXq1JFhGLp9+7befPPNey7nEhERoREjRji0dgAAAOBRy3BPoj///PMyDENlypRJtc3d3V3Tpk3TpUuXlJCQoBUrVrAeOgAAAOAEW7Zs0dixYzV9+nTt2bNHK1as0Ndff61Ro0aZ7hMeHq64uDjb68yZM4+xYgAAAODBZLgn0QEAAAA8Xnny5FGWLFkUExNj1x4TE2P60MqQIUP0j3/8Q6+//rokqVKlSkpISFCPHj00ePBgubikfl7HarXKarU6/gQAAACARyjDPYkOAAAA4PFyc3NTQECANm7caGtLSUnRxo0bFRQUlOY+169fTxWUZ8mSRZJkGMajKxYAAAB4zHgSHQAAAIDCwsLUqVMnVa9eXc8++6wmT56shIQEdenSRZIUGhqqggULKiIiQpLUvHlzTZw4UdWqVVNgYKCOHz+uIUOGqHnz5rYwHQAAAMgMCNEBAAAA6JVXXtGFCxc0dOhQRUdHq2rVqlq7dq3ty0ajoqLsnjz/4IMPZLFY9MEHH+js2bPKmzevmjdvrjFjxjjrFAAAAIBHghAdAAAAgCSpd+/e6t27d5rbtmzZYvc+a9asGjZsmIYNG/YYKgMAAACchzXRAQAAAAAAAAAwQYgOAAAAAAAAAIAJQnQAAAAAAAAAAEwQogMAAAAAAAAAYIIQHQAAAAAAAAAAE4ToAAAAAAAAAACYyOrsAgAAAAAAAABkLkYWV8VV7mD3HnhSEaIDAAAAAPAXhD8A8JAsFhlZ3ZxdBeAQhOgAAAAAAPwV4Q8AAPgf1kQHAAAAAAAAAMAEIToAAAAAAAAAACYI0QEAAAAAAAAAMEGIDgAAAAAAAACACUJ0AAAAAAAAAABMEKIDAAAAAAAAAGCCEB0AAAAAAAAAABOE6AAAAAAAAAAAmCBEBwAAAAAAAADABCE6AAAAAAAAAAAmCNEBAAAAAAAAADBBiA4AAAAAAAAAgAlCdAAAAAAAAAAATBCiAwAAAAAAAABgghAdAAAAAAAAAAAThOgAAAAAAAAAAJggRAcAAAAAAAAAwAQhOgAAAAAAAAAAJgjRAQAAAAAAAAAwQYgOAAAAAAAAAIAJQnQAAAAAAAAAAEwQogMAAAAAAAAAYIIQHQAAAAAAAAAAE4ToAAAAAAAAAACYIEQHAAAAAAAAAMAEIToAAAAAAAAAACYI0QEAAAAAAAAAMEGIDgAAAAAAAACACUJ0AAAAAAAAAABMEKIDAAAAAAAAAGCCEB0AAAAAAAAAABOE6AAAAAAAAAAAmCBEBwAAAAAAAADABCE6AAAAAAAAAAAmMlyIfvbsWb322mvKnTu3PDw8VKlSJe3atcu23TAMDR06VPnz55eHh4eCg4N17NgxJ1YMAAAAAAAAAMisMlSIfvnyZdWuXVuurq765ptvdPDgQX388cfKmTOnrc+ECRM0ZcoUzZw5Uzt27JCnp6dCQkJ08+ZNJ1YOAAAAAAAAAMiMsjq7gLuNHz9ehQsX1pw5c2xtxYsXt/23YRiaPHmyPvjgA7Vs2VKSNG/ePPn6+mrVqlVq3779Y68ZAAAAAAAAAJB5Zagn0desWaPq1aurbdu2ypcvn6pVq6ZZs2bZtp86dUrR0dEKDg62tfn4+CgwMFCRkZFpjpmYmKj4+Hi7FwAAAAAAAAAA6ZGhQvSTJ09qxowZKl26tNatW6e33npLffv21RdffCFJio6OliT5+vra7efr62vb9lcRERHy8fGxvQoXLvxoTwIAAAAAAAAAkGlkqBA9JSVFzzzzjMaOHatq1aqpR48e6t69u2bOnPnAY4aHhysuLs72OnPmjAMrBgAAAAAAAABkZhkqRM+fP7/Kly9v11auXDlFRUVJkvz8/CRJMTExdn1iYmJs2/7KarXK29vb7gUAAAAAAAAAQHpkqBC9du3aOnLkiF3b0aNHVbRoUUl/fsmon5+fNm7caNseHx+vHTt2KCgo6LHWCgAAAAAAAADI/LI6u4C7DRgwQLVq1dLYsWPVrl077dy5U//617/0r3/9S5JksVjUv39/jR49WqVLl1bx4sU1ZMgQFShQQK1atXJu8QAAAAAAAACATCdDheg1atTQypUrFR4erpEjR6p48eKaPHmyOnbsaOvzzjvvKCEhQT169NCVK1dUp04drV27Vu7u7k6sHAAAAAAAAACQGWWoEF2SmjVrpmbNmplut1gsGjlypEaOHPkYqwIAAAAAAAAAPI0y1JroAAAAAAAAAABkJIToAAAAAAAAAACYIEQHAAAAAAAAAMAEIToAAAAAAAAAACYI0QEAAAAAAAAAMEGIDgAAAAAAAACACUJ0AAAAAAAAAABMEKIDAAAAAAAAAGCCEB0AAAAAAAAAABOE6AAAAAAAAAAAmCBEBwAAAAAAAADABCE6AAAAAAAAAAAmCNEBAAAAAAAAADBBiA4AAAAAAAAAgAlCdAAAAAAAAAAATBCiAwAAAAAAAABgghAdAAAAAAAAAAAThOgAAAAAAAAAAJggRAcAAAAAAAAAwAQhOgAAAAAAAAAAJgjRAQAAAAAAAAAwQYgOAAAAAAAAAIAJQnQAAAAAAAAAAEwQogMAAAAAAAAAYIIQHQAAAAAAAAAAE4ToAAAAAAAAAACYIEQHAAAAAAAAAMAEIToAAAAAAAAAACYI0QEAAAAAAAAAMEGIDgAAAAAAAACACUJ0AAAAAAAAAABMEKIDAAAAAAAAAGCCEB0AAAAAAAAAABOE6AAAAAAAAAAAmCBEBwAAAAAAAADABCE6AAAAAAAAAAAmCNEBAAAAAAAAADBBiA4AAAAAAAAAgAlCdAAAAACSpGnTpqlYsWJyd3dXYGCgdu7cec/+V65cUa9evZQ/f35ZrVaVKVNG//3vfx9TtQAAAMDjkdXZBQAAAABwviVLligsLEwzZ85UYGCgJk+erJCQEB05ckT58uVL1T8pKUmNGzdWvnz5tGzZMhUsWFCnT59Wjhw5Hn/xAAAAwCNEiA4AAABAEydOVPfu3dWlSxdJ0syZM/X111/r888/13vvvZeq/+eff65Lly7pxx9/lKurqySpWLFi9zxGYmKiEhMTbe/j4+MddwIAAADAI8JyLgAAAMBTLikpSbt371ZwcLCtzcXFRcHBwYqMjExznzVr1igoKEi9evWSr6+vKlasqLFjxyo5Odn0OBEREfLx8bG9Chcu7PBzAQAAABzNIU+inzp1Stu2bdPp06d1/fp15c2bV9WqVVNQUJDc3d0dcQgAAAAAj8jFixeVnJwsX19fu3ZfX18dPnw4zX1OnjypTZs2qWPHjvrvf/+r48ePq2fPnrp165aGDRuW5j7h4eEKCwuzvY+PjydIBwAAQIb3UCH6ggUL9Mknn2jXrl3y9fVVgQIF5OHhoUuXLunEiRNyd3dXx44d9e6776po0aKOqhkAAACAk6WkpChfvnz617/+pSxZsiggIEBnz57Vhx9+aBqiW61WWa3Wx1wpAAAA8HAeOESvVq2a3Nzc1LlzZy1fvjzVEySJiYmKjIzU4sWLVb16dU2fPl1t27Z96IIBAAAA2EtMTHyocDpPnjzKkiWLYmJi7NpjYmLk5+eX5j758+eXq6ursmTJYmsrV66coqOjlZSUJDc3tweuBwAAAMhIHnhN9HHjxmnHjh3q2bNnmh/BtFqtatCggWbOnKnDhw+rRIkSD1UoAAAAgD9988036tSpk0qUKCFXV1dly5ZN3t7eql+/vsaMGaNz5879rfHc3NwUEBCgjRs32tpSUlK0ceNGBQUFpblP7dq1dfz4caWkpNjajh49qvz58xOgAwAAIFN54BA9JCQk3X1z586tgICABz0UAAAAAEkrV65UmTJl1LVrV2XNmlXvvvuuVqxYoXXr1umzzz5T/fr1tWHDBpUoUUJvvvmmLly4kO6xw8LCNGvWLH3xxRc6dOiQ3nrrLSUkJKhLly6SpNDQUIWHh9v6v/XWW7p06ZL69euno0eP6uuvv9bYsWPVq1cvh583AAAA4EwO+WLRPXv2yNXVVZUqVZIkrV69WnPmzFH58uU1fPhwnkQBAAAAHGDChAmaNGmSmjRpIheX1M/DtGvXTpJ09uxZ/fOf/9T8+fM1YMCAdI39yiuv6MKFCxo6dKiio6NVtWpVrV271vZlo1FRUXbHLFy4sNatW6cBAwaocuXKKliwoPr166d3333XAWcKAAAAZBwP/CT63d544w0dPXpUknTy5Em1b99e2bJl09KlS/XOO++ke5zhw4fLYrHYvfz9/W3bb968qV69eil37tzy8vJSmzZtUq3bCAAAAGRWkZGRatq0aZoB+t0KFiyocePGpTtAv6N37946ffq0EhMTtWPHDgUGBtq2bdmyRXPnzrXrHxQUpO3bt+vmzZs6ceKE3n//fbs10gEAAIDMwCEh+tGjR1W1alVJ0tKlS1WvXj0tXLhQc+fO1fLly//WWBUqVND58+dtr++//962bcCAAfrqq6+0dOlSbd26VefOnVPr1q0dcQoAAAAAAAAAAKTikBDdMAzbFwpt2LBBL774oqQ/P+J58eLFvzVW1qxZ5efnZ3vlyZNHkhQXF6fZs2dr4sSJatiwoQICAjRnzhz9+OOP2r59uyNOAwAAAHjinTlzRl27dnV2GQAAAECm4ZAQvXr16ho9erT+/e9/a+vWrWratKkk6dSpU7Y1FNPr2LFjKlCggEqUKKGOHTsqKipKkrR7927dunVLwcHBtr7+/v4qUqSIIiMjTcdLTExUfHy83QsAAADIrC5duqQvvvjC2WUAAAAAmYZDvlh08uTJ6tixo1atWqXBgwerVKlSkqRly5apVq1a6R4nMDBQc+fOVdmyZXX+/HmNGDFCdevW1YEDBxQdHS03NzflyJHDbh9fX19FR0ebjhkREaERI0Y80HkBAAAAGc2aNWvuuf3kyZOPqRIAAADg6eCQEL1y5crav39/qvYPP/zwb32xUJMmTezGDAwMVNGiRfXll1/Kw8PjgWoLDw9XWFiY7X18fLwKFy78QGMBAAAAztaqVStZLBYZhmHax2KxPMaKAAAAgMzNIcu5mHF3d5erq+sD758jRw6VKVNGx48fl5+fn5KSknTlyhW7PjExMfLz8zMdw2q1ytvb2+4FAAAAPKny58+vFStWKCUlJc3Xnj17nF0iAAAAkKk8cIieM2dO5cqVK12vB3Xt2jWdOHFC+fPnV0BAgFxdXbVx40bb9iNHjigqKkpBQUEPfAwAAADgSRIQEKDdu3ebbr/fU+oAAAAA/p4HXs5l8uTJtv/+448/NHr0aIWEhNgC7cjISK1bt05DhgxJ95gDBw5U8+bNVbRoUZ07d07Dhg1TlixZ1KFDB/n4+Khbt24KCwtTrly55O3trT59+igoKEg1a9Z80NMAAAAAniiDBg1SQkKC6fZSpUpp8+bNj7EiAAAAIHN74BC9U6dOtv9u06aNRo4cqd69e9va+vbtq6lTp2rDhg0aMGBAusb8/fff1aFDB/3xxx/Kmzev6tSpo+3btytv3rySpEmTJsnFxUVt2rRRYmKiQkJCNH369Ac9BQAAAOCJU7du3Xtu9/T0VP369R9TNQAAAEDm55AvFl23bp3Gjx+fqv2FF17Qe++9l+5xFi9efM/t7u7umjZtmqZNm/a3awQAAAAyq0WLFqlFixby9PR0dikAAABApuOQLxbNnTu3Vq9enap99erVyp07tyMOAQAAAMDEG2+8oZiYGGeXAQAAAGRKDnkSfcSIEXr99de1ZcsWBQYGSpJ27NihtWvXatasWY44BAAAAAATfJEoAAAA8Og4JETv3LmzypUrpylTpmjFihWSpHLlyun777+3heoAAAAAAAAAADxpHBKiS1JgYKAWLFjgqOEAAAAApNM333yjggULOrsMAAAAIFNyWIiekpKi48ePKzY2VikpKXbb6tWr56jDAAAAAPifuLg4RUdHK2/evLp586asVquzSwIAAAAyHYeE6Nu3b9err76q06dPp1qP0WKxKDk52RGHAQAAACDps88+08SJE3XkyBG79rJly+rtt99Wt27dnFQZAAAAkPk4JER/8803Vb16dX399dfKnz+/LBaLI4YFAAAA8Bcffvihhg8frr59+yokJES+vr6SpJiYGH377bfq16+fLl++rIEDBzq5UgAAACBzcEiIfuzYMS1btkylSpVyxHAAAAAATEydOlVz5sxRu3bt7NrLlSunBg0aqEqVKho0aBAhOgAAAOAgLo4YJDAwUMePH3fEUAAAAADuITY2VpUqVTLdXqlSJV28ePExVgQAAABkbg55Er1Pnz56++23FR0drUqVKsnV1dVue+XKlR1xGAAAAOCpV6NGDY0bN06zZ89W1qz20/nk5GSNHz9eNWrUcFJ1AAAAQObjkBC9TZs2kqSuXbva2iwWiwzD4ItFAQAAAAeaOnWqQkJC5Ofnp3r16tmtif7dd9/Jzc1N3377rZOrBAAAADIPh4Top06dcsQwAAAAAO6jcuXKOnr0qObPn6/t27fr5MmTkiQ/Pz+NHj1ar776qry9vZ1cJQAAAJB5OCREL1q0qCOGAQAAAJAO2bNn11tvvaW33nrL2aUAAAAAmZ5DQnRJOnHihCZPnqxDhw5JksqXL69+/fqpZMmSjjoEAAAA8FRLSEiQp6fnI+sPAAAAIDUXRwyybt06lS9fXjt37lTlypVVuXJl7dixQxUqVND69esdcQgAAADgqVeqVCmNGzdO58+fN+1jGIbWr1+vJk2aaMqUKY+xOgAAACBzcsiT6O+9954GDBigcePGpWp/99131bhxY0ccBgAAAHiqbdmyRe+//76GDx+uKlWqqHr16ipQoIDc3d11+fJlHTx4UJGRkcqaNavCw8P1xhtvOLtkAAAA4InnkBD90KFD+vLLL1O1d+3aVZMnT3bEIQAAAICnXtmyZbV8+XJFRUVp6dKl2rZtm3788UfduHFDefLkUbVq1TRr1iw1adJEWbJkcXa5AAAAQKbgkBA9b9682rt3r0qXLm3XvnfvXuXLl88RhwAAAADwP0WKFNHbb7+tt99+29mlAAAAAJmeQ0L07t27q0ePHjp58qRq1aolSfrhhx80fvx4hYWFOeIQAAAAAAAAAAA8dg4J0YcMGaLs2bPr448/Vnh4uCSpQIECGj58uPr27euIQ2RKAYPmObsEZDCW20nyuet9gyGLZWR1c1o9yJh2fxjq7BIAAAAAAACeGg4J0S0WiwYMGKABAwbo6tWrkqTs2bM7YmgAAAAAAAAAAJzGISH6qVOndPv2bZUuXdouPD927JhcXV1VrFgxRxwGAAAAAAAAAIDHysURg3Tu3Fk//vhjqvYdO3aoc+fOjjgEAAAAAAAAAACPnUNC9J9//lm1a9dO1V6zZk3t3bvXEYcAAAAAcJdixYpp5MiRioqKcnYpAAAAQKbmkBDdYrHY1kK/W1xcnJKTkx1xCAAAAAB36d+/v1asWKESJUqocePGWrx4sRITE51dFgAAAJDpOCREr1evniIiIuwC8+TkZEVERKhOnTqOOAQAAACAu/Tv31979+7Vzp07Va5cOfXp00f58+dX7969tWfPHmeXBwAAAGQaDvli0fHjx6tevXoqW7as6tatK0natm2b4uPjtWnTJkccAgAAAEAannnmGT3zzDP6+OOPNX36dL377ruaMWOGKlWqpL59+6pLly6yWCzOLhMAAAB4YjnkSfTy5ctr3759ateunWJjY3X16lWFhobq8OHDqlixoiMOAQAAACANt27d0pdffqkWLVro7bffVvXq1fXZZ5+pTZs2ev/999WxY0dnlwgAAAA80RzyJLokFShQQGPHjnXUcAAAAADuYc+ePZozZ44WLVokFxcXhYaGatKkSfL397f1eemll1SjRg0nVgkAAAA8+RzyJLr05/Itr732mmrVqqWzZ89Kkv7973/r+++/d9QhAAAAAPxPjRo1dOzYMc2YMUNnz57VRx99ZBegS1Lx4sXVvn17J1UIAAAAZA4OCdGXL1+ukJAQeXh4aM+ePUpMTJQkxcXF8XQ6AAAA8AicPHlSa9euVdu2beXq6ppmH09PT82ZM+cxVwYAAABkLg4J0UePHq2ZM2dq1qxZdhP42rVra8+ePY44BAAAAIC7xMbGaseOHanad+zYoV27djmhIgAAACBzckiIfuTIEdWrVy9Vu4+Pj65cueKIQwAAAAC4S69evXTmzJlU7WfPnlWvXr2cUBEAAACQOTkkRPfz89Px48dTtX///fcqUaKEIw4BAAAA4C4HDx7UM888k6q9WrVqOnjwoBMqAgAAADInh4To3bt3V79+/bRjxw5ZLBadO3dOCxYs0MCBA/XWW2854hAAAAAA7mK1WhUTE5Oq/fz588qaNasTKgIAAAAyJ4fMrt977z2lpKSoUaNGun79uurVqyer1aqBAweqT58+jjgEAAAAgLs8//zzCg8P1+rVq+Xj4yNJunLlit5//301btzYydUBAAAAmYdDQnSLxaLBgwdr0KBBOn78uK5du6by5cvLy8vLEcMDAAAA+IuPPvpI9erVU9GiRVWtWjVJ0t69e+Xr66t///vfTq4OAAAAyDwc+jlPNzc3lS9fXvHx8dqwYYPKli2rcuXKOfIQAAAAACQVLFhQ+/bt04IFC/TLL7/Iw8NDXbp0UYcOHeTq6urs8gAAAIBMwyEhert27VSvXj317t1bN27cUI0aNXTq1CkZhqHFixerTZs2jjgMAAAAgLt4enqqR48ezi4DAAAAyNQcEqJ/9913Gjx4sCRp5cqVSklJ0ZUrV/TFF19o9OjRhOgAAADAI3Lw4EFFRUUpKSnJrr1FixZOqggAAADIXBwSosfFxSlXrlySpLVr16pNmzbKli2bmjZtqkGDBjniEAAAAADucvLkSb300kvav3+/LBaLDMOQ9Of3FUlScnKyM8sDAAAAMg0XRwxSuHBhRUZGKiEhQWvXrtXzzz8vSbp8+bLc3d0dcQgAAAAAd+nXr5+KFy+u2NhYZcuWTb/++qu+++47Va9eXVu2bHF2eQAAAECm4ZAn0fv376+OHTvKy8tLRYsWVYMGDST9ucxLpUqVHHEIAAAAAHeJjIzUpk2blCdPHrm4uMjFxUV16tRRRESE+vbtq59//tnZJQIAAACZgkNC9J49eyowMFBRUVFq3LixXFz+fMC9RIkSGj16tCMOAQAAAOAuycnJyp49uyQpT548OnfunMqWLauiRYvqyJEjTq4OAAAAyDwcEqJLUkBAgAICAuzamjZt6qjhAQAAANylYsWK+uWXX1S8eHEFBgZqwoQJcnNz07/+9S+VKFHC2eUBAAAAmcYDr4k+btw43bhxI119d+zYoa+//vpBDwUAAADgLz744AOlpKRIkkaOHKlTp06pbt26+u9//6spU6Y4uToAAAAg83jgJ9EPHjyoIkWKqG3btmrevLmqV6+uvHnzSpJu376tgwcP6vvvv9f8+fN17tw5zZs3z2FFAwAAAE+7kJAQ23+XKlVKhw8f1qVLl5QzZ05ZLBYnVgYAAABkLg/8JPq8efO0YcMG3bp1S6+++qr8/Pzk5uam7Nmzy2q1qlq1avr8888VGhqqw4cPq169eo6sGwAAAHhq3bp1S1mzZtWBAwfs2nPlykWADgAAADjYQ62JXqVKFc2aNUuffvqp9u3bp9OnT+vGjRvKkyePqlatqjx58jiqTgAAAAD/4+rqqiJFiig5OdnZpQAAAACZ3gM/iW43iIuLqlatqpYtW6p9+/YKDg52SIA+btw4WSwW9e/f39Z28+ZN9erVS7lz55aXl5fatGmjmJiYhz4WAAAA8CQZPHiw3n//fV26dMnZpQAAAACZ2kM9if4o/fTTT/r0009VuXJlu/YBAwbo66+/1tKlS+Xj46PevXurdevW+uGHH5xUKQAAAPD4TZ06VcePH1eBAgVUtGhReXp62m3fs2ePkyoDAAAAMpcMGaJfu3ZNHTt21KxZszR69Ghbe1xcnGbPnq2FCxeqYcOGkqQ5c+aoXLly2r59u2rWrJlqrMTERCUmJtrex8fHP/oTAAAAAB6xVq1aObsEAAAA4KmQIUP0Xr16qWnTpgoODrYL0Xfv3q1bt24pODjY1ubv768iRYooMjIyzRA9IiJCI0aMeCx1AwAAAI/LsGHDnF0CAAAA8FRwyJrojrR48WLt2bNHERERqbZFR0fLzc1NOXLksGv39fVVdHR0muOFh4crLi7O9jpz5syjKBsAAAAAAAAAkAk59En048eP68SJE6pXr548PDxkGIYsFku69z9z5oz69eun9evXy93d3SE1Wa1WWa1Wh4wFAAAAZBQuLi73nGsnJyc/xmoAAACAzMshIfoff/yhV155RZs2bZLFYtGxY8dUokQJdevWTTlz5tTHH3+crnF2796t2NhYPfPMM7a25ORkfffdd5o6darWrVunpKQkXblyxe5p9JiYGPn5+TniVAAAAIAnwsqVK+3e37p1Sz///LO++OILljMEAAAAHMghIfqAAQOUNWtWRUVFqVy5crb2V155RWFhYekO0Rs1aqT9+/fbtXXp0kX+/v569913VbhwYbm6umrjxo1q06aNJOnIkSOKiopSUFCQI04FAAAAeCK0bNkyVdvLL7+sChUqaMmSJerWrZsTqgIAAAAyH4eE6N9++63WrVunQoUK2bWXLl1ap0+fTvc42bNnV8WKFe3aPD09lTt3blt7t27dFBYWply5csnb21t9+vRRUFBQml8qCgAAADxtatasqR49eji7DAAAACDTcEiInpCQoGzZsqVqv3TpksPXI580aZJcXFzUpk0bJSYmKiQkRNOnT3foMQAAAIAn0Y0bNzRlyhQVLFjQ2aUAAAAAmYZDQvS6detq3rx5GjVqlCTJYrEoJSVFEyZM0HPPPfdQY2/ZssXuvbu7u6ZNm6Zp06Y91LgAAADAkyxnzpx2XyxqGIauXr2qbNmyaf78+U6sDAAAAMhcHBKiT5gwQY0aNdKuXbuUlJSkd955R7/++qsuXbqkH374wRGHAAAAAHCXSZMm2YXoLi4uyps3rwIDA5UzZ04nVgYAAABkLg4J0StWrKijR49q6tSpyp49u65du6bWrVurV69eyp8/vyMOAQAAAOAunTt3dnYJAAAAwFPBISG6JPn4+Gjw4MGOGg4AAADAPcyZM0deXl5q27atXfvSpUt1/fp1derUyUmVAQAAAJmLw0L0mzdvat++fYqNjVVKSordthYtWjjqMAAAAAAkRURE6NNPP03Vni9fPvXo0YMQHQAAAHAQh4Toa9euVWhoqC5evJhqm8ViUXJysiMOAwAAAOB/oqKiVLx48VTtRYsWVVRUlBMqAgAAADInF0cM0qdPH7Vt21bnz59XSkqK3YsAHQAAAHC8fPnyad++fanaf/nlF+XOndsJFQEAAACZk0NC9JiYGIWFhcnX19cRwwEAAAC4jw4dOqhv377avHmzkpOTlZycrE2bNqlfv35q3769s8sDAAAAMg2HLOfy8ssva8uWLSpZsqQjhgMAAABwH6NGjdJvv/2mRo0aKWvWP6f1KSkpCg0N1dixY51cHQAAAJB5OCREnzp1qtq2batt27apUqVKcnV1tdvet29fRxwGAAAAwP+4ublpyZIlGj16tPbu3SsPDw9VqlRJRYsWdXZpAAAAQKbikBB90aJF+vbbb+Xu7q4tW7bIYrHYtlksFkJ0AAAA4BEpXbq0Spcu7ewyAAAAgEzLIWuiDx48WCNGjFBcXJx+++03nTp1yvY6efKkIw4BAAAA4C5t2rTR+PHjU7VPmDBBbdu2dUJFAAAAQObkkBA9KSlJr7zyilxcHDIcAAAAgPv47rvv9OKLL6Zqb9Kkib777jsnVAQAAABkTg5JvTt16qQlS5Y4YigAAAAA6XDt2jW5ubmland1dVV8fLwTKgIAAAAyJ4esiZ6cnKwJEyZo3bp1qly5cqovFp04caIjDgMAAADgfypVqqQlS5Zo6NChdu2LFy9W+fLlnVQVAAAAkPk4JETfv3+/qlWrJkk6cOCA3ba7v2QUAAAAgGMMGTJErVu31okTJ9SwYUNJ0saNG7Vo0SItXbrUydUBAAAAmYdDQvTNmzc7YhgAAAAA6dS8eXOtWrVKY8eO1bJly+Th4aHKlStrw4YNql+/vrPLAwAAADINh4ToAAAAAB6/pk2bqmnTpqnaDxw4oIoVKzqhIgAAACDzeeAQvXXr1po7d668vb3VunXre/ZdsWLFgx4GAAAAQDpcvXpVixYt0meffabdu3crOTnZ2SUBAAAAmcIDh+g+Pj629c59fHwcVhAAAACA9Pvuu+/02WefacWKFSpQoIBat26tadOmObssAAAAINN44BB9zpw5GjlypAYOHKg5c+Y4siYAAAAA9xAdHa25c+dq9uzZio+PV7t27ZSYmKhVq1apfPnyzi4PAAAAyFRcHmbnESNG6Nq1a46qBQAAAMB9NG/eXGXLltW+ffs0efJknTt3Tv/85z+dXRYAAACQaT1UiG4YhqPqAAAAAJAO33zzjbp166YRI0aoadOmypIli8PGnjZtmooVKyZ3d3cFBgZq586d6dpv8eLFslgsatWqlcNqAQAAADKKhwrRJdnWRQcAAADw6H3//fe6evWqAgICFBgYqKlTp+rixYsPPe6SJUsUFhamYcOGac+ePapSpYpCQkIUGxt7z/1+++03DRw4UHXr1n3oGgAAAICM6KFD9DJlyihXrlz3fAEAAABwjJo1a2rWrFk6f/683njjDS1evFgFChRQSkqK1q9fr6tXrz7QuBMnTlT37t3VpUsXlS9fXjNnzlS2bNn0+eefm+6TnJysjh07asSIESpRosR9j5GYmKj4+Hi7FwAAAJDRPfAXi94xYsQI+fj4OKIWAAAAAOnk6emprl27qmvXrjpy5Ihmz56tcePG6b333lPjxo21Zs2adI+VlJSk3bt3Kzw83Nbm4uKi4OBgRUZGmu43cuRI5cuXT926ddO2bdvue5yIiAiNGDEi3XUBAAAAGcFDh+jt27dXvnz5HFELAAAAgAdQtmxZTZgwQREREfrqq6/u+fR4Wi5evKjk5GT5+vratfv6+urw4cNp7vP9999r9uzZ2rt3b7qPEx4errCwMNv7+Ph4FS5c+G/VCgAAADxuDxWisx46AAAAkHFkyZJFrVq1euRf8Hn16lX94x//0KxZs5QnT55072e1WmW1Wh9hZQAAAIDjPVSIbhiGo+oAAAAA4CR58uRRlixZFBMTY9ceExMjPz+/VP1PnDih3377Tc2bN7e1paSkSJKyZs2qI0eOqGTJko+2aAAAAOAxeagvFk1JSWEpFwAAAOAJ5+bmpoCAAG3cuNHWlpKSoo0bNyooKChVf39/f+3fv1979+61vVq0aKHnnntOe/fuZYkWAAAAZCoPvSY6AAAAgCdfWFiYOnXqpOrVq+vZZ5/V5MmTlZCQoC5dukiSQkNDVbBgQUVERMjd3V0VK1a02z9HjhySlKodAAAAeNIRogMAAADQK6+8ogsXLmjo0KGKjo5W1apVtXbtWtuXjUZFRcnF5aE+yAoAAAA8kQjRAQAAAEiSevfurd69e6e5bcuWLffcd+7cuY4vCAAAAMgAeJQEAAAAAAAAAAAThOgAAAAAAAAAAJggRAcAAAAAAAAAwAQhOgAAAAAAAAAAJgjRAQAAAAAAAAAwQYgOAAAAAAAAAIAJQnQAAAAAAAAAAEwQogMAAAAAAAAAYIIQHQAAAAAAAAAAE4ToAAAAAAAAAACYIEQHAAAAAAAAAMAEIToAAAAAAAAAACYI0QEAAAAAAAAAMEGIDgAAAAAAAACACUJ0AAAAAAAAAABMEKIDAAAAAAAAAGAiQ4XoM2bMUOXKleXt7S1vb28FBQXpm2++sW2/efOmevXqpdy5c8vLy0tt2rRRTEyMEysGAAAAAAAAAGRmGSpEL1SokMaNG6fdu3dr165datiwoVq2bKlff/1VkjRgwAB99dVXWrp0qbZu3apz586pdevWTq4aAAAAAAAAAJBZZXV2AXdr3ry53fsxY8ZoxowZ2r59uwoVKqTZs2dr4cKFatiwoSRpzpw5KleunLZv366aNWumOWZiYqISExNt7+Pj4x/dCQAAAAAAAAAAMpUM9ST63ZKTk7V48WIlJCQoKChIu3fv1q1btxQcHGzr4+/vryJFiigyMtJ0nIiICPn4+NhehQsXfhzlAwAAAAAAAAAygQwXou/fv19eXl6yWq168803tXLlSpUvX17R0dFyc3NTjhw57Pr7+voqOjradLzw8HDFxcXZXmfOnHnEZwAAAAAAAAAAyCwy1HIuklS2bFnt3btXcXFxWrZsmTp16qStW7c+8HhWq1VWq9WBFQIAAAAAAAAAnhYZLkR3c3NTqVKlJEkBAQH66aef9Mknn+iVV15RUlKSrly5Yvc0ekxMjPz8/JxULQAAAAAAAAAgM8twy7n8VUpKihITExUQECBXV1dt3LjRtu3IkSOKiopSUFCQEysEAAAAAAAAAGRWGepJ9PDwcDVp0kRFihTR1atXtXDhQm3ZskXr1q2Tj4+PunXrprCwMOXKlUve3t7q06ePgoKCVLNmTWeXDgAAAAAAAADIhDJUiB4bG6vQ0FCdP39ePj4+qly5statW6fGjRtLkiZNmiQXFxe1adNGiYmJCgkJ0fTp051cNQAAAAAAAAAgs8pQIfrs2bPvud3d3V3Tpk3TtGnTHlNFAAAAAAAAAICnWYZfEx0AAAAAAAAAAGchRAcAAAAAAAAAwAQhOgAAAAAAAAAAJgjRAQAAAAAAAAAwQYgOAAAAAAAAAIAJQnQAAAAAAAAAAEwQogMAAAAAAAAAYIIQHQAAAAAAAAAAE4ToAAAAAAAAAACYIEQHAAAAAAAAAMAEIToAAAAAAAAAACYI0QEAAAAAAAAAMEGIDgAAAAAAAACACUJ0AAAAAAAAAABMEKIDAAAAAAAAAGCCEB0AAAAAAAAAABOE6AAAAAAAAAAAmCBEBwAAAAAAAADABCE6AAAAAAAAAAAmCNEBAAAAAAAAADBBiA4AAAAAAAAAgAlCdAAAAAAAAAAATBCiAwAAAAAAAABgghAdAAAAAAAAAAAThOgAAAAAAAAAAJggRAcAAAAAAAAAwAQhOgAAAAAAAAAAJgjRAQAAAAAAAAAwQYgOAAAAAAAAAIAJQnQAAAAAAAAAAEwQogMAAAAAAAAAYIIQHQAAAAAAAAAAE4ToAAAAAAAAAACYIEQHAAAAAAAAAMAEIToAAAAAAAAAACYI0QEAAAAAAAAAMEGIDgAAAAAAAACACUJ0AAAAAAAAAABMEKIDAAAAAAAAAGCCEB0AAAAAAAAAABOE6AAAAAAAAAAAmCBEBwAAAAAAAADABCE6AAAAAAAAAAAmCNEBAAAAAAAAADBBiA4AAAAAAAAAgAlCdAAAAAAAAAAATBCiAwAAAAAAAABgghAdAAAAAAAAAAATGSpEj4iIUI0aNZQ9e3bly5dPrVq10pEjR+z63Lx5U7169VLu3Lnl5eWlNm3aKCYmxkkVAwAAAAAAAAAyswwVom/dulW9evXS9u3btX79et26dUvPP/+8EhISbH0GDBigr776SkuXLtXWrVt17tw5tW7d2olVAwAAAAAAAAAyq6zOLuBua9eutXs/d+5c5cuXT7t371a9evUUFxen2bNna+HChWrYsKEkac6cOSpXrpy2b9+umjVrOqNsAAAAAAAAAEAmlaGeRP+ruLg4SVKuXLkkSbt379atW7cUHBxs6+Pv768iRYooMjIyzTESExMVHx9v9wIAAAAAAAAAID0ybIiekpKi/v37q3bt2qpYsaIkKTo6Wm5ubsqRI4ddX19fX0VHR6c5TkREhHx8fGyvwoULP+rSAQAAAAAAAACZRIYN0Xv16qUDBw5o8eLFDzVOeHi44uLibK8zZ844qEIAAAAAAAAAQGaXodZEv6N37976z3/+o++++06FChWytfv5+SkpKUlXrlyxexo9JiZGfn5+aY5ltVpltVofdckAAAAAAAAAgEwoQz2JbhiGevfurZUrV2rTpk0qXry43faAgAC5urpq48aNtrYjR44oKipKQUFBj7tcAAAAAAAAAEAml6GeRO/Vq5cWLlyo1atXK3v27LZ1zn18fOTh4SEfHx9169ZNYWFhypUrl7y9vdWnTx8FBQWpZs2aTq4eAAAAAAAAAJDZZKgQfcaMGZKkBg0a2LXPmTNHnTt3liRNmjRJLi4uatOmjRITExUSEqLp06c/5koBAAAAAAAAAE+DDBWiG4Zx3z7u7u6aNm2apk2b9hgqAh4vI4ur4ip3sHsPAAAAAAAAwHkyVIgOPPUsFhlZ3ZxdBQAAAAAAAID/yVBfLAoAAAAAAAAAQEZCiA4AAAAAAAAAgAlCdAAAAAAAAAAATBCiAwAAAAAAAABgghAdAAAAgCRp2rRpKlasmNzd3RUYGKidO3ea9p01a5bq1q2rnDlzKmfOnAoODr5nfwAAAOBJRYgOAAAAQEuWLFFYWJiGDRumPXv2qEqVKgoJCVFsbGya/bds2aIOHTpo8+bNioyMVOHChfX888/r7Nmzj7lyAAAA4NEiRAcAAACgiRMnqnv37urSpYvKly+vmTNnKlu2bPr888/T7L9gwQL17NlTVatWlb+/vz777DOlpKRo48aNj7lyAAAA4NEiRAcAAACecklJSdq9e7eCg4NtbS4uLgoODlZkZGS6xrh+/bpu3bqlXLlymfZJTExUfHy83QsAAADI6AjRAQAAgKfcxYsXlZycLF9fX7t2X19fRUdHp2uMd999VwUKFLAL4v8qIiJCPj4+tlfhwoUfqm4AAADgcSBEBwAAAPBQxo0bp8WLF2vlypVyd3c37RceHq64uDjb68yZM4+xSgAAAODBZHV2AQAAAACcK0+ePMqSJYtiYmLs2mNiYuTn53fPfT/66CONGzdOGzZsUOXKle/Z12q1ymq1PnS9AAAAwOPEk+gAAADAU87NzU0BAQF2Xwp650tCg4KCTPebMGGCRo0apbVr16p69eqPo1QAAADgseNJdAAAAAAKCwtTp06dVL16dT377LOaPHmyEhIS1KVLF0lSaGioChYsqIiICEnS+PHjNXToUC1cuFDFihWzrZ3u5eUlLy8vp50HAAAA4GiE6AAAAAD0yiuv6MKFCxo6dKiio6NVtWpVrV271vZlo1FRUXJx+f8Pss6YMUNJSUl6+eWX7cYZNmyYhg8f/jhLBwAAAB4pQnQAAAAAkqTevXurd+/eaW7bsmWL3fvffvvt0RcEAAAAZACsiQ4AAAAAAAAAgAlCdAAAAAAAAAAATBCiAwAAAAAAAABgghAdAAAAAAAAAAAThOgAAAAAAAAAAJggRAcAAAAAAAAAwAQhOgAAAAAAAAAAJgjRAQAAAAAAAAAwQYgOAAAAAAAAAIAJQnQAAAAAAAAAAEwQogMAAAAAAAAAYIIQHQAAAAAAAAAAE4ToAAAAAAAAAACYIEQHAAAAAAAAAMAEIToAAAAAAAAAACYI0QEAAAAAAAAAMEGIDgAAAAAAAACACUJ0AAAAAAAAAABMEKIDAAAAAAAAAGCCEB0AAAAAAAAAABOE6AAAAAAAAAAAmCBEBwAAAAAAAADABCE6AAAAAAAAAAAmCNEBAAAAAAAAADBBiA4AAAAAAAAAgAlCdAAAAAAAAAAATBCiAwAAAAAAAABgghAdAAAAAAAAAAAThOgAAAAAAAAAAJggRAcAAAAAAAAAwAQhOgAAAAAAAAAAJgjRAQAAAAAAAAAwkaFC9O+++07NmzdXgQIFZLFYtGrVKrvthmFo6NChyp8/vzw8PBQcHKxjx445p1gAAAAAAAAAQKaXoUL0hIQEValSRdOmTUtz+4QJEzRlyhTNnDlTO3bskKenp0JCQnTz5s3HXCkAAAAAAAAA4GmQ1dkF3K1JkyZq0qRJmtsMw9DkyZP1wQcfqGXLlpKkefPmydfXV6tWrVL79u0fZ6kAAAAAAAAAgKdAhnoS/V5OnTql6OhoBQcH29p8fHwUGBioyMhI0/0SExMVHx9v9wIAAAAAAAAAID2emBA9OjpakuTr62vX7uvra9uWloiICPn4+NhehQsXfqR1AgAAAAAAAAAyjycmRH9Q4eHhiouLs73OnDnj7JIAAAAAAAAAAE+IJyZE9/PzkyTFxMTYtcfExNi2pcVqtcrb29vuBQAAAAAAAABAejwxIXrx4sXl5+enjRs32tri4+O1Y8cOBQUFObEyAAAAAAAAAEBmldXZBdzt2rVrOn78uO39qVOntHfvXuXKlUtFihRR//79NXr0aJUuXVrFixfXkCFDVKBAAbVq1cp5RQMAAAAAAAAAMq0MFaLv2rVLzz33nO19WFiYJKlTp06aO3eu3nnnHSUkJKhHjx66cuWK6tSpo7Vr18rd3d1ZJQMAAAAAAAAAMrEMFaI3aNBAhmGYbrdYLBo5cqRGjhz5GKsCAABPCsMwlJCQYHvv6ekpi8XixIoAAAAAAE+6DBWiAwAAPIyEhAS1bNnS9n716tXy8vJyYkUAAAAAgCfdE/PFogAAAAAAAAAAPG6E6AAAAAAAAAAAmCBEBwAAAAAAAADABCE6AAAAAAAAAAAmCNEBAAAAAAAAADBBiA4AAAAAAAAAgImszi4AAIAHFTBonrNLQAZjuZ0kn7veNxiyWEZWN6fVg4xp94ehzi4BAAAAwBOEJ9EBAAAAAAAAADBBiA4AAAAAAAAAgAlCdAAAAAAAAAAATBCiAwAAAAAAAABgghAdAAAAAAAAAAAThOgAAAAAAAAAAJggRAcAAAAAAAAAwAQhOgAAAAAAAAAAJgjRAQAAAAAAAAAwQYgOAAAAAAAAAICJrM4uAAAAwFGMLK6Kq9zB7j0AAAAAAA+DEB0AAGQeFouMrG7OrgIAAAAAkImwnAsAAAAAAAAAACYI0QEAAAAAAAAAMEGIDgAAAAAAAACACUJ0AAAAAAAAAABMEKIDAAAAAAAAAGCCEB0AAAAAAAAAABOE6AAAAAAAAAAAmCBEBwAAAAAAAADABCE6AAAAAAAAAAAmCNEBAAAAAAAAADBBiA4AAAAAAAAAgAlCdAAAAAAAAAAATBCiAwAAAAAAAABgghAdAAAAAAAAAAAThOgAAAAAAAAAAJggRAcAAAAAAAAAwAQhOgAAAAAAAAAAJgjRAQAAAAAAAAAwQYgOAAAAAAAAAIAJQnQAAAAAAAAAAEwQogMAAAAAAAAAYIIQHQAAAAAAAAAAE4ToAAAAAAAAAACYIEQHAAAAAAAAAMAEIToAAAAAAAAAACYI0QEAAAAAAAAAMEGIDgAAAAAAAACACUJ0AAAAAAAAAABMEKIDAAAAAAAAAGDiiQ3Rp02bpmLFisnd3V2BgYHauXOns0sCAAAAnmh/d469dOlS+fv7y93dXZUqVdJ///vfx1QpAAAA8Pg8kSH6kiVLFBYWpmHDhmnPnj2qUqWKQkJCFBsb6+zSAAAAgCfS/7F333FZ1f//x58XCBcKghtEUXEk7oGmaO6B5kxzVR9wZFlajrSiMldFVq5ykKZQjizLmam5cOQoV2qpmSNMBc0UnIhwfn/04/p6BUdBwQv1cb/dzu3zOe/zPu/rdY5I7+vpud5XZufYW7ZsUY8ePdSnTx/t3r1bHTt2VMeOHbV///57XDkAAACQvXI5uoA7MX78ePXt21e9evWSJEVERGj58uWaNWuWXn/9dbu+iYmJSkxMtO3Hx8dLkhISEu5dwSaSE686ugQA96Gc8Psrp+D3KIA74ejfo6mvbxiGQ+v4r8zMsSVp0qRJatWqlYYNGyZJGjNmjFavXq3JkycrIiIi3ddgbg7gQZITfnflJPweBZBZOeH3aIbn5sZ9JjEx0XB2djYWLVpk1x4SEmK0b98+Tf8RI0YYktjY2NjY2NjY2Nhy1HbixIl7NIO+vczOsQ3DMPz8/IwJEybYtb399ttG1apVTV+HuTkbGxsbGxsbG1tO3G43N7/vnkT/+++/lZycLG9vb7t2b29vHTx4ME3/sLAwDRkyxLafkpKif/75RwULFpTFYsn2eoHMSkhIkJ+fn06cOCFPT09HlwMA9x1+jyKnMwxDFy9elK+vr6NLscnsHFuSYmNj0+0fGxtr+jrMzXG/4b8pAHB3+D2KnC6jc/P7LkTPLKvVKqvVateWL18+xxQDZIKnpyf/gQGAu8DvUeRkXl5eji7BIZib437Ff1MA4O7wexQ5WUbm5vfdF4sWKlRIzs7OiouLs2uPi4uTj4+Pg6oCAAAA7l93Msf28fFhTg4AAICHwn0Xoru6uiowMFBr1661taWkpGjt2rUKCgpyYGUAAADA/elO5thBQUF2/SVp9erVzMkBAADwwLkvl3MZMmSIQkNDVatWLT366KOaOHGiLl++rF69ejm6NOCuWa1WjRgxIs1HnQEAGcPvUeDO3G6OHRISomLFiik8PFySNHDgQDVq1Ejjxo1TmzZtNH/+fO3YsUPTp0935GUAWYr/pgDA3eH3KB4UFsMwDEcXcScmT56sDz/8ULGxsapevbo+/vhj1alTx9FlAQAAAPetW82xGzdurFKlSikqKsrWf8GCBXrrrbd0/PhxlStXTh988IEef/xxB1UPAAAAZI/7NkQHAAAAAAAAACC73XdrogMAAAAAAAAAcK8QogMAAAAAAAAAYIIQHQAAAAAAAAAAE4ToQA5WqlQpTZw40bZvsVi0ePFih9UDAPejqKgo5cuXz9FlAADuY8zLAeDuMS/H/YwQHbgDPXv2lMVikcVikYuLi7y9vdWiRQvNmjVLKSkp2fa6p0+fVuvWrbNtfAC411J/n/br1y/Nsf79+8tisahnz573vjAAwH2BeTkAZA3m5cCtEaIDd6hVq1Y6ffq0jh8/rhUrVqhJkyYaOHCg2rZtqxs3bmTLa/r4+MhqtWbL2ADgKH5+fpo/f76uXr1qa7t27ZrmzZunEiVK3NXYSUlJd1teGoZhZNvveQBA5jEvB4CswbwcMEeIDtwhq9UqHx8fFStWTDVr1tQbb7yhJUuWaMWKFYqKipIkxcTEqEOHDvLw8JCnp6e6du2quLg4u3GWLVum2rVry83NTYUKFdITTzxh+po3f2z0+PHjslgsWrhwoZo0aaI8efKoWrVq2rp1q905M2bMkJ+fn/LkyaMnnnhC48eP5+NTAHKUmjVrys/PTwsXLrS1LVy4UCVKlFCNGjVsbStXrtRjjz2mfPnyqWDBgmrbtq2OHDliO576e/Grr75So0aN5Obmprlz56Z5vbNnz6pWrVp64oknlJiYqJSUFIWHh8vf31+5c+dWtWrV9M0339j6R0dHy2KxaMWKFQoMDJTVatXmzZuz6W4AADKLeTkAZA3m5YA5QnQgCzVt2lTVqlXTwoULlZKSog4dOuiff/7Rhg0btHr1ah09elTdunWz9V++fLmeeOIJPf7449q9e7fWrl2rRx99NFOv+eabb2ro0KHas2ePHnnkEfXo0cP2L7E//vij+vXrp4EDB2rPnj1q0aKF3n333Sy9ZgDICr1791ZkZKRtf9asWerVq5ddn8uXL2vIkCHasWOH1q5dKycnJz3xxBNpPq7/+uuva+DAgTpw4ICCg4Ptjp04cUINGjRQ5cqV9c0338hqtSo8PFxffPGFIiIi9Ouvv2rw4MF65plntGHDhjTjvv/++zpw4ICqVq2axXcAAJCVmJcDwJ1hXg6YMABkWmhoqNGhQ4d0j3Xr1s2oUKGC8cMPPxjOzs5GTEyM7divv/5qSDJ++uknwzAMIygoyHj66adNX6dkyZLGhAkTbPuSjEWLFhmGYRjHjh0zJBmfffZZmvEPHDhgq6VNmzZ2Yz799NOGl5dXJq4WALJP6u/TM2fOGFar1Th+/Lhx/Phxw83NzTh79qzRoUMHIzQ0NN1zz549a0gy9u3bZxjG//1enDhxol2/yMhIw8vLyzh48KDh5+dnvPzyy0ZKSophGIZx7do1I0+ePMaWLVvszunTp4/Ro0cPwzAMY/369YYkY/HixVl89QCAu8W8HACyBvNy4NZ4Eh3IYoZhyGKx6MCBA/Lz85Ofn5/tWMWKFZUvXz4dOHBAkrRnzx41a9bsrl7v5n91LVq0qCTpzJkzkqRDhw6leYIms0/UAMC9ULhwYbVp00ZRUVGKjIxUmzZtVKhQIbs+hw8fVo8ePVS6dGl5enqqVKlSkv79iP7NatWqlWb8q1evqkGDBurUqZMmTZoki8UiSfrjjz905coVtWjRQh4eHrbtiy++sPtIqtm4AICci3k5AGQe83IgfbkcXQDwoDlw4ID8/f0z1Dd37tx3/XouLi62/5/6H5//foQKAO4HvXv31oABAyRJU6ZMSXO8Xbt2KlmypGbMmCFfX1+lpKSocuXKun79ul0/d3f3NOdarVY1b95c3333nYYNG6ZixYpJki5duiTp34/xp7bdfM7txgUA5FzMywHgzjAvB9LiSXQgC61bt0779u1T586dVaFCBZ04cUInTpywHf/tt9904cIFVaxYUdK/T6usXbs22+opX768fv75Z7u2/+4DQE7RqlUrXb9+XUlJSWnWTDx37pwOHTqkt956S82aNVOFChV0/vz5DI/t5OSk2bNnKzAwUE2aNNGpU6ck/fskotVqVUxMjMqWLWu33fzEIgDg/sK8HADuHPNyIC2eRAfuUGJiomJjY5WcnKy4uDitXLlS4eHhatu2rUJCQuTk5KQqVaro6aef1sSJE3Xjxg29+OKLatSoke2jRyNGjFCzZs1UpkwZde/eXTdu3ND333+v1157LUtqfOmll9SwYUONHz9e7dq107p167RixQrbkzEAkJM4OzvbPlbv7Oxsdyx//vwqWLCgpk+frqJFiyomJkavv/56psefO3euevTooaZNmyo6Olo+Pj4aOnSoBg8erJSUFD322GOKj4/Xjz/+KE9PT4WGhmbZ9QEAsgfzcgDIWszLgbR4Eh24QytXrlTRokVVqlQptWrVSuvXr9fHH3+sJUuWyNnZWRaLRUuWLFH+/PnVsGFDNW/eXKVLl9ZXX31lG6Nx48ZasGCBli5dqurVq6tp06b66aefsqzG+vXrKyIiQuPHj1e1atW0cuVKDR48WG5ubln2GgCQlTw9PeXp6Zmm3cnJSfPnz9fOnTtVuXJlDR48WB9++GGmx8+VK5e+/PJLVapUSU2bNtWZM2c0ZswYDR8+XOHh4apQoYJatWql5cuXZ3gJAACAYzEvB4Csx7wcsGcxDMNwdBEA7p2+ffvq4MGD2rRpk6NLAQAAAB5azMsBALh/sJwL8ID76KOP1KJFC7m7u2vFihX6/PPPNXXqVEeXBQAAADxUmJcDAHD/4kl04AHXtWtXRUdH6+LFiypdurReeukl9evXz9FlAQAAAA8V5uUAANy/CNEBAAAAAAAAADDBF4sCAAAAAAAAAGCCEB0AAAAAAAAAABOE6AAAAAAAAAAAmCBEBwAAAAAAAADABCE6AAAAAAAAAAAmCNEBAAAAAAAAADBBiA4AD6CzZ8/qhRdeUIkSJWS1WuXj46Pg4GD9+OOPdz12z5491bFjx7svEgAAAHgIMDcHgPtfLkcXAADIep07d9b169f1+eefq3Tp0oqLi9PatWt17tw5R5cGAAAAPFSYmwPA/Y8n0QHgAXPhwgVt2rRJY8eOVZMmTVSyZEk9+uijCgsLU/v27dW7d2+1bdvW7pykpCQVKVJEM2fOlCR98803qlKlinLnzq2CBQuqefPmunz5skaOHKnPP/9cS5YskcVikcViUXR0tCTpxIkT6tq1q/Lly6cCBQqoQ4cOOn78uO01Up+See+99+Tt7a18+fJp9OjRunHjhoYNG6YCBQqoePHiioyMvFe3CgAAAMhWzM0B4MFAiA4ADxgPDw95eHho8eLFSkxMTHP82Wef1cqVK3X69Glb23fffacrV66oW7duOn36tHr06KHevXvrwIEDio6OVqdOnWQYhoYOHaquXbuqVatWOn36tE6fPq169eopKSlJwcHByps3rzZt2qQff/xRHh4eatWqla5fv257nXXr1unUqVPauHGjxo8frxEjRqht27bKnz+/tm/frn79+un555/XX3/9dU/uFQAAAJCdmJsDwIPBYhiG4egiAABZ69tvv1Xfvn119epV1axZU40aNVL37t1VtWpVSVKlSpUUGhqqV199VZLUvn17FSxYUJGRkdq1a5cCAwN1/PhxlSxZMs3YPXv21IULF7R48WJb25w5c/TOO+/owIEDslgskqTr168rX758Wrx4sVq2bKmePXsqOjpaR48elZPTv/+GGxAQoCJFimjjxo2SpOTkZHl5eemzzz5T9+7ds/MWAQAAAPcEc3MAuP/xJDoAPIA6d+6sU6dOaenSpWrVqpWio6NVs2ZNRUVFSfr3iZfUj2bGxcVpxYoV6t27tySpWrVqatasmapUqaIuXbpoxowZOn/+/C1f75dfftEff/yhvHnz2p62KVCggK5du6YjR47Y+lWqVMk2SZckb29vValSxbbv7OysggUL6syZM1l1KwAAAACHYm4OAPc/QnQAeEC5ubmpRYsWGj58uLZs2aKePXtqxIgRkqSQkBAdPXpUW7du1Zw5c+Tv768GDRpI+neyvHr1aq1YsUIVK1bUJ598ovLly+vYsWOmr3Xp0iUFBgZqz549dtvvv/+up556ytbPxcXF7jyLxZJuW0pKSlbdBgAAAMDhmJsDwP2NEB0AHhIVK1bU5cuXJUkFCxZUx44dFRkZqaioKPXq1cuur8ViUf369TVq1Cjt3r1brq6uWrRokSTJ1dVVycnJdv1r1qypw4cPq0iRIipbtqzd5uXldW8uEAAAALhPMDcHgPsLIToAPGDOnTunpk2bas6cOdq7d6+OHTumBQsW6IMPPlCHDh1s/Z599ll9/vnnOnDggEJDQ23t27dv13vvvacdO3YoJiZGCxcu1NmzZ1WhQgVJUqlSpbR3714dOnRIf//9t5KSkvT000+rUKFC6tChgzZt2qRjx44pOjpaL7/8Ml9EBAAAgIcWc3MAeDDkcnQBAICs5eHhoTp16mjChAk6cuSIkpKS5Ofnp759++qNN96w9WvevLmKFi2qSpUqydfX19bu6empjRs3auLEiUpISFDJkiU1btw4tW7dWpLUt29fRUdHq1atWrp06ZLWr1+vxo0ba+PGjXrttdfUqVMnXbx4UcWKFVOzZs3k6el5z+8BAAAAkBMwNweAB4PFMAzD0UUAAO69S5cuqVixYoqMjFSnTp0cXQ4AAADw0GJuDgA5G0+iA8BDJiUlRX///bfGjRunfPnyqX379o4uCQAAAHgoMTcHgPsDIToAPGRiYmLk7++v4sWLKyoqSrly8Z8CAAAAwBGYmwPA/YHlXAAAAAAAAAAAMOHk6AIAAAAAAAAAAMipCNEBAAAAAAAAADBBiA4AAAAAAAAAgAlCdAAAAAAAAAAATBCiAwAAAAAAAABgghAdAAAAAAAAAAAThOgAAAAAAAAAAJggRAcAAAAAAAAAwAQhOgAAAAAAAAAAJgjRAQAAAAAAAAAwQYgOAAAAAAAAAIAJQnQAAAAAAAAAAEwQogMAAAAAAAAAYIIQHQBw3+jZs6c8PDwcXQYAAABuYeTIkbJYLA57/Z49e6pUqVJ2bZcuXdKzzz4rHx8fWSwWDRo0SMePH5fFYlFUVNQ9ra9x48Zq3LjxPX1NPDj4+QEcgxAdgE1CQoLeffdd1apVS15eXrJarSpZsqS6deum5cuX2/WNjo6WxWKxbS4uLipdurRCQkJ09OjRNP2++eabdF9zwIABGZpg9+zZUxaLRZ6enrp69Wqa44cPH7bV8tFHH2Xyyh8MjRs3VuXKldM99vfff8tisWjkyJH3tqj7WFJSkj7++GPVrl1befPmlYeHh2rXrq2PP/5YSUlJ6Z6TnJysyMhINW7cWAUKFJDValWpUqXUq1cv7dixw9YvKirK7u9Prly5VKxYMfXs2VMnT57McI2bN29W69atVaxYMbm5ualEiRJq166d5s2bd9fXn54tW7Zo5MiRunDhQraMDwAAHOfs2bMaOHCgAgIClDt3bhUpUkSPPvqoXnvtNV26dMnR5aVx6tQpjRw5Unv27MlQ//fee09RUVF64YUXNHv2bP3vf//L0npSA3mz7f3338/S18tJ3nvvPS1evDjLxvvvXNnNzU2+vr4KDg7Wxx9/rIsXL97x2L/99ptGjhyp48ePZ1m96WHeDDx4cjm6AAB379dff1WNGjXk6uqa7vHr16/rwIEDKlOmjOkYf/zxh4KDg/Xnn3/qiSeeUEhIiDw8PHTixAl9//33atu2rb744os0k82XX35ZtWvXVlJSknbt2qXp06dr+fLl2rdvn3x9fbP0OnPlyqUrV65o2bJl6tq1q92xuXPnys3NTdeuXcvS18TD6fLly2rTpo02bNigtm3bqmfPnnJyctLKlSs1cOBALVy4UMuXL5e7u7vtnKtXr6pTp05auXKlGjZsqDfeeEMFChTQ8ePH9fXXX+vzzz9XTEyMihcvbjtn9OjR8vf317Vr17Rt2zZFRUVp8+bN2r9/v9zc3G5Z44IFC9StWzdVr15dAwcOVP78+XXs2DFt3LhRM2bM0FNPPZXl92XLli0aNWqUevbsqXz58mX5+AAA4M7c7fuBf/75R7Vq1VJCQoJ69+6tgIAAnTt3Tnv37tW0adP0wgsvZOrTgG+99ZZef/31O7qWjDp16pRGjRqlUqVKqXr16nbHZsyYoZSUFLu2devWqW7duhoxYoStzTAMXb16VS4uLllWV48ePfT444+naa9Ro0aWvUZO89577+nJJ59Ux44ds3Tc1LlyUlKSYmNjFR0drUGDBmn8+PFaunSpqlatmukxf/vtN40aNUqNGzdO82mFrMS8GXjwEKIDDwDDMPToo49q8+bN6R6vW7euDMMwPf/GjRt64oknFBcXpw0bNqh+/fp2x0eMGKEffvhBycnJac5t0KCBnnzySUlSr1699Mgjj+jll1/W559/rrCwsLu4qrSsVqvq16+vL7/8Mk2IPm/ePLVp00bffvttlr7mvXLlyhXlyZPH0WXg/xsyZIg2bNigTz75RAMGDLC1v/DCC5oyZYoGDBigoUOHatq0abZjw4YN08qVKzVhwgQNGjTIbrwRI0ZowoQJaV6ndevWqlWrliTp2WefVaFChTR27FgtXbo0zc/4f40cOVIVK1bUtm3b0rxhPnPmTGYvGQAA3Mfu9v3AzJkzFRMTox9//FH16tWzO5aQkGAazpvJlSuXcuVyXNyQXih+5swZVaxY0a4t9SnnrFSzZk0988wzWTrmw+rmubIkhYWFad26dWrbtq3at2+vAwcOKHfu3A6sEMDDhOVcAGjBggXav3+/hg8fniZAT9WyZUu1bt36tmM1bdpUknTs2LEsrTHVU089pRUrVth9LO7nn3/W4cOHTZ+8vXDhggYNGiQ/Pz9ZrVaVLVtWY8eOtXs6JfXjlx999JGmTJmi0qVLK0+ePGrZsqVOnDghwzA0ZswYFS9eXLlz51aHDh30zz//pHmtqVOnqlKlSrJarfL19VX//v3TfIQvddmVnTt3qmHDhsqTJ4/eeOMNhYaGqlChQukuFdKyZUuVL1/+zm6aiYsXL2rQoEEqVaqUrFarihQpohYtWmjXrl22Pps2bVKXLl1UokQJWa1W+fn5afDgwekuqbNgwQJVrFhRbm5uqly5shYtWpTuepQpKSmaOHGiKlWqJDc3N3l7e+v555/X+fPnM1z70aNHFRwcLHd3d/n6+mr06NG2N4aGYahUqVLq0KFDmvOuXbsmLy8vPf/886Zj//XXX5o5c6aaNm1qF6Cn6t+/v5o0aaLPPvtMf/31l+2cTz/9VC1atEgToEuSs7Ozhg4davcUenoaNGggSTpy5Mgt+6X2qV27drpvaosUKSIp8/fik08+UaVKlZQnTx7lz59ftWrVsi0NM3LkSA0bNkyS5O/vb/t47c0fhZ0zZ44CAwOVO3duFShQQN27d9eJEyfsXjf153/v3r1q1KiR8uTJo7Jly9qWfNqwYYPq1Kmj3Llzq3z58lqzZs1t7wUAALg7R44ckbOzs+rWrZvmmKenZ5qgefv27Xr88ceVP39+ubu7q2rVqpo0aZLtuNma6JmZK/z2229q0qSJ8uTJo2LFiumDDz6w9YmOjlbt2rUl/fsgT+q8JHVt85vnoKnLSx47dkzLly+3m8OYrYl+8OBBde3aVYULF7bNSd58880M3887kZiYqBEjRqhs2bK2eferr76qxMREu34Wi0UDBgywzb1z586toKAg7du3T5L06aefqmzZsnJzc1Pjxo3TXbZk+/btatWqlby8vJQnTx41atRIP/74o12f1D/DP/74w/Y0tZeXl3r16qUrV67Y1XP58mV9/vnntnvbs2dPSRl7v5FZTZs21fDhw/Xnn39qzpw5dscOHjyoJ598UgUKFJCbm5tq1aqlpUuX2o5HRUWpS5cukqQmTZrY6o2Ojrb1WbFihRo0aCB3d3flzZtXbdq00a+//pqmjlv9jGTVvFmSpk+frjJlyih37tx69NFHtWnTpju+dwDuDiE6AC1btkySsuSJidTwr2DBgnc9Vno6deoki8WihQsX2trmzZungIAA1axZM03/K1euqFGjRpozZ45CQkL08ccfq379+goLC9OQIUPS9J87d66mTp2ql156Sa+88oo2bNigrl276q233tLKlSv12muv6bnnntOyZcs0dOhQu3NHjhyp/v37y9fXV+PGjVPnzp316aefqmXLlmmC8XPnzql169aqXr26Jk6cqCZNmuh///ufzp07p1WrVtn1jY2N1bp167L8iZZ+/fpp2rRp6ty5s6ZOnaqhQ4cqd+7cOnDggK3PggULdOXKFb3wwgv65JNPFBwcrE8++UQhISF2Yy1fvlzdunWTi4uLwsPD1alTJ/Xp00c7d+5M87rPP/+8hg0bpvr162vSpEnq1auX5s6dq+DgYNO1xm+WnJysVq1aydvbWx988IECAwM1YsQI20dzLRaLnnnmGa1YsSLNP3QsW7ZMCQkJt7yXK1asUHJycpprvFlISIhu3LihlStX2s65cePGXa+tmTqxzp8//237lixZUmvXrrUF+enJzL2YMWOGXn75ZVWsWFETJ07UqFGjVL16dW3fvl3Sv3/3evToIUmaMGGCZs+erdmzZ6tw4cKSpHfffVchISEqV66cxo8fr0GDBmnt2rVq2LBhmn9IOn/+vNq2bas6derogw8+kNVqVffu3fXVV1+pe/fuevzxx/X+++/r8uXLevLJJ+9q3UsAAHB7JUuWVHJysmbPnn3bvqtXr1bDhg3122+/aeDAgRo3bpyaNGmi77777pbnZXau0KpVK1WrVk3jxo1TQECAXnvtNa1YsUKSVKFCBY0ePVqS9Nxzz9nmJQ0bNkzzuhUqVNDs2bNVqFAhVa9ePc0c5r/27t2rOnXqaN26derbt68mTZqkjh072t4z3c6VK1f0999/p9lu3Lhhek5KSorat2+vjz76SO3atdMnn3yijh07asKECerWrVua/ps2bdIrr7yi0NBQjRw5UgcOHFDbtm01ZcoUffzxx3rxxRc1bNgwbd26Vb1797Y7d926dWrYsKESEhI0YsQIvffee7pw4YKaNm2qn376Kc1rde3aVRcvXlR4eLi6du2qqKgojRo1ynZ89uzZslqtatCgge3epj6kkZH3G3cidc79ww8/2Np+/fVX1a1bVwcOHNDrr7+ucePGyd3dXR07dtSiRYskSQ0bNtTLL78sSXrjjTds9VaoUMF2LW3atJGHh4fGjh2r4cOH67ffftNjjz1mF4Df7mckq+bNM2fO1PPPPy8fHx998MEHql+/vtq3b59u2A7gHjAA3Pf27dtn1K9f3/R4nTp1jMOHD5ser1GjhpEvX7407ZcuXTLOnj1r2+Lj423H1q9fb0gyZs2aZZw9e9Y4deqUsXz5cqNUqVKGxWIxfv75Z7t+CxYsSPe1+/fvb2TkV1FoaKjh7u5uGIZhPPnkk0azZs0MwzCM5ORkw8fHxxg1apRx7NgxQ5Lx4Ycf2s4bM2aM4e7ubvz+++92473++uuGs7OzERMTYxiGYTu3cOHCxoULF2z9wsLCDElGtWrVjKSkJFt7jx49DFdXV+PatWuGYRjGmTNnDFdXV6Nly5ZGcnKyrd/kyZNt9ylVo0aNDElGRESEXU3JyclG8eLFjW7dutm1jx8/3rBYLMbRo0dveY8aNWpkVKpUKd1jZ8+eNSQZI0aMsLV5eXkZ/fv3v+WYV65cSdMWHh5uWCwW488//7S1ValSxShevLhx8eJFW1t0dLQhyShZsqStbdOmTYYkY+7cuXZjrly5Mt32/woNDTUkGS+99JKtLSUlxWjTpo3h6upqnD171jAMwzh06JAhyZg2bZrd+e3btzdKlSplpKSkmL7GoEGDDEnG7t27Tfvs2rXLkGQMGTLEMAzDGDx48G3PuVlkZKQhyVizZo1x9uxZ48SJE8Y333xjFC5c2LBarcaJEyduO8bMmTMNSYarq6vRpEkTY/jw4camTZvsfv4MI+P3okOHDqY/P6k+/PBDQ5Jx7Ngxu/bjx48bzs7OxrvvvmvXvm/fPiNXrlx27ak///PmzbO1HTx40JBkODk5Gdu2bbO1r1q1ypBkREZG3vZ+AADwMLvb9wOxsbFG4cKFDUlGQECA0a9fP2PevHl282LDMIwbN24Y/v7+RsmSJY3z58/bHbt5fjVixAi7Of6dzBW++OILW1tiYqLh4+NjdO7c2db2888/m84TQkND7eaghmEYJUuWNNq0aWPXlvoe4OYxGjZsaOTNm9durvvf60tP6lhm29atW+2usVGjRrb92bNnG05OTsamTZvsxoyIiDAkGT/++KOtTZJhtVrt5mOffvqpIcnw8fExEhISbO2p72VS+6akpBjlypUzgoOD7a7nypUrhr+/v9GiRQtbW+qfYe/eve1qeuKJJ4yCBQvatbm7uxuhoaFp7klG3m+kJ3WunPqeMj1eXl5GjRo1bPvNmjUzqlSpYnt/Zhj/Xm+9evWMcuXK2doWLFhgSDLWr19vN97FixeNfPnyGX379rVrj42NNby8vOzaM/Izcrfz5uvXrxtFihQxqlevbiQmJtr6TZ8+3ZBk9/MD4N7gSXQASkhISPeLgt58800VLlzYtqW3XErv3r1VuHBh+fr6qk2bNraP8t28dl1We+qppxQdHW17Qjs2NtZ0KZcFCxaoQYMGyp8/v92TIM2bN1dycrI2btxo179Lly7y8vKy7depU0fSv0/p37yuY506dXT9+nWdPHlSkrRmzRpdv35dgwYNkpPT//1q7du3rzw9PbV8+XK717FarerVq5ddm5OTk55++mktXbrU7snbuXPnql69evL398/MbbqtfPnyafv27Tp16pRpn5vXGLx8+bL+/vtv1atXT4ZhaPfu3ZL+/VKnffv22b6MNlWjRo1UpUoVu/EWLFggLy8vtWjRwu7PIzAwUB4eHlq/fn2Gar95mZXUj7Rev37dtvTHI488ojp16mju3Lm2fv/8849WrFihp59+Ot2PF6dKvfd58+Y17ZN6LCEhwe5/b3VOepo3b67ChQvLz89PTz75pNzd3bV06dLbLvsi/ft3b+XKlWrcuLE2b96sMWPGqEGDBipXrpy2bNli65fRe5EvXz799ddf+vnnnzN1DZK0cOFCpaSkqGvXrnZ/rj4+PipXrlyaP1cPDw91797dtl++fHnly5dPFSpUsP2dk/7v79/Ro0czXRMAAMg4b29v/fLLL+rXr5/Onz+viIgIPfXUUypSpIjGjBljWzZv9+7dOnbsmAYNGpTmyxJvNb+6k7nCzZ8cdHV11aOPPprtc4KzZ89q48aN6t27t0qUKGF37FbXd7PnnntOq1evTrP9dz32my1YsEAVKlRQQECA3f1JXSrzv/enWbNmdksmps6ZOnfubDcf/e9cas+ePbZlMM+dO2d7ncuXL6tZs2bauHFjmi9k7devn91+gwYNdO7cOdv891Yy8n7jTnl4eNjm7f/884/WrVtne2o+9brOnTun4OBgHT582Pa+zczq1at14cIF9ejRw+7PwNnZWXXq1LH9Gdztz0hG/y7s2LFDZ86cUb9+/eyWb+zZs6fd+1UA9w5fLApAefPm1blz59K0v/jii2rbtq0k86Ve3n77bTVo0EDOzs4qVKiQKlSokO1fIvT4448rb968+uqrr7Rnzx7Vrl1bZcuWTXe9v8OHD2vv3r2mH9f87xcw/ncilDpB8fPzS7c9dR3vP//8U5LSrFvu6uqq0qVL246nKlasWLprWYeEhGjs2LFatGiRQkJCdOjQIe3cuVMRERHp1p9ZN0/sPvjgA4WGhsrPz0+BgYF6/PHHFRISotKlS9v6xMTE6O2339bSpUvTrFkeHx8v6f+uvWzZsmler2zZsnZrHh4+fFjx8fG2Nbv/KyNfiOnk5GRXo/RvUCzJ7mcgJCREAwYM0J9//qmSJUtqwYIFSkpKuu2SK6lvPG61hMh/g3ZPT8/bnpOeKVOm6JFHHlF8fLxmzZqljRs3ymq1Zvj84OBgBQcH68qVK9q5c6e++uorRUREqG3btjp48KDtPmfkXrz22mtas2aNHn30UZUtW1YtW7bUU089Zfo9CTc7fPiwDMNQuXLl0j3+3y/3Kl68eJo3GV5eXrf9ewYAALJP0aJFNW3aNE2dOlWHDx/WqlWrNHbsWL399tsqWrSonn32WdvSjZUrV87U2FkxV8ifP7/27t2bqdfNrNSwObPXd7Ny5cqpefPmmTrn8OHDOnDgQLa/Zzl8+LAkKTQ01LSW+Ph4u6UF//taqcfOnz9vmwObycj7jTt16dIl21z3jz/+kGEYGj58uIYPH55u/zNnzqhYsWKm46Xem9R/uPiv1Gu925+RjP5dSH2P9d9+Li4uWXL/AGQeIToABQQEaM+ePTp58qTdxOKRRx6xhZNm31pfpUqVW04SU89L74sopX/XDDQb24zValWnTp30+eef6+jRoxo5cqRp35SUFLVo0UKvvvpqusdTry+Vs7Nzuv3M2lOfyskss2+Rr1ixogIDA21ruM+ZM0eurq7q2rXrbcd0c3O75X1O7ZOqa9euatCggRYtWqQffvhBH374ocaOHauFCxeqdevWSk5OVosWLfTPP//otddeU0BAgNzd3XXy5En17NkzzVMqGZGSkqIiRYrYPRV9M7M3Dneie/fuGjx4sObOnas33nhDc+bMUa1atW77Ba2payLu3btX1atXT7dP6hu41CeKAgICJEn79u0zPSc9jz76qO1TGx07dtRjjz2mp556SocOHUr30yFm8uTJowYNGqhBgwYqVKiQRo0apRUrVtjeIGXkXlSoUEGHDh3Sd999p5UrV+rbb7/V1KlT9fbbb9ute5melJQUWSwWrVixIt2/K/+9lnv19wwAAGSexWKxvQ9o06aNypUrp7lz5+rZZ5+94zGzaq7woM4JUlJSVKVKFY0fPz7d4/8Nx+90LpU6f//www9N56xZ+Wdxu/cbd+qvv/5SfHy87SGe1OsaOnSogoOD0z0nvQd+bpY6xuzZs+Xj45PmeFY9KJbZvwsAcg5CdABq27at5s+fr7lz55qGzXeqZMmSkqRDhw6le/zQoUO2Ppnx1FNPadasWXJycrJbFuK/ypQpo0uXLmX6aZDMuvk6b34y4Pr16zp27FimXj8kJERDhgzR6dOnNW/ePLVp0ybDXzS5bt06Xb16NU1In3r//3uvixYtqhdffFEvvviizpw5o5o1a+rdd99V69attW/fPv3+++/6/PPP7b5kc/Xq1ele+x9//JGmpv+2lSlTRmvWrFH9+vVN/yHhdlJSUnT06FG7fwD5/fffJcnuY60FChRQmzZtNHfuXD399NP68ccfNXHixNuO37p1azk7O2v27NmmXy76xRdfKFeuXGrVqpXdOXPmzLnjLxd1dnZWeHi4mjRposmTJ+v111+/o3FSQ/nTp0/b2jJ6L9zd3dWtWzd169ZN169fV6dOnfTuu+8qLCxMbm5uph9RLVOmjAzDkL+/f5p/mAIAAPev0qVLK3/+/LZ5RZkyZSRJ+/fvz9T8NjvmChldXiUzUufx+/fvz/Kxb6VMmTL65Zdf1KxZs2y5rptfR/r3qeqsfH90q5pv9X7jTqV+AW5qYJ765+bi4nLb67rVfFaSihQpcssxMvozcrfz5tT3WIcPH7Z7Oj4pKUnHjh1TtWrVbvn6ALIea6IDUNeuXVWxYkWNGTNG27ZtS7fPnT71UbRoUVWvXl1z5syx+6ZxSdq5c6e2bdt2RxOoJk2aaMyYMZo8eXK6Twqk6tq1q7Zu3apVq1alOXbhwgXduHEj06+dnubNm8vV1VUff/yx3b2aOXOm4uPj1aZNmwyP1aNHD1ksFg0cOFBHjx41XUrnvx5//HElJSXp008/tWtPSUnRtGnT5OrqqmbNmkmSkpOTbcuxpCpSpIh8fX2VmJgo6f+eOrn5egzD0KRJk+zO8/X1VeXKlfXFF1/o0qVLtvYNGzZo3759dn27du2q5ORkjRkzJk39N27cSPMzYmby5Ml2NU2ePFkuLi6260v1v//9T7/99puGDRsmZ2fnW/6DSyo/Pz/16tVLa9as0bRp09Icj4iI0Lp169SnTx/b2uV+fn7q27evfvjhB33yySdpzklJSdG4ceP0119/3fK1GzdurEcffVQTJ07UtWvXbtl37dq16bZ///33ktIuLXS7e/HfJZ1cXV1VsWJFGYahpKQkSf+G7JLS/Dl16tRJzs7OGjVqVJrfFYZhpLtcFAAAyDm2b9+uy5cvp2n/6aefdO7cOdu8ombNmvL399fEiRPTzAdu9X4hO+YKZvOSu1G4cGE1bNhQs2bNUkxMjN2x7HwKvmvXrjp58qRmzJiR5tjVq1fT/bO5E4GBgSpTpow++ugju3l7qrNnz97RuO7u7mn+HDLyfuNOrFu3TmPGjJG/v7+efvpp27iNGzfWp59+avcgSaqbr8vs5yY4OFienp567733bHPf9MbI6M/I3c6ba9WqpcKFCysiIkLXr1+39YmKisrSn3kAGceT6ADk4uKiRYsWKTg4WI899pg6deqkBg0a2JbuWLp0qWJiYjIVBN9s/PjxCg4OVvXq1dWzZ0/5+vrqwIEDmj59uooWLaqwsLBMj+nk5KS33nrrtv2GDRumpUuXqm3bturZs6cCAwN1+fJl7du3T998842OHz+uQoUK3cll2SlcuLDCwsI0atQotWrVSu3bt9ehQ4c0depU1a5dO8NBeOpYrVq10oIFC5QvX74M3/d27dqpZcuWGjx4sH766SfVq1dPV65c0dKlS/Xjjz/qnXfesS2XcvHiRRUvXlxPPvmkqlWrJg8PD61Zs0Y///yzxo0bJ+nfJUrKlCmjoUOH6uTJk/L09NS3336b7vrU7733njp06KD69eurV69eOn/+vCZPnqzKlSvbTdAbNWqk559/XuHh4dqzZ49atmwpFxcXHT58WAsWLNCkSZP05JNP3vI63dzctHLlSoWGhqpOnTpasWKFli9frjfeeCPNcjBt2rRRwYIFtWDBArVu3dp0Lfb/mjBhgg4ePKgXX3xRK1eutD1xvmrVKi1ZskSNGjWy3adU48aN05EjR/Tyyy9r4cKFatu2rfLnz6+YmBgtWLBABw8ezFCIP2zYMHXp0kVRUVFpvsjpZh06dJC/v7/atWunMmXK6PLly1qzZo2WLVum2rVrq127dpm6Fy1btpSPj4/q168vb29vHThwQJMnT1abNm1sa78HBgZK+vdLh7t37y4XFxfb67/zzjsKCwvT8ePH1bFjR+XNm1fHjh3TokWL9Nxzz2no0KG3v/EAAMAhZs+erblz5+qJJ55QYGCgXF1ddeDAAc2aNUtubm564403JP07B582bZratWun6tWrq1evXipatKgOHjyoX3/9Nd0HVyRly1yhTJkyypcvnyIiIpQ3b165u7urTp068vf3v6t78fHHH+uxxx5TzZo19dxzz8nf31/Hjx/X8uXLtWfPntuev2vXLs2ZMyfdeoOCgtI953//+5++/vpr9evXT+vXr1f9+vWVnJysgwcP6uuvv9aqVatsnza8G05OTvrss8/UunVrVapUSb169VKxYsV08uRJrV+/Xp6enlq2bFmmxw0MDNSaNWs0fvx4+fr6yt/fX+XLl7/t+43bWbFihQ4ePKgbN24oLi5O69at0+rVq1WyZEktXbrUbqnKKVOm6LHHHlOVKlXUt29flS5dWnFxcdq6dav++usv/fLLL5Kk6tWry9nZWWPHjlV8fLysVquaNm2qIkWKaNq0afrf//6nmjVrqnv37ipcuLBiYmK0fPly1a9f3/YgT0Z+Ru523uzi4qJ33nlHzz//vJo2bapu3brp2LFjioyMZE10wFEMAPe9ffv2GfXr1zc9XqdOHePw4cO3HefChQvG6NGjjRo1ahgeHh6Gq6ur4efnZzz55JPGsmXL7PquX7/ekGQsWLAgQzVu27bNaNu2rZE/f34jV65cRrFixYxnn33W+OuvvzJ0fmhoqOHu7n7LPseOHTMkGR9++KFd+8WLF42wsDCjbNmyhqurq1GoUCGjXr16xkcffWRcv379lueaXWdkZKQhyfj555/t2idPnmwEBAQYLi4uhre3t/HCCy8Y58+ft+vTqFEjo1KlSre8lq+//tqQZDz33HO37Pdf165dM0aOHGkEBAQYVqvVcHd3N+rWrWvMmTPHrl9iYqIxbNgwo1q1akbevHkNd3d3o1q1asbUqVPt+v32229G8+bNDQ8PD6NQoUJG3759jV9++cWQZERGRtr1nT9/vu11K1eubCxdutTo3LmzERAQkKbO6dOnG4GBgUbu3LmNvHnzGlWqVDFeffVV49SpU7e8vtSfgyNHjhgtW7Y08uTJY3h7exsjRowwkpOT0z3nxRdfNCQZ8+bNy8AdtL9HEyZMMAIDAw13d3cjT548Rs2aNY2JEyfafm7+68aNG8Znn31mNGjQwPDy8jJcXFyMkiVLGr169TJ2795t62f282MYhpGcnGyUKVPGKFOmjHHjxg3T+r788kuje/fuRpkyZYzcuXMbbm5uRsWKFY0333zTSEhIyPS9+PTTT42GDRsaBQsWNKxWq1GmTBlj2LBhRnx8vF2/MWPGGMWKFTOcnJwMScaxY8dsx7799lvjscceM9zd3Q13d3cjICDA6N+/v3Ho0CFbH7Of/5IlSxpt2rRJ0y7J6N+/v+l9AAAAd/9+YO/evcawYcOMmjVrGgUKFDBy5cplFC1a1OjSpYuxa9euNP03b95stGjRwjaPrFq1qvHJJ5/Yjo8YMcJIL264m7lCaGioUbJkSbu2JUuWGBUrVjRy5cplNz9Nr296c43U9wD/ndfu37/feOKJJ4x8+fIZbm5uRvny5Y3hw4end+vSjGW2hYaG2l1jo0aN7M6/fv26MXbsWKNSpUqG1Wo18ufPbwQGBhqjRo2ym4+lNzfK7HuZ3bt3G506dbLN+0qWLGl07drVWLt2ra1P6p/h2bNn7c5NncfePAc8ePCg0bBhQyN37ty2a83o+430pL5G6ubq6mr4+PgYLVq0MCZNmmQ61z1y5IgREhJi+Pj4GC4uLkaxYsWMtm3bGt98841dvxkzZhilS5c2nJ2dDUnG+vXr7e5ZcHCw4eXlZbi5uRllypQxevbsaezYscNujIz8jNztvNkwDGPq1KmGv7+/YbVajVq1ahkbN25M9+cHQPazGMYD+s0cwENk//796tevnzZv3pzu8bp162rOnDm3/TIV5BxLlixRx44dtXHjRjVo0MDR5dyx6tWrq3DhwmnWUb+XBg8erJkzZyo2NlZ58uRxWB05AfcCAIAHE+8HAADIXqyJDgA50IwZM1S6dGk99thjji4lQ5KSktKsLx8dHa1ffvlFjRs3dkxRkq5du6Y5c+aoc+fOD31ozL0AAAAAAODOsCY68IDYtm2b8uXLl+6x9L40BjnT/PnztXfvXi1fvlyTJk265Tfd5yQnT55U8+bN9cwzz8jX11cHDx5URESEfHx8brmud3Y5c+aM1qxZo2+++Ubnzp3TwIED73kNOQX3AgCAhwPvBwAAyD4s5wIAOYjFYpGHh4e6deumiIgI5cp1f/xbZ3x8vJ577jn9+OOPOnv2rNzd3dWsWTO9//77KlOmzD2vJzo6Wk2aNFGRIkU0fPhwDRgw4J7XkFNwLwAAAAAAuDuE6AAAAAAAAAAAmGBNdAAAAAAAAAAATBCiAwAAAAAAAABg4v5YbDcLpaSk6NSpU8qbN+9984V9AAAAeHAYhqGLFy/K19dXTk4P9zMtzM0BAADgSBmdmz90IfqpU6fk5+fn6DIAAADwkDtx4oSKFy/u6DIcirk5AAAAcoLbzc0fuhA9b968kv69MZ6eng6uBgAAAA+bhIQE+fn52ealDzPm5gAAAHCkjM7NH7oQPfVjop6enkzUAQAA4DAsX8LcHAAAADnD7ebmD/cijAAAAAAAAAAA3AIhOgAAAAAAAAAAJgjRAQAAAAAAAAAwQYgOAAAAAAAAAIAJQnQAAAAAAAAAAEwQogMAAAAAAAAAYIIQHQAAAAAAAAAAE4ToAAAAAAAAAACYIEQHAAAAAAAAAMAEIToAAAAAAAAAACYI0QEAAAAAAAAAMEGIDgAAAAAAAACACUJ0AAAAAHaSk5M1fPhw+fv7K3fu3CpTpozGjBkjwzBsfQzD0Ntvv62iRYsqd+7cat68uQ4fPuzAqgEAAIDsQYgOAAAAwM7YsWM1bdo0TZ48WQcOHNDYsWP1wQcf6JNPPrH1+eCDD/Txxx8rIiJC27dvl7u7u4KDg3Xt2jUHVg4AAABkvVyOLgAAAABAzrJlyxZ16NBBbdq0kSSVKlVKX375pX766SdJ/z6FPnHiRL311lvq0KGDJOmLL76Qt7e3Fi9erO7du6c7bmJiohITE237CQkJ2XwlAAAAwN3jSXQAAAAAdurVq6e1a9fq999/lyT98ssv2rx5s1q3bi1JOnbsmGJjY9W8eXPbOV5eXqpTp462bt1qOm54eLi8vLxsm5+fX/ZeCAAAAJAFeBIdAAAAgJ3XX39dCQkJCggIkLOzs5KTk/Xuu+/q6aefliTFxsZKkry9ve3O8/b2th1LT1hYmIYMGWLbT0hIIEgHAABAjkeIDgAAAMDO119/rblz52revHmqVKmS9uzZo0GDBsnX11ehoaF3PK7VapXVas3CSgEAAIDsR4gOAAAAwM6wYcP0+uuv29Y2r1Kliv7880+Fh4crNDRUPj4+kqS4uDgVLVrUdl5cXJyqV6/uiJIBAACAbOPQNdGnTZumqlWrytPTU56engoKCtKKFStuec6CBQsUEBAgNzc3ValSRd9///09qhYAAOR0hmHo0qVLts0wDEeXBNyXrly5Iicn+7cKzs7OSklJkST5+/vLx8dHa9eutR1PSEjQ9u3bFRQUdE9rBQAAORNzczxIHPokevHixfX++++rXLlyMgxDn3/+uTp06KDdu3erUqVKafpv2bJFPXr0UHh4uNq2bat58+apY8eO2rVrlypXruyAKwAAADnJ5cuX1aFDB9v+kiVL5OHh4cCKgPtTu3bt9O6776pEiRKqVKmSdu/erfHjx6t3796SJIvFokGDBumdd95RuXLl5O/vr+HDh8vX11cdO3Z0bPEAACBHYG6OB4lDQ/R27drZ7b/77ruaNm2atm3blm6IPmnSJLVq1UrDhg2TJI0ZM0arV6/W5MmTFRERke5rJCYmKjEx0bafkJCQhVcAAAAAPHg++eQTDR8+XC+++KLOnDkjX19fPf/883r77bdtfV599VVdvnxZzz33nC5cuKDHHntMK1eulJubmwMrBwAAALKeQ5dzuVlycrLmz5+vy5cvm34EdOvWrWrevLldW3BwsLZu3Wo6bnh4uLy8vGybn59fltYNZCU+6gQAAHKCvHnzauLEifrzzz919epVHTlyRO+8845cXV1tfSwWi0aPHq3Y2Fhdu3ZNa9as0SOPPOLAqgEAAIDs4fAvFt23b5+CgoJ07do1eXh4aNGiRapYsWK6fWNjY+Xt7W3X5u3trdjYWNPxw8LCNGTIENt+QkICQTpyLD7qBGRO4LAvHF0CchjLjevyumm/8fD5MnK5mvbHw2nnhyGOLgEAAADAfcThIXr58uW1Z88excfH65tvvlFoaKg2bNhgGqRnltVqldVqzZKxAAAAAAAAAAAPF4eH6K6uripbtqwkKTAwUD///LMmTZqkTz/9NE1fHx8fxcXF2bXFxcXJx8fnntSa1XiCEv/FE5TICJ6gBAAAAAAAuHdyzJroqVJSUuy+CPRmQUFBWrt2rV3b6tWrTddQBwAADxfD2UXxVXvYNsPZxdElAQAAAADucw59Ej0sLEytW7dWiRIldPHiRc2bN0/R0dFatWqVJCkkJETFihVTeHi4JGngwIFq1KiRxo0bpzZt2mj+/PnasWOHpk+f7sjLAAAAOYXFwid4AAAAAABZyqEh+pkzZxQSEqLTp0/Ly8tLVatW1apVq9SiRQtJUkxMjJyc/u9h+Xr16mnevHl666239MYbb6hcuXJavHixKleu7KhLAAAAAAAAAAA8wBwaos+cOfOWx6Ojo9O0denSRV26dMmmigAAAAAAAAAA+D8O/2JRAP8ndS3fm/cBAAAAAAAAOA4hOpCTsJYvAAAAAAAAkKM43b4LAAAAAAAAAAAPJ0J0AAAAAAAAAABMEKIDAAAAAAAAAGCCEB0AAAAAAAAAABOE6AAAAAAAAAAAmCBEBwAAAAAAAADABCE6AAAAAAAAAAAmCNEBAAAAAAAAADBBiA4AAAAAAAAAgAlCdAAAAAAAAAAATBCiAwAAAAAAAABgghAdAAAAAAAAAAATuRxdAAAAAAAAOY1hGLp8+bJt393dXRaLxYEVAQAARyFEBwAAAADgPy5fvqwOHTrY9pcsWSIPDw8HVgQAAByF5VwAAAAAAAAAADBBiA4AAAAAAAAAgAlCdAAAAAAAAAAATBCiAwAAAAAAAABgghAdAAAAAAAAAAAThOgAAAAAAAAAAJggRAcAAAAAAAAAwAQhOgAAAAAAAAAAJgjRAQAAAAAAAAAwQYgOAAAAAAAAAIAJQnQAAAAAAAAAAEwQogMAAAAAAAAAYIIQHQAAAAAAAAAAE4ToAAAAAAAAAACYIEQHAAAAAAAAAMAEIToAAAAAAAAAACYI0QEAAAAAAAAAMEGIDgAAAAAAAACACUJ0AAAAAAAAAABMEKIDAAAAAAAAAGCCEB0AAAAAAAAAABOE6AAAAAAAAAAAmCBEBwAAAAAAAADABCE6AAAAAAAAAAAmCNEBAAAAAAAAADBBiA4AAAAAAAAAgAlCdAAAAAAAAAAATBCiAwAAAEijVKlSslgsabb+/ftLkq5du6b+/furYMGC8vDwUOfOnRUXF+fgqgEAAICsR4gOAAAAII2ff/5Zp0+ftm2rV6+WJHXp0kWSNHjwYC1btkwLFizQhg0bdOrUKXXq1MmRJQMAAADZIpejCwAAAACQ8xQuXNhu//3331eZMmXUqFEjxcfHa+bMmZo3b56aNm0qSYqMjFSFChW0bds21a1b1xElAwAAANmCJ9EBAAAA3NL169c1Z84c9e7dWxaLRTt37lRSUpKaN29u6xMQEKASJUpo69atpuMkJiYqISHBbgMAAAByOkJ0AAAAALe0ePFiXbhwQT179pQkxcbGytXVVfny5bPr5+3trdjYWNNxwsPD5eXlZdv8/PyysWoAAAAgaxCiAwAAALilmTNnqnXr1vL19b2rccLCwhQfH2/bTpw4kUUVAgAAANmHNdEBAAAAmPrzzz+1Zs0aLVy40Nbm4+Oj69ev68KFC3ZPo8fFxcnHx8d0LKvVKqvVmp3lAgAAAFmOJ9EBAAAAmIqMjFSRIkXUpk0bW1tgYKBcXFy0du1aW9uhQ4cUExOjoKAgR5QJAAAAZBueRAcAAACQrpSUFEVGRio0NFS5cv3fWwcvLy/16dNHQ4YMUYECBeTp6amXXnpJQUFBqlu3rgMrBgAAALIeIToAAACAdK1Zs0YxMTHq3bt3mmMTJkyQk5OTOnfurMTERAUHB2vq1KkOqBIAAADIXoToAAAAANLVsmVLGYaR7jE3NzdNmTJFU6ZMucdVAQAAAPcWa6IDAAAAAAAAAGCCEB0AAAAAAAAAABOE6AAAAAAAAAAAmCBEBwAAAAAAAADABCE6AAAAAAAAAAAmCNEBAAAAAAAAADBBiA4AAAAAAAAAgAlCdAAAAAAAAAAATBCiAwAAAAAAAABgghAdAAAAAAAAAAAThOgAAAAAAAAAAJggRAcAAAAAAAAAwAQhOgAAAAAAAAAAJgjRAQAAAAAAAAAwQYgOAAAAAAAAAIAJQnQAAAAAAAAAAEw4NEQPDw9X7dq1lTdvXhUpUkQdO3bUoUOHbnlOVFSULBaL3ebm5naPKgYAAAAAAAAAPEwcGqJv2LBB/fv317Zt27R69WolJSWpZcuWunz58i3P8/T01OnTp23bn3/+eY8qBgAAAAAAAAA8THI58sVXrlxptx8VFaUiRYpo586datiwoel5FotFPj4+GXqNxMREJSYm2vYTEhLurFgAAAAAAAAAwEMnR62JHh8fL0kqUKDALftdunRJJUuWlJ+fnzp06KBff/3VtG94eLi8vLxsm5+fX5bWDAAAAAAAAAB4cOWYED0lJUWDBg1S/fr1VblyZdN+5cuX16xZs7RkyRLNmTNHKSkpqlevnv766690+4eFhSk+Pt62nThxIrsuAQAAAAAAAADwgHHoci4369+/v/bv36/Nmzffsl9QUJCCgoJs+/Xq1VOFChX06aefasyYMWn6W61WWa3WLK8XAAAAAAAAAPDgyxEh+oABA/Tdd99p48aNKl68eKbOdXFxUY0aNfTHH39kU3UAAAAAAAAAgIeVQ5dzMQxDAwYM0KJFi7Ru3Tr5+/tneozk5GTt27dPRYsWzYYKAQAAAAAAAAAPM4c+id6/f3/NmzdPS5YsUd68eRUbGytJ8vLyUu7cuSVJISEhKlasmMLDwyVJo0ePVt26dVW2bFlduHBBH374of788089++yzDrsOAAAAAAAAAMCDyaEh+rRp0yRJjRs3tmuPjIxUz549JUkxMTFycvq/B+bPnz+vvn37KjY2Vvnz51dgYKC2bNmiihUr3quyAQAAAAAAAAAPCYeG6IZh3LZPdHS03f6ECRM0YcKEbKoIAAAAAAAAAID/49A10QEAAAAAAAAAyMkI0QEAAAAAAAAAMEGIDgAAAAAAAACACUJ0AAAAAAAAAABMEKIDAAAAAAAAAGCCEB0AAAAAAAAAABOE6AAAAAAAAAAAmCBEBwAAAAAAAADABCE6AAAAAAAAAAAmCNEBAAAAAAAAADBBiA4AAAAAAAAAgAlCdAAAAAAAAAAATBCiAwAAAAAAAABgghAdAAAAAAAAAAAThOgAAAAAAAAAAJggRAcAAAAAAAAAwAQhOgAAAAAAAAAAJgjRAQAAAAAAAAAwQYgOAAAAAAAAAIAJQnQAAAAAAAAAAEwQogMAAAAAAAAAYIIQHQAAAAAAAAAAE4ToAAAAAAAAAACYIEQHAAAAAAAAAMAEIToAAACANE6ePKlnnnlGBQsWVO7cuVWlShXt2LHDdtwwDL399tsqWrSocufOrebNm+vw4cMOrBgAAADIHoToAAAAAOycP39e9evXl4uLi1asWKHffvtN48aNU/78+W19PvjgA3388ceKiIjQ9u3b5e7uruDgYF27ds2BlQMAAABZL5ejCwAAAACQs4wdO1Z+fn6KjIy0tfn7+9v+v2EYmjhxot566y116NBBkvTFF1/I29tbixcvVvfu3dMdNzExUYmJibb9hISEbLoCAAAAIOvwJDoAAAAAO0uXLlWtWrXUpUsXFSlSRDVq1NCMGTNsx48dO6bY2Fg1b97c1ubl5aU6depo69atpuOGh4fLy8vLtvn5+WXrdQAAAABZgRAdAAAAgJ2jR49q2rRpKleunFatWqUXXnhBL7/8sj7//HNJUmxsrCTJ29vb7jxvb2/bsfSEhYUpPj7etp04cSL7LgIAAADIIiznAgAAAMBOSkqKatWqpffee0+SVKNGDe3fv18REREKDQ2943GtVqusVmtWlQkAAADcEzyJDgAAAMBO0aJFVbFiRbu2ChUqKCYmRpLk4+MjSYqLi7PrExcXZzsGAAAAPCgI0QEAAADYqV+/vg4dOmTX9vvvv6tkyZKS/v2SUR8fH61du9Z2PCEhQdu3b1dQUNA9rRUAAADIbiznAgAAAMDO4MGDVa9ePb333nvq2rWrfvrpJ02fPl3Tp0+XJFksFg0aNEjvvPOOypUrJ39/fw0fPly+vr7q2LGjY4sHAAAAshghOgAAAAA7tWvX1qJFixQWFqbRo0fL399fEydO1NNPP23r8+qrr+ry5ct67rnndOHCBT322GNauXKl3NzcHFg5AAAAkPUI0QEAAACk0bZtW7Vt29b0uMVi0ejRozV69Oh7WBUAAABw77EmOgAAAAAAAAAAJgjRAQAAAAAAAAAwQYgOAAAAAAAAAIAJQnQAAAAAAAAAAEwQogMAAAAAAAAAYIIQHQAAAAAAAAAAE4ToAAAAAAAAAACYIEQHAAAAAAAAAMAEIToAAAAAAAAAACYI0QEAAAAAAAAAMEGIDgAAAAAAAACACUJ0AAAAAAAAAABMEKIDAAAAAAAAAGCCEB0AAAAAAAAAABOE6AAAAAAAAAAAmCBEBwAAAAAAAADABCE6AAAAAAAAAAAmCNEBAAAAAAAAADBBiA4AAAAAAAAAgAlCdAAAAAAAAAAATBCiAwAAAAAAAABgghAdAAAAAAAAAAAThOgAAAAAAAAAAJggRAcAAAAAAAAAwAQhOgAAAAAAAAAAJgjRAQAAAAAAAAAwQYgOAAAAAAAAAIAJQnQAAAAAAAAAAEwQogMAAAAAAAAAYIIQHQAAAAAAAAAAE4ToAAAAAAAAAACYIEQHAAAAAAAAAMBELkcXAAAAAACOFjjsC0eXgBzGcuO6vG7abzx8voxcrg6rBznPzg9DHF0CAOAe4Ul0AAAAAAAAAABMEKIDAAAAAAAAAGDCoSF6eHi4ateurbx586pIkSLq2LGjDh06dNvzFixYoICAALm5ualKlSr6/vvv70G1AAAAAAAAAICHjUND9A0bNqh///7atm2bVq9eraSkJLVs2VKXL182PWfLli3q0aOH+vTpo927d6tjx47q2LGj9u/ffw8rBwAAAAAAAAA8DBz6xaIrV66024+KilKRIkW0c+dONWzYMN1zJk2apFatWmnYsGGSpDFjxmj16tWaPHmyIiIi0vRPTExUYmKibT8hISELrwAAAAAAAAAA8CDLUWuix8fHS5IKFChg2mfr1q1q3ry5XVtwcLC2bt2abv/w8HB5eXnZNj8/v6wrGAAAAAAAAADwQMsxIXpKSooGDRqk+vXrq3Llyqb9YmNj5e3tbdfm7e2t2NjYdPuHhYUpPj7etp04cSJL6wYAAAAAAAAAPLgcupzLzfr376/9+/dr8+bNWTqu1WqV1WrN0jEBAAAAAADwfwKHfeHoEpDDWG5cl9dN+42Hz5eRy9Vh9SDn2flhiKNLyLAcEaIPGDBA3333nTZu3KjixYvfsq+Pj4/i4uLs2uLi4uTj45OdJQIAAAAAAAAAHkIOXc7FMAwNGDBAixYt0rp16+Tv73/bc4KCgrR27Vq7ttWrVysoKCi7ygQAAAAAAAAAPKQc+iR6//79NW/ePC1ZskR58+a1rWvu5eWl3LlzS5JCQkJUrFgxhYeHS5IGDhyoRo0aady4cWrTpo3mz5+vHTt2aPr06Q67DgAAAAAAAADAg8mhT6JPmzZN8fHxaty4sYoWLWrbvvrqK1ufmJgYnT592rZfr149zZs3T9OnT1e1atX0zTffaPHixbf8MlIAAAAAAAAAAO6EQ59ENwzjtn2io6PTtHXp0kVdunTJhooAAAAAAAAAAPg/Dn0SHQAAAAAAAACAnIwQHQAAAAAAAAAAE4ToAAAAAAAAAACYIEQHAAAAAAAAAMAEIToAAAAAAAAAACYI0QEAAACkMXLkSFksFrstICDAdvzatWvq37+/ChYsKA8PD3Xu3FlxcXEOrBgAAADIHoToAAAAANJVqVIlnT592rZt3rzZdmzw4MFatmyZFixYoA0bNujUqVPq1KmTA6sFAAAAskcuRxcAAAAAIGfKlSuXfHx80rTHx8dr5syZmjdvnpo2bSpJioyMVIUKFbRt2zbVrVs33fESExOVmJho209ISMiewgEAAIAsxJPoAAAAANJ1+PBh+fr6qnTp0nr66acVExMjSdq5c6eSkpLUvHlzW9+AgACVKFFCW7duNR0vPDxcXl5ets3Pzy/brwEAAAC4W4ToAAAAANKoU6eOoqKitHLlSk2bNk3Hjh1TgwYNdPHiRcXGxsrV1VX58uWzO8fb21uxsbGmY4aFhSk+Pt62nThxIpuvAgAAALh7LOcCAAAAII3WrVvb/n/VqlVVp04dlSxZUl9//bVy5859R2NarVZZrdasKhEAAAC4J3gSHQAAAMBt5cuXT4888oj++OMP+fj46Pr167pw4YJdn7i4uHTXUAcAAADuZ4ToAAAAAG7r0qVLOnLkiIoWLarAwEC5uLho7dq1tuOHDh1STEyMgoKCHFglAAAAkPVYzgUAAABAGkOHDlW7du1UsmRJnTp1SiNGjJCzs7N69OghLy8v9enTR0OGDFGBAgXk6empl156SUFBQapbt66jSwcAAACyFCE6AAAAgDT++usv9ejRQ+fOnVPhwoX12GOPadu2bSpcuLAkacKECXJyclLnzp2VmJio4OBgTZ061cFVAwAAAFmPEB0AAABAGvPnz7/lcTc3N02ZMkVTpky5RxUBAAAAjsGa6AAAAAAAAAAAmCBEBwAAAAAAAADARKaWczlw4IDmz5+vTZs26c8//9SVK1dUuHBh1ahRQ8HBwercubOsVmt21QoAAAAAAAAAwD2VoSfRd+3apebNm6tGjRravHmz6tSpo0GDBmnMmDF65plnZBiG3nzzTfn6+mrs2LFKTEzM7roBAAAAAAAAAMh2GXoSvXPnzho2bJi++eYb5cuXz7Tf1q1bNWnSJI0bN05vvPFGVtUIAAAAAAAAAIBDZChE//333+Xi4nLbfkFBQQoKClJSUtJdFwYAAAAAAAAAgKNlaDmXjATod9MfAAAAAAAAAICcKEMheqqLFy9q586dunTpkqR/10oPCQlRly5dNHfu3GwpEAAAAAAAAAAAR8nQci6StHHjRrVt21aXLl1S/vz59eWXX+rJJ59UsWLF5OzsrIULF+rKlSvq27dvdtYLAAAAAAAAAMA9k+En0d966y116dJFJ06c0KBBg9StWzcNGDBABw4c0P79+zVq1ChNmTIlO2sFAAAAAAAAAOCeynCIvnfvXg0bNkzFihXTa6+9poSEBHXr1s12vHv37jpy5Ei2FAkAAAAAAAAAgCNkOERPSEhQgQIFJEmurq7KkyeP8ubNazueN29eXblyJesrBAAAAAAAAADAQTIcolssFlksFtN9AAAAAAAAAAAeNBn+YlHDMNSsWTPlyvXvKVeuXFG7du3k6uoqSbpx40b2VAgAAAAAAAAAgINkOEQfMWKE3X6HDh3S9OncufPdVwQAAAAAAAAAQA5xxyE6AAAAAAAAAAAPugyviQ4AAAAAAAAAwMMmwyH6kSNH1Lt3b9t+iRIlVKBAAdtWuHBhHTp0KFuKBAAAAAAAAADAETK8nMsnn3wib29v2/758+f19ttvq0iRIpKkr776ShMmTFBERETWVwkAAAAAAAAAgANkOERfu3atZs6cadfWuXNnlS5dWpJUqlQpPfvss1lbHQAAAAAAAAAADpTh5VyOHz8uX19f2/6zzz4rLy8v236pUqX0119/ZW11AAAAAAAAAAA4UIZDdCcnJ506dcq2P2HCBBUsWNC2HxcXJxcXl6ytDgAAAAAAAAAAB8pwiF6pUiWtWbPG9PiqVatUuXLlLCkKAAAAAAAAAICcIMMheq9evfTuu+9q+fLlaY4tW7ZM77//vnr16pWlxQEAAAAAAAAA4EgZ/mLRvn37at26dWrXrp0CAgJUvnx5SdKhQ4d06NAhde7cWX379s22QgEAAAAAAAAAuNcy/CS6JH355ZeaN2+eHnnkEVt4Xq5cOc2dO1dff/11dtUIAAAAAAAAAIBDZPhJ9FTdu3dX9+7ds6MWAAAAAAAAAABylAw9iX758uVMDZrZ/gAAAACyRunSpXXu3Lk07RcuXFDp0qUdUBEAAABwf8tQiF62bFm9//77On36tGkfwzC0evVqtW7dWh9//HGWFQgAAAAg444fP67k5OQ07YmJiTp58qQDKgIAAADubxlaziU6OlpvvPGGRo4cqWrVqqlWrVry9fWVm5ubzp8/r99++01bt25Vrly5FBYWpueffz676wYAAABwk6VLl9r+/6pVq+Tl5WXbT05O1tq1a1WqVCkHVAYAAADc3zIUopcvX17ffvutYmJitGDBAm3atElbtmzR1atXVahQIdWoUUMzZsxQ69at5ezsnN01AwAAAPiPjh07SpIsFotCQ0Ptjrm4uKhUqVIaN26cAyoDAAAA7m+Z+mLREiVK6JVXXtErr7ySXfUAAAAAuAMpKSmSJH9/f/38888qVKiQgysCAAAAHgyZCtEBAAAA5GzHjh1zdAkAAADAA4UQHQAAAHjArF27VmvXrtWZM2dsT6inmjVrloOqAgAAAO5PhOgAAADAA2TUqFEaPXq0atWqpaJFi8pisTi6JAAAAOC+RogOAAAAPEAiIiIUFRWl//3vf44uBQAAAHggODm6AAAAAABZ5/r166pXr56jywAAAAAeGJkO0UuVKqXRo0crJiYmO+oBAAAAcBeeffZZzZs3z9FlAAAAAA+MTC/nMmjQIEVFRWn06NFq0qSJ+vTpoyeeeEJWqzU76gMAAACQCdeuXdP06dO1Zs0aVa1aVS4uLnbHx48f76DKAAAAgPtTpp9EHzRokPbs2aOffvpJFSpU0EsvvaSiRYtqwIAB2rVrV3bUCAAAACCD9u7dq+rVq8vJyUn79+/X7t27bduePXscXR4AAABw37njLxatWbOmatasqXHjxmnq1Kl67bXXNG3aNFWpUkUvv/yyevXqJYvFkpW1AgAAALiN9evXO7oEAAAA4IFyxyF6UlKSFi1apMjISK1evVp169ZVnz599Ndff+mNN97QmjVrWIsRAAAAAAAAAHBfy3SIvmvXLkVGRurLL7+Uk5OTQkJCNGHCBAUEBNj6PPHEE6pdu3aWFgoAAADg9po0aXLLT4SuW7fuHlYDAAAA3P8yHaLXrl1bLVq00LRp09SxY8c0X1QkSf7+/urevXuWFAgAAAAg46pXr263n5SUpD179mj//v0KDQ11TFEAAADAfSxTIXpycrJmzZql9u3bK3/+/Kb93N3dFRkZedfFAQAAAMicCRMmpNs+cuRIXbp06R5XAwAAANz/nDLT2dnZWc8//7wuXLiQTeUAAAAAyA7PPPOMZs2a5egyAAAAgPtOpkJ0SapcubKOHj2aHbUAAAAAyCZbt26Vm5ubo8sAAAAA7juZXhP9nXfe0dChQzVmzBgFBgbK3d3d7rinp2eWFQcAAAAgczp16mS3bxiGTp8+rR07dmj48OEOqgoAAAC4f2U6RH/88cclSe3bt5fFYrG1G4Yhi8Wi5OTkrKsOAAAAQKZ4eXnZ7Ts5Oal8+fIaPXq0WrZs6aCqAAAAgPtXpkP09evXZ0cdAAAAALJAZGSko0sAAAAAHiiZDtEbNWqUHXUAAAAAyEI7d+7UgQMHJEmVKlVSjRo17nis999/X2FhYRo4cKAmTpwoSbp27ZpeeeUVzZ8/X4mJiQoODtbUqVPl7e2dFeUDAAAAOUamQ3RJunDhgmbOnGk3Ke/du3eaj44CAAAAuLfOnDmj7t27Kzo6Wvny5ZP07/y9SZMmmj9/vgoXLpyp8X7++Wd9+umnqlq1ql374MGDtXz5ci1YsEBeXl4aMGCAOnXqpB9//DGrLgUAAADIEZwye8KOHTtUpkwZTZgwQf/884/++ecfjR8/XmXKlNGuXbuyo0YAAAAAGfTSSy/p4sWL+vXXX23z9f379yshIUEvv/xypsa6dOmSnn76ac2YMUP58+e3tcfHx2vmzJkaP368mjZtqsDAQEVGRmrLli3atm1bVl8SAAAA4FCZDtEHDx6s9u3b6/jx41q4cKEWLlyoY8eOqW3btho0aFA2lAgAAAAgo1auXKmpU6eqQoUKtraKFStqypQpWrFiRabG6t+/v9q0aaPmzZvbte/cuVNJSUl27QEBASpRooS2bt1qOl5iYqISEhLsNgAAACCnu6Mn0V977TXlyvV/K8HkypVLr776qnbs2JGpsTZu3Kh27drJ19dXFotFixcvvmX/6OhoWSyWNFtsbGxmLwMAAAB4IKWkpMjFxSVNu4uLi1JSUjI8zvz587Vr1y6Fh4enORYbGytXV1fbcjGpvL29bzk3Dw8Pl5eXl23z8/PLcD0AAACAo2Q6RPf09FRMTEya9hMnTihv3ryZGuvy5cuqVq2apkyZkqnzDh06pNOnT9u2IkWKZOp8AAAA4EHVtGlTDRw4UKdOnbK1nTx5UoMHD1azZs0yNMaJEyc0cOBAzZ07V25ubllWW1hYmOLj423biRMnsmxsAAAAILtk+otFu3Xrpj59+uijjz5SvXr1JEk//vijhg0bph49emRqrNatW6t169aZLUFFihRJ89SLmcTERCUmJtr2+cgoAAAAHmSTJ09W+/btVapUKduT3idOnFDlypU1Z86cDI2xc+dOnTlzRjVr1rS1JScna+PGjZo8ebJWrVql69ev68KFC3bz8ri4OPn4+JiOa7VaZbVa7+zCAAAAAAfJdIj+0UcfyWKxKCQkRDdu3JD070dDX3jhBb3//vtZXmB6qlevrsTERFWuXFkjR45U/fr1TfuGh4dr1KhR96QuAAAAwNH8/Py0a9curVmzRgcPHpQkVahQIc265rfSrFkz7du3z66tV69eCggI0GuvvSY/Pz+5uLho7dq16ty5s6R/Py0aExOjoKCgrLsYAAAAIAfIdIju6uqqSZMmKTw8XEeOHJEklSlTRnny5Mny4v6raNGiioiIUK1atZSYmKjPPvtMjRs31vbt2+2ekrlZWFiYhgwZYttPSEhg7UUAAAA8cNatW6cBAwZo27Zt8vT0VIsWLdSiRQtJUnx8vCpVqqSIiAg1aNDgtmPlzZtXlStXtmtzd3dXwYIFbe19+vTRkCFDVKBAAXl6euqll15SUFCQ6tatm/UXBwAAADhQpkP0VHny5FGVKlWyspbbKl++vMqXL2/br1evno4cOaIJEyZo9uzZ6Z7DR0YBAADwMJg4caL69u0rT0/PNMe8vLz0/PPPa/z48RkK0TNiwoQJcnJyUufOnZWYmKjg4GBNnTo1S8YGAAAAcpJMh+jXrl3TJ598ovXr1+vMmTNKSUmxO75r164sKy4jHn30UW3evPmeviYAAACQ0/zyyy8aO3as6fGWLVvqo48+uuPxo6Oj7fbd3Nw0ZcoUTZky5Y7HBAAAAO4HmQ7R+/Tpox9++EFPPvmkHn30UVksluyoK8P27NmjokWLOrQGAAAAwNHi4uLk4uJiejxXrlw6e/bsPawIAAAAeDBkOkT/7rvv9P3339/yyzwz6tKlS/rjjz9s+8eOHdOePXtUoEABlShRQmFhYTp58qS++OILSf9+RNXf31+VKlXStWvX9Nlnn2ndunX64Ycf7roWAAAA4H5WrFgx7d+/X2XLlk33+N69e3n4BAAAALgDmQ7RixUrprx582bJi+/YsUNNmjSx7ad+AWhoaKiioqJ0+vRpxcTE2I5fv35dr7zyik6ePKk8efKoatWqWrNmjd0YAAAAwMPo8ccf1/Dhw9WqVSu5ubnZHbt69apGjBihtm3bOqg6AAAA4P6V6RB93Lhxeu211xQREaGSJUve1Ys3btxYhmGYHo+KirLbf/XVV/Xqq6/e1WsCAAAAD6K33npLCxcu1COPPKIBAwaofPnykqSDBw9qypQpSk5O1ptvvungKgEAAID7T6ZD9Fq1aunatWsqXbq08uTJk2bdxX/++SfLigMAAACQMd7e3tqyZYteeOEFhYWF2R5WsVgsCg4O1pQpU+Tt7e3gKgEAAID7T6ZD9B49eujkyZN677335O3t7fAvFgUAAADwr5IlS+r777/X+fPn9ccff8gwDJUrV0758+d3dGkAAADAfSvTIfqWLVu0detWVatWLTvqAQAAAHCX8ufPr9q1azu6DAAAAOCB4JTZEwICAnT16tXsqAUAAAAAAAAAgBwl0yH6+++/r1deeUXR0dE6d+6cEhIS7DYAAAAAAAAAAB4UmV7OpVWrVpKkZs2a2bUbhiGLxaLk5OSsqQwAAAAAAAAAAAfLdIi+fv367KgDAAAAAAAAAIAcJ9MheqNGjbKjDgAAAAAAAAAAcpxMr4kuSZs2bdIzzzyjevXq6eTJk5Kk2bNna/PmzVlaHAAAAAAAAAAAjpTpEP3bb79VcHCwcufOrV27dikxMVGSFB8fr/feey/LCwQAAAAAAAAAwFEyHaK/8847ioiI0IwZM+Ti4mJrr1+/vnbt2pWlxQEAAAAAAAAA4EiZDtEPHTqkhg0bpmn38vLShQsXsqImAAAAAAAAAAByhEyH6D4+Pvrjjz/StG/evFmlS5fOkqIAAAAAAAAAAMgJMh2i9+3bVwMHDtT27dtlsVh06tQpzZ07V0OHDtULL7yQHTUCAAAAAAAAAOAQuTJ7wuuvv66UlBQ1a9ZMV65cUcOGDWW1WjV06FC99NJL2VEjAAAAAAAAAAAOkekQ3WKx6M0339SwYcP0xx9/6NKlS6pYsaI8PDyyoz4AAAAAAAAAABwm0yF6KldXV1WsWDErawEAAAAAAAAAIEfJcIjeu3fvDPWbNWvWHRcDAAAAAAAAAEBOkuEQPSoqSiVLllSNGjVkGEZ21gQAAAAAAAAAQI6Q4RD9hRde0Jdffqljx46pV69eeuaZZ1SgQIHsrA0AAAAAAAAAAIdyymjHKVOm6PTp03r11Ve1bNky+fn5qWvXrlq1ahVPpgMAAAAAAAAAHkgZDtElyWq1qkePHlq9erV+++03VapUSS+++KJKlSqlS5cuZVeNAAAAAAAAAAA4RKZCdLsTnZxksVhkGIaSk5OzsiYAAAAAAAAAAHKETIXoiYmJ+vLLL9WiRQs98sgj2rdvnyZPnqyYmBh5eHhkV40AAAAAAAAAADhEhr9Y9MUXX9T8+fPl5+en3r1768svv1ShQoWyszYAAAAAAAAAABwqwyF6RESESpQoodKlS2vDhg3asGFDuv0WLlyYZcUBAAAAAAAAAOBIGQ7RQ0JCZLFYsrMWAAAAAAAAAABylAyH6FFRUdlYBgAAAAAAAAAAOU+mvlgUAAAAAAAAAICHCSE6AAAAAAAAAAAmCNEBAAAAAAAAADBBiA4AAAAAAAAAgAlCdAAAAAAAAAAATBCiAwAAAAAAAABgghAdAAAAAAAAAAAThOgAAAAAAAAAAJggRAcAAAAAAAAAwAQhOgAAAAAAAAAAJgjRAQAAAAAAAAAwQYgOAAAAAAAAAIAJQnQAAAAAAAAAAEwQogMAAAAAAAAAYIIQHQAAAAAAAAAAE4ToAAAAAAAAAACYIEQHAAAAAAAAAMAEIToAAAAAAAAAACYI0QEAAAAAAAAAMEGIDgAAACCNadOmqWrVqvL09JSnp6eCgoK0YsUK2/Fr166pf//+KliwoDw8PNS5c2fFxcU5sGIAAAAgexCiAwAAAEijePHiev/997Vz507t2LFDTZs2VYcOHfTrr79KkgYPHqxly5ZpwYIF2rBhg06dOqVOnTo5uGoAAAAg6+VydAEAAAAAcp527drZ7b/77ruaNm2atm3bpuLFi2vmzJmaN2+emjZtKkmKjIxUhQoVtG3bNtWtW9cRJQMAAADZgifRAQAAANxScnKy5s+fr8uXLysoKEg7d+5UUlKSmjdvbusTEBCgEiVKaOvWrabjJCYmKiEhwW4DAAAAcjpCdAAAAADp2rdvnzw8PGS1WtWvXz8tWrRIFStWVGxsrFxdXZUvXz67/t7e3oqNjTUdLzw8XF5eXrbNz88vm68AAAAAuHuE6AAAAADSVb58ee3Zs0fbt2/XCy+8oNDQUP322293PF5YWJji4+Nt24kTJ7KwWgAAACB7sCY6AAAAgHS5urqqbNmykqTAwED9/PPPmjRpkrp166br16/rwoULdk+jx8XFycfHx3Q8q9Uqq9Wa3WUDAAAAWYon0QEAAABkSEpKihITExUYGCgXFxetXbvWduzQoUOKiYlRUFCQAysEAAAAsh5PogMAAABIIywsTK1bt1aJEiV08eJFzZs3T9HR0Vq1apW8vLzUp08fDRkyRAUKFJCnp6deeuklBQUFqW7duo4uHQAAAMhShOgAAAAA0jhz5oxCQkJ0+vRpeXl5qWrVqlq1apVatGghSZowYYKcnJzUuXNnJSYmKjg4WFOnTnVw1QAAAEDWI0QHAAAAkMbMmTNvedzNzU1TpkzRlClT7lFFAAAAgGOwJjoAAAAAAAAAACYI0QEAAAAAAAAAMEGIDgAAAAAAAACACUJ0AAAAAAAAAABMEKIDAAAAAAAAAGCCEB0AAAAAAAAAABOE6AAAAAAAAAAAmMjl6AIAAAAAAMhpDGcXxVftYbcPAAAeToToAAAAAAD8l8UiI5ero6sAAAA5AMu5AAAAAAAAAABgghAdAAAAAAAAAAAThOgAAAAAAAAAAJhwaIi+ceNGtWvXTr6+vrJYLFq8ePFtz4mOjlbNmjVltVpVtmxZRUVFZXudAAAAAAAAAICHk0ND9MuXL6tatWqaMmVKhvofO3ZMbdq0UZMmTbRnzx4NGjRIzz77rFatWpXNlQIAAAAAAAAAHka5HPnirVu3VuvWrTPcPyIiQv7+/ho3bpwkqUKFCtq8ebMmTJig4ODg7CoTAAAAAAAAAPCQuq/WRN+6dauaN29u1xYcHKytW7eanpOYmKiEhAS7DQAAAAAAAACAjLivQvTY2Fh5e3vbtXl7eyshIUFXr15N95zw8HB5eXnZNj8/v3tRKgAAAAAAAADgAXBfheh3IiwsTPHx8bbtxIkTji4JAAAAAAAAAHCfcOia6Jnl4+OjuLg4u7a4uDh5enoqd+7c6Z5jtVpltVrvRXkAAAAAAAAAgAfMffUkelBQkNauXWvXtnr1agUFBTmoIgAAAAAAAADAg8yhIfqlS5e0Z88e7dmzR5J07Ngx7dmzRzExMZL+XYolJCTE1r9fv346evSoXn31VR08eFBTp07V119/rcGDBzuifAAAAAAAAADAA86hIfqOHTtUo0YN1ahRQ5I0ZMgQ1ahRQ2+//bYk6fTp07ZAXZL8/f21fPlyrV69WtWqVdO4ceP02WefKTg42CH1AwAAAAAAAAAebA5dE71x48YyDMP0eFRUVLrn7N69OxurAgAAAAAAAADgX/fVmugAAAAAAAAAANxLhOgAAAAAAAAAAJggRAcAAAAAAAAAwAQhOgAAAAAAAAAAJgjRAQAAAAAAAAAwQYgOAAAAAAAAAIAJQnQAAAAAAAAAAEwQogMAAAAAAAAAYIIQHQAAAAAAAAAAE7kcXQAAAAAAAACAB4vh7KL4qj3s9oH7FSE6AAAAAAAAgKxlscjI5eroKoAswXIuAAAAAAAAAACYIEQHAAAAAAAAAMAEIToAAAAAAAAAACYI0QEAAAAAAAAAMEGIDgAAAAAAAACACUJ0AAAAAAAAAABMEKIDAAAAAAAAAGCCEB0AAAAAAAAAABOE6AAAAAAAAAAAmCBEBwAAAAAAAADABCE6AAAAAAAAAAAmCNEBAAAAAAAAADBBiA4AAAAAAAAAgAlCdAAAAAAAAAAATBCiAwAAAAAAAABgghAdAAAAAAAAAAAThOgAAAAAAAAAAJggRAcAAACQRnh4uGrXrq28efOqSJEi6tixow4dOmTX59q1a+rfv78KFiwoDw8Pde7cWXFxcQ6qGAAAAMgehOgAAAAA0tiwYYP69++vbdu2afXq1UpKSlLLli11+fJlW5/Bgwdr2bJlWrBggTZs2KBTp06pU6dODqwaAAAAyHq5HF0AAAAAgJxn5cqVdvtRUVEqUqSIdu7cqYYNGyo+Pl4zZ87UvHnz1LRpU0lSZGSkKlSooG3btqlu3bppxkxMTFRiYqJtPyEhIXsvAgAAAMgCPIkOAAAA4Lbi4+MlSQUKFJAk7dy5U0lJSWrevLmtT0BAgEqUKKGtW7emO0Z4eLi8vLxsm5+fX/YXDgAAANwlQnQAAAAAt5SSkqJBgwapfv36qly5siQpNjZWrq6uypcvn11fb29vxcbGpjtOWFiY4uPjbduJEyeyu3QAAADgrrGcCwAAAIBb6t+/v/bv36/Nmzff1ThWq1VWqzWLqgIAAADuDZ5EBwAAAGBqwIAB+u6777R+/XoVL17c1u7j46Pr16/rwoULdv3j4uLk4+Nzj6sEAAAAsg8hOgAAAIA0DMPQgAEDtGjRIq1bt07+/v52xwMDA+Xi4qK1a9fa2g4dOqSYmBgFBQXd63IBAACAbMNyLgAAAADS6N+/v+bNm6clS5Yob968tnXOvby8lDt3bnl5ealPnz4aMmSIChQoIE9PT7300ksKCgpS3bp1HVw9AAAAkHUI0QEAAACkMW3aNElS48aN7dojIyPVs2dPSdKECRPk5OSkzp07KzExUcHBwZo6deo9rhQAAADIXoToAAAAANIwDOO2fdzc3DRlyhRNmTLlHlQEAAAAOAZrogMAAAAAAAAAYIIQHQAAAAAAAAAAE4ToAAAAAAAAAACYIEQHAAAAAAAAAMAEIToAAAAAAAAAACYI0QEAAAAAAAAAMEGIDgAAAAAAAACACUJ0AAAAAAAAAABMEKIDAAAAAAAAAGCCEB0AAAAAAAAAABOE6AAAAAAAAAAAmCBEBwAAAAAAAADABCE6AAAAAAAAAAAmCNEBAAAAAAAAADBBiA4AAAAAAAAAgAlCdAAAAAAAAAAATBCiAwAAAAAAAABgghAdAAAAAAAAAAAThOgAAAAAAAAAAJggRAcAAAAAAAAAwAQhOgAAAAAAAAAAJgjRAQAAAAAAAAAwQYgOAAAAAAAAAIAJQnQAAAAAAAAAAEwQogMAAAAAAAAAYIIQHQAAAAAAAAAAE4ToAAAAAAAAAACYIEQHAAAAAAAAAMAEIToAAAAAAAAAACYI0QEAAAAAAAAAMEGIDgAAAAAAAACAiRwRok+ZMkWlSpWSm5ub6tSpo59++sm0b1RUlCwWi93m5uZ2D6sFAAAAAAAAADwsHB6if/XVVxoyZIhGjBihXbt2qVq1agoODtaZM2dMz/H09NTp06dt259//nkPKwYAAAAAAAAAPCwcHqKPHz9effv2Va9evVSxYkVFREQoT548mjVrluk5FotFPj4+ts3b2/seVgwAAAAAAAAAeFg4NES/fv26du7cqebNm9vanJyc1Lx5c23dutX0vEuXLqlkyZLy8/NThw4d9Ouvv5r2TUxMVEJCgt0GAAAAAAAAAEBGODRE//vvv5WcnJzmSXJvb2/Fxsame0758uU1a9YsLVmyRHPmzFFKSorq1aunv/76K93+4eHh8vLysm1+fn5Zfh0AAAAAAAAAgAeTw5dzyaygoCCFhISoevXqatSokRYuXKjChQvr008/Tbd/WFiY4uPjbduJEyfuccUAAAAAAAAAgPtVLke+eKFCheTs7Ky4uLj/197dB1lV138Af19QF5UWFJMFWgGzB1DCgDRKU54CEwV0EslGHpKiskxGK00ltaInES0bJ0vQrHQ0Wk2FyVndMGU0TcpqNR8gaIQFTURIAdn7+8Nxc39wS2Dh7sLrNXNnON/zPed87v3j3M+8+Z67zcYbGhpSVVX1ls6x99575/3vf3+efvrpre6vqKhIRUXFDtcKAAAAAMCep6wr0ffZZ58MHDgwtbW1TWONjY2pra3N4MGD39I5Nm/enMcffzzdunXbWWUCAAAAALCHKutK9CSZPn16Jk6cmEGDBuWoo47K7Nmzs379+kyePDlJcuaZZ6ZHjx6ZOXNmkuSyyy7LBz/4wRx22GFZs2ZNvve97+Uf//hHzjrrrHK+DQAAAAAAdkNlD9HHjx+f1atX55JLLsnKlStz5JFHZsGCBU1/bHTZsmVp1+4/C+ZffPHFTJ06NStXrswBBxyQgQMH5sEHH0zfvn3L9RYAAAAAANhNlT1ET5Kzzz47Z5999lb31dXVNdu+8sorc+WVV+6CqgAAAAAA2NOV9TfRAQAAAACgNROiAwAAAABACUJ0AAAAAAAoQYgOAAAAAAAlCNEBAAAAAKAEIToAAAAAAJQgRAcAAAAAgBKE6AAAAAAAUIIQHQAAAAAAShCiAwAAW1i4cGFOOumkdO/ePYVCITU1Nc32F4vFXHLJJenWrVv23XffDB8+PE899VR5igUAgJ1IiA4AAGxh/fr16d+/f6655pqt7v/ud7+bq6++Otdee20eeuih7L///hk5cmReffXVXVwpAADsXHuVuwAAAKD1OeGEE3LCCSdsdV+xWMzs2bNz0UUXZcyYMUmSG2+8MV27dk1NTU1OP/30rR63YcOGbNiwoWl77dq1LV84AAC0MCvRAQCAbbJkyZKsXLkyw4cPbxrr1KlTjj766CxatKjkcTNnzkynTp2aXtXV1buiXAAA2CFCdAAAYJusXLkySdK1a9dm4127dm3atzUXXHBBXnrppabX8uXLd2qdAADQEvycCwAAsEtUVFSkoqKi3GUAAMA2sRIdAADYJlVVVUmShoaGZuMNDQ1N+wAAYHchRAcAALZJ7969U1VVldra2qaxtWvX5qGHHsrgwYPLWBkAALQ8P+cCAABsYd26dXn66aebtpcsWZLFixfnwAMPzCGHHJIvfelL+cY3vpF3vetd6d27dy6++OJ07949Y8eOLV/RAACwEwjRAQCALTzyyCMZMmRI0/b06dOTJBMnTszcuXPz5S9/OevXr8+nP/3prFmzJsccc0wWLFiQDh06lKtkAADYKYToAADAFo4//vgUi8WS+wuFQi677LJcdtllu7AqAADY9fwmOgAAAAAAlCBEBwAAAACAEoToAAAAAABQghAdAAAAAABKEKIDAAAAAEAJQnQAAAAAAChBiA4AAAAAACUI0QEAAAAAoAQhOgAAAAAAlCBEBwAAAACAEoToAAAAAABQghAdAAAAAABKEKIDAAAAAEAJQnQAAAAAAChBiA4AAAAAACUI0QEAAAAAoAQhOgAAAAAAlCBEBwAAAACAEoToAAAAAABQghAdAAAAAABKEKIDAAAAAEAJQnQAAAAAAChBiA4AAAAAACUI0QEAAAAAoAQhOgAAAAAAlCBEBwAAAACAEoToAAAAAABQghAdAAAAAABKEKIDAAAAAEAJQnQAAAAAAChBiA4AAAAAACUI0QEAAAAAoAQhOgAAAAAAlCBEBwAAAACAEoToAAAAAABQghAdAAAAAABKEKIDAAAAAEAJQnQAAAAAAChBiA4AAAAAACUI0QEAAAAAoAQhOgAAAAAAlCBEBwAAAACAEoToAAAAAABQghAdAAAAAABKEKIDAAAAAEAJQnQAAAAAAChBiA4AAAAAACUI0QEAAAAAoAQhOgAAAAAAlCBEBwAAAACAElpFiH7NNdekV69e6dChQ44++ug8/PDD/3X+rbfemve+973p0KFD+vXrl7vvvnsXVQoAALzZtvbyAADQ1pQ9RL/lllsyffr0zJgxI3/84x/Tv3//jBw5MqtWrdrq/AcffDATJkzIpz71qTz22GMZO3Zsxo4dm7/85S+7uHIAANizbWsvDwAAbVHZQ/RZs2Zl6tSpmTx5cvr27Ztrr702++23X66//vqtzr/qqqsyatSonH/++enTp08uv/zyDBgwID/84Q93ceUAALBn29ZeHgAA2qK9ynnxjRs35tFHH80FF1zQNNauXbsMHz48ixYt2uoxixYtyvTp05uNjRw5MjU1NVudv2HDhmzYsKFp+6WXXkqSrF27dger33GbN7xS7hKANqg13L9aC/dRYHuU+z76xvWLxWJZ69hR29PL682B3UlruHe1Ju6jwLZqDffRt9qblzVEf/7557N58+Z07dq12XjXrl3zxBNPbPWYlStXbnX+ypUrtzp/5syZufTSS7cYr66u3s6qAcqr0w+mlbsEgDattdxHX3755XTq1KncZWy37enl9ebA7qS1fJ8AtFWt6T76v3rzsobou8IFF1zQbOV6Y2Nj/vWvf6VLly4pFAplrAy2bu3atamurs7y5ctTWVlZ7nIA2hz3UVq7YrGYl19+Od27dy93Kbuc3py2xncKwI5xH6W1e6u9eVlD9IMOOijt27dPQ0NDs/GGhoZUVVVt9Ziqqqptml9RUZGKiopmY507d97+omEXqays9AUDsAPcR2nN2vIK9DdsTy+vN6et8p0CsGPcR2nN3kpvXtY/LLrPPvtk4MCBqa2tbRprbGxMbW1tBg8evNVjBg8e3Gx+ktxzzz0l5wMAAC1ve3p5AABoi8r+cy7Tp0/PxIkTM2jQoBx11FGZPXt21q9fn8mTJydJzjzzzPTo0SMzZ85Mkpxzzjk57rjjcsUVV+TEE0/MzTffnEceeSQ//vGPy/k2AABgj/O/enkAANgdlD1EHz9+fFavXp1LLrkkK1euzJFHHpkFCxY0/YGiZcuWpV27/yyY/9CHPpRf/OIXueiii3LhhRfmXe96V2pqanLEEUeU6y1Ai6qoqMiMGTO2eNQZgLfGfRR2nf/Vy0Nb5zsFYMe4j7K7KBSLxWK5iwAAAAAAgNaorL+JDgAAAAAArZkQHQAAAAAAShCiAwAAAABACUJ0aMV69eqV2bNnN20XCoXU1NSUrR6Atmju3Lnp3LlzucsAoA3TlwPsOH05bZkQHbbDpEmTUigUUigUsvfee6dr164ZMWJErr/++jQ2Nu60665YsSInnHDCTjs/wK72xv102rRpW+z7/Oc/n0KhkEmTJu36wgBoE/TlAC1DXw7/nRAdttOoUaOyYsWKLF26NPPnz8+QIUNyzjnnZPTo0Xnttdd2yjWrqqpSUVGxU84NUC7V1dW5+eab88orrzSNvfrqq/nFL36RQw45ZIfOvWnTph0tbwvFYnGn3ecB2Hb6coCWoS+H0oTosJ0qKipSVVWVHj16ZMCAAbnwwgtz++23Z/78+Zk7d26SZNmyZRkzZkw6duyYysrKnHbaaWloaGh2nt/85jf5wAc+kA4dOuSggw7KuHHjSl7zzY+NLl26NIVCIfPmzcuQIUOy3377pX///lm0aFGzY6677rpUV1dnv/32y7hx4zJr1iyPTwGtyoABA1JdXZ158+Y1jc2bNy+HHHJI3v/+9zeNLViwIMccc0w6d+6cLl26ZPTo0XnmmWea9r9xX7zlllty3HHHpUOHDvn5z3++xfVWr16dQYMGZdy4cdmwYUMaGxszc+bM9O7dO/vuu2/69++f2267rWl+XV1dCoVC5s+fn4EDB6aioiK///3vd9KnAcC20pcDtAx9OZQmRIcWNHTo0PTv3z/z5s1LY2NjxowZk3/961/53e9+l3vuuSfPPvtsxo8f3zT/rrvuyrhx4/Kxj30sjz32WGpra3PUUUdt0zW/9rWv5bzzzsvixYvz7ne/OxMmTGj6n9gHHngg06ZNyznnnJPFixdnxIgR+eY3v9mi7xmgJUyZMiVz5sxp2r7++uszefLkZnPWr1+f6dOn55FHHkltbW3atWuXcePGbfG4/le/+tWcc845qa+vz8iRI5vtW758eY499tgcccQRue2221JRUZGZM2fmxhtvzLXXXpu//vWvOffcc/PJT34yv/vd77Y477e//e3U19fnfe97Xwt/AgC0JH05wPbRl0MJRWCbTZw4sThmzJit7hs/fnyxT58+xd/+9rfF9u3bF5ctW9a0769//WsxSfHhhx8uFovF4uDBg4tnnHFGyev07NmzeOWVVzZtJyn++te/LhaLxeKSJUuKSYo/+clPtjh/fX19Uy0nnnhis3OeccYZxU6dOm3DuwXYed64n65atapYUVFRXLp0aXHp0qXFDh06FFevXl0cM2ZMceLEiVs9dvXq1cUkxccff7xYLP7nvjh79uxm8+bMmVPs1KlT8YknnihWV1cXv/jFLxYbGxuLxWKx+Oqrrxb322+/4oMPPtjsmE996lPFCRMmFIvFYvG+++4rJinW1NS08LsHYEfpywFahr4c/jsr0aGFFYvFFAqF1NfXp7q6OtXV1U37+vbtm86dO6e+vj5Jsnjx4gwbNmyHrvfm/3Xt1q1bkmTVqlVJkieffHKLFTTbuqIGYFd4+9vfnhNPPDFz587NnDlzcuKJJ+aggw5qNuepp57KhAkTcuihh6aysjK9evVK8voj+m82aNCgLc7/yiuv5Nhjj80pp5ySq666KoVCIUny9NNP59///ndGjBiRjh07Nr1uvPHGZo+kljovAK2Xvhxg2+nLYev2KncBsLupr69P796939Lcfffdd4evt/feezf9+40vn///CBVAWzBlypScffbZSZJrrrlmi/0nnXRSevbsmeuuuy7du3dPY2NjjjjiiGzcuLHZvP3333+LYysqKjJ8+PDceeedOf/889OjR48kybp165K8/hj/G2NvPuZ/nReA1ktfDrB99OWwJSvRoQXde++9efzxx3PqqaemT58+Wb58eZYvX960/29/+1vWrFmTvn37Jnl9tUptbe1Oq+c973lP/vCHPzQb+//bAK3FqFGjsnHjxmzatGmL30x84YUX8uSTT+aiiy7KsGHD0qdPn7z44otv+dzt2rXLz372swwcODBDhgzJc889l+T1lYgVFRVZtmxZDjvssGavN69YBKBt0ZcDbD99OWzJSnTYThs2bMjKlSuzefPmNDQ0ZMGCBZk5c2ZGjx6dM888M+3atUu/fv1yxhlnZPbs2Xnttdfyuc99Lscdd1zTo0czZszIsGHD8s53vjOnn356Xnvttdx99935yle+0iI1fuELX8hHPvKRzJo1KyeddFLuvffezJ8/v2llDEBr0r59+6bH6tu3b99s3wEHHJAuXbrkxz/+cbp165Zly5blq1/96jaf/+c//3kmTJiQoUOHpq6uLlVVVTnvvPNy7rnnprGxMcccc0xeeumlPPDAA6msrMzEiRNb7P0BsHPoywFalr4ctmQlOmynBQsWpFu3bunVq1dGjRqV++67L1dffXVuv/32tG/fPoVCIbfffnsOOOCAfOQjH8nw4cNz6KGH5pZbbmk6x/HHH59bb701d9xxR4488sgMHTo0Dz/8cIvV+OEPfzjXXnttZs2alf79+2fBggU599xz06FDhxa7BkBLqqysTGVl5Rbj7dq1y80335xHH300RxxxRM4999x873vf2+bz77XXXvnlL3+Zww8/PEOHDs2qVaty+eWX5+KLL87MmTPTp0+fjBo1Knfddddb/gkAAMpLXw7Q8vTl0FyhWCwWy10EsOtMnTo1TzzxRO6///5ylwIAAHssfTkAtB1+zgV2c9///vczYsSI7L///pk/f35uuOGG/OhHPyp3WQAAsEfRlwNA22UlOuzmTjvttNTV1eXll1/OoYcemi984QuZNm1aucsCAIA9ir4cANouIToAAAAAAJTgD4sCAAAAAEAJQnQAAAAAAChBiA4AAAAAACUI0QEAAAAAoAQhOgAAAAAAlCBEBwAAAACAEoToALuh1atX57Of/WwOOeSQVFRUpKqqKiNHjswDDzyww+eeNGlSxo4du+NFAgDAHkBvDtD27VXuAgBoeaeeemo2btyYG264IYceemgaGhpSW1ubF154odylAQDAHkVvDtD2WYkOsJtZs2ZN7r///nznO9/JkCFD0rNnzxx11FG54IILcvLJJ2fKlCkZPXp0s2M2bdqUgw8+OD/96U+TJLfddlv69euXfffdN126dMnw4cOzfv36fP3rX88NN9yQ22+/PYVCIYVCIXV1dUmS5cuX57TTTkvnzp1z4IEHZsyYMVm6dGnTNd5YJfOtb30rXbt2TefOnXPZZZfltddey/nnn58DDzww73jHOzJnzpxd9VEBAMBOpTcH2D0I0QF2Mx07dkzHjh1TU1OTDRs2bLH/rLPOyoIFC7JixYqmsTvvvDP//ve/M378+KxYsSITJkzIlClTUl9fn7q6upxyyikpFos577zzctppp2XUqFFZsWJFVqxYkQ996EPZtGlTRo4cmbe97W25//7788ADD6Rjx44ZNWpUNm7c2HSde++9N88991wWLlyYWbNmZcaMGRk9enQOOOCAPPTQQ5k2bVo+85nP5J///Ocu+awAAGBn0psD7B4KxWKxWO4iAGhZv/rVrzJ16tS88sorGTBgQI477ricfvrped/73pckOfzwwzNx4sR8+ctfTpKcfPLJ6dKlS+bMmZM//vGPGThwYJYuXZqePXtuce5JkyZlzZo1qampaRq76aab8o1vfCP19fUpFApJko0bN6Zz586pqanJRz/60UyaNCl1dXV59tln067d6/+H+973vjcHH3xwFi5cmCTZvHlzOnXqlJ/85Cc5/fTTd+ZHBAAAu4TeHKDtsxIdYDd06qmn5rnnnssdd9yRUaNGpa6uLgMGDMjcuXOTvL7i5Y1HMxsaGjJ//vxMmTIlSdK/f/8MGzYs/fr1y8c//vFcd911efHFF//r9f70pz/l6aefztve9ram1TYHHnhgXn311TzzzDNN8w4//PCmJj1Junbtmn79+jVtt2/fPl26dMmqVata6qMAAICy0psDtH1CdIDdVIcOHTJixIhcfPHFefDBBzNp0qTMmDEjSXLmmWfm2WefzaJFi3LTTTeld+/eOfbYY5O83izfc889mT9/fvr27Zsf/OAHec973pMlS5aUvNa6desycODALF68uNnr73//ez7xiU80zdt7772bHVcoFLY61tjY2FIfAwAAlJ3eHKBtE6ID7CH69u2b9evXJ0m6dOmSsWPHZs6cOZk7d24mT57cbG6hUMiHP/zhXHrppXnssceyzz775Ne//nWSZJ999snmzZubzR8wYECeeuqpHHzwwTnssMOavTp16rRr3iAAALQRenOAtkWIDrCbeeGFFzJ06NDcdNNN+fOf/5wlS5bk1ltvzXe/+92MGTOmad5ZZ52VG264IfX19Zk4cWLT+EMPPZRvfetbeeSRR7Js2bLMmzcvq1evTp8+fZIkvXr1yp///Oc8+eSTef7557Np06acccYZOeiggzJmzJjcf//9WbJkSerq6vLFL37RHyICAGCPpTcH2D3sVe4CAGhZHTt2zNFHH50rr7wyzzzzTDZt2pTq6upMnTo1F154YdO84cOHp1u3bjn88MPTvXv3pvHKysosXLgws2fPztq1a9OzZ89cccUVOeGEE5IkU6dOTV1dXQYNGpR169blvvvuy/HHH5+FCxfmK1/5Sk455ZS8/PLL6dGjR4YNG5bKyspd/hkAAEBroDcH2D0UisVisdxFALDrrVu3Lj169MicOXNyyimnlLscAADYY+nNAVo3K9EB9jCNjY15/vnnc8UVV6Rz5845+eSTy10SAADskfTmAG2DEB1gD7Ns2bL07t0773jHOzJ37tzstZevAgAAKAe9OUDb4OdcAAAAAACghHblLgAAAAAAAForIToAAAAAAJQgRAcAAAAAgBKE6AAAAAAAUIIQHQAAAAAAShCiAwAAAABACUJ0AAAAAAAoQYgOAAAAAAAl/B/d3d1MIqW8cwAAAABJRU5ErkJggg==\n"}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["<Figure size 1000x600 with 2 Axes>"], "image/png": "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\n"}, "metadata": {}}], "source": ["# Cell 8: Display Results and Visualizations\n", "\n", "if extractions and not results_df.empty:\n", "    print(\"📊 GPU-OPTIMIZED BENCHMARK RESULTS\")\n", "    print(\"=\" * 70)\n", "    display(results_df)\n", "\n", "    print(f\"\\n📈 SUMMARY STATISTICS\")\n", "    print(\"=\" * 70)\n", "    display(summary_df)\n", "\n", "    # Create visualizations\n", "    plt.style.use('default')\n", "    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))\n", "\n", "    # 1. Processing Time Comparison\n", "    sns.barplot(data=results_df, x='System', y='Processing_Time', ax=ax1)\n", "    ax1.set_title('🚀 Processing Time by OCR System')\n", "    ax1.set_ylabel('Time (seconds)')\n", "\n", "    # 2. Character Accuracy Comparison\n", "    sns.barplot(data=results_df, x='System', y='Character_Accuracy', ax=ax2)\n", "    ax2.set_title('📝 Character Accuracy by OCR System')\n", "    ax2.set_ylabel('Accuracy (0-1)')\n", "    ax2.set_ylim(0, 1)\n", "\n", "    # 3. GPU Memory Usage\n", "    if device_info['cuda_available']:\n", "        sns.barplot(data=results_df, x='System', y='GPU_Memory_Used', ax=ax3)\n", "        ax3.set_title('🔥 GPU Memory Usage by OCR System')\n", "        ax3.set_ylabel('Memory (GB)')\n", "    else:\n", "        ax3.text(0.5, 0.5, 'GPU Not Available', ha='center', va='center', transform=ax3.transAxes)\n", "        ax3.set_title('🔥 GPU Memory Usage (Not Available)')\n", "\n", "    # 4. Scientific Elements Detection\n", "    sns.barplot(data=results_df, x='System', y='Scientific_Elements_Total', ax=ax4)\n", "    ax4.set_title('🔬 Scientific Elements Detected')\n", "    ax4.set_ylabel('Count')\n", "\n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "    # Performance vs Accuracy scatter plot\n", "    plt.figure(figsize=(10, 6))\n", "    scatter = plt.scatter(results_df['Processing_Time'], results_df['Character_Accuracy'],\n", "                         c=results_df['GPU_Memory_Used'], s=100, alpha=0.7, cmap='viridis')\n", "\n", "    for i, txt in enumerate(results_df['System']):\n", "        plt.annotate(txt, (results_df['Processing_Time'].iloc[i], results_df['Character_Accuracy'].iloc[i]),\n", "                    xytext=(5, 5), textcoords='offset points')\n", "\n", "    plt.xlabel('Processing Time (seconds)')\n", "    plt.ylabel('Character Accuracy')\n", "    plt.title('⚖️ Performance vs Accuracy Trade-off')\n", "    plt.colorbar(scatter, label='GPU Memory Used (GB)')\n", "    plt.grid(True, alpha=0.3)\n", "    plt.show()"]}, {"cell_type": "code", "source": [], "metadata": {"id": "ZMYx7E2i1zAW"}, "id": "ZMYx7E2i1zAW", "execution_count": null, "outputs": []}], "metadata": {"language_info": {"name": "python"}, "colab": {"provenance": [], "gpuType": "T4", "include_colab_link": true}, "accelerator": "GPU", "kernelspec": {"name": "python3", "display_name": "Python 3"}}, "nbformat": 4, "nbformat_minor": 5}