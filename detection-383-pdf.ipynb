{"cells": [{"cell_type": "code", "execution_count": null, "id": "b85a29f7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The autoreload extension is already loaded. To reload it, use:\n", "  %reload_ext autoreload\n"]}], "source": ["# This helps to live-reload the code in scripts.ocr_text to this notebook\n", "%load_ext autoreload\n", "%autoreload 2\n", "\n", "import os, shutil\n", "import fitz  \n", "import tqdm\n", "\n", "import pandas as pd\n", "from pymupdf4llm import to_markdown\n", "\n", "# Highly recommended to import the modules to reuse code (avoiding repeated code in the ipynb)\n", "from scripts.ocr_text import (\n", "    is_scanned_pdf, \n", "    process_pdf_pipeline, \n", "    analyze_markdown_header_hierarchy\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "e48f2000", "metadata": {}, "outputs": [], "source": ["def analyze_pdf(pdf_path):\n", "    \"\"\"Analyze a single PDF file and return a dict with its properties\"\"\"\n", "    record = {\n", "        'filename': os.path.basename(pdf_path),\n", "        'file_size_mb': round(os.path.getsize(pdf_path) / (1024*1024), 3)\n", "    }\n", "    try:\n", "        doc = fitz.open(pdf_path)\n", "        record['page_count'] = len(doc)\n", "        toc = doc.get_toc()\n", "        record['has_toc'] = bool(toc)\n", "        record['toc_entries'] = len(toc) if toc else 0\n", "        record['is_scanned'] = is_scanned_pdf(pdf_path)\n", "        # --- Header structure analysis ---\n", "        tmp_pdf_path = './temp_ocr/tmp.pdf'\n", "        shutil.copy(pdf_path, tmp_pdf_path)\n", "        md_full = process_pdf_pipeline(tmp_pdf_path)\n", "        record['markdown_status'] = 'success' if md_full.strip() else 'empty'\n", "\n", "        # Add header analysis results to record\n", "        record.update(analyze_markdown_header_hierarchy(md_full))\n", "    except Exception as e:\n", "        record['page_count'] = None\n", "        record['has_toc'] = None\n", "        record['toc_entries'] = None\n", "        record['is_scanned'] = None\n", "        record['markdown_status'] = 'error'\n", "        record['header_level_counts'] = None\n", "        record['header_assessment'] = str(e)\n", "    return record\n", "# PDF Folder with 383 pdfs\n", "pdf_folder = \"383-pdfs\"  "]}, {"cell_type": "code", "execution_count": 11, "id": "def22e5d", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": [" 99%|█████████▉| 381/383 [27:21<00:08,  4.11s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["📄 Detected born-digital PDFd margins saved: output_markdown/tmp.md\r"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|█████████▉| 382/383 [27:27<00:04,  4.51s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["📄 Detected born-digital PDFd margins saved: output_markdown/tmp.md\r"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 383/383 [27:29<00:00,  4.31s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Markdown with hierarchy and margins saved: output_markdown/tmp.md\r"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["# List all PDFs in the folder, analyze each, store results\n", "records = []\n", "file_list = sorted([f for f in os.listdir(pdf_folder) if f.lower().endswith('.pdf')])\n", "\n", "for fname in tqdm.tqdm(file_list):\n", "    fpath = os.path.join(pdf_folder, fname)\n", "    rec = analyze_pdf(fpath)\n", "    records.append(rec)"]}, {"cell_type": "code", "execution_count": 25, "id": "b43064bf", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Saved detail table to pdfs_analysis_table.csv\n"]}, {"data": {"text/plain": ["(383, 15)"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["df = pd.DataFrame(records)\n", "df.to_csv(\"pdfs_analysis_table.csv\", index=False)\n", "print(\"✅ Saved detail table to pdfs_analysis_table.csv\")\n", "df.shape"]}, {"cell_type": "code", "execution_count": 26, "id": "03848522", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>filename</th>\n", "      <th>file_size_mb</th>\n", "      <th>page_count</th>\n", "      <th>has_toc</th>\n", "      <th>toc_entries</th>\n", "      <th>is_scanned</th>\n", "      <th>markdown_status</th>\n", "      <th>hdr_level_2</th>\n", "      <th>hdr_level_3</th>\n", "      <th>assessment</th>\n", "      <th>hdr_level_1</th>\n", "      <th>hdr_level_4</th>\n", "      <th>hdr_level_5</th>\n", "      <th>header_level_counts</th>\n", "      <th>header_assessment</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>383</td>\n", "      <td>383.000000</td>\n", "      <td>381.000000</td>\n", "      <td>381</td>\n", "      <td>381.000000</td>\n", "      <td>381</td>\n", "      <td>383</td>\n", "      <td>282.000000</td>\n", "      <td>176.000000</td>\n", "      <td>381</td>\n", "      <td>274.000000</td>\n", "      <td>31.000000</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>unique</th>\n", "      <td>383</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>2</td>\n", "      <td>NaN</td>\n", "      <td>1</td>\n", "      <td>3</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>6</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>top</th>\n", "      <td>12936_2015_Article_885_pdf.pdf</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>True</td>\n", "      <td>NaN</td>\n", "      <td>False</td>\n", "      <td>success</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>Header hierarchy appears reasonable.</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>Choose only one of --force-ocr, --skip-text, -...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>freq</th>\n", "      <td>1</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>203</td>\n", "      <td>NaN</td>\n", "      <td>381</td>\n", "      <td>370</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>201</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>NaN</td>\n", "      <td>0.946833</td>\n", "      <td>12.984252</td>\n", "      <td>NaN</td>\n", "      <td>13.157480</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>7.872340</td>\n", "      <td>9.193182</td>\n", "      <td>NaN</td>\n", "      <td>4.354015</td>\n", "      <td>4.612903</td>\n", "      <td>2.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>NaN</td>\n", "      <td>2.646645</td>\n", "      <td>14.468287</td>\n", "      <td>NaN</td>\n", "      <td>14.243979</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>6.255362</td>\n", "      <td>13.501626</td>\n", "      <td>NaN</td>\n", "      <td>5.270924</td>\n", "      <td>2.800922</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>NaN</td>\n", "      <td>0.033000</td>\n", "      <td>1.000000</td>\n", "      <td>NaN</td>\n", "      <td>0.000000</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "      <td>NaN</td>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "      <td>2.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>NaN</td>\n", "      <td>0.243000</td>\n", "      <td>7.000000</td>\n", "      <td>NaN</td>\n", "      <td>0.000000</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>1.000000</td>\n", "      <td>2.000000</td>\n", "      <td>NaN</td>\n", "      <td>1.000000</td>\n", "      <td>2.500000</td>\n", "      <td>2.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>NaN</td>\n", "      <td>0.477000</td>\n", "      <td>9.000000</td>\n", "      <td>NaN</td>\n", "      <td>8.000000</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>7.000000</td>\n", "      <td>7.000000</td>\n", "      <td>NaN</td>\n", "      <td>1.000000</td>\n", "      <td>4.000000</td>\n", "      <td>2.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>NaN</td>\n", "      <td>0.801000</td>\n", "      <td>13.000000</td>\n", "      <td>NaN</td>\n", "      <td>26.000000</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>12.000000</td>\n", "      <td>12.000000</td>\n", "      <td>NaN</td>\n", "      <td>9.000000</td>\n", "      <td>6.000000</td>\n", "      <td>2.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>NaN</td>\n", "      <td>39.900000</td>\n", "      <td>106.000000</td>\n", "      <td>NaN</td>\n", "      <td>55.000000</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>35.000000</td>\n", "      <td>102.000000</td>\n", "      <td>NaN</td>\n", "      <td>40.000000</td>\n", "      <td>14.000000</td>\n", "      <td>2.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                              filename  file_size_mb  page_count has_toc  \\\n", "count                              383    383.000000  381.000000     381   \n", "unique                             383           NaN         NaN       2   \n", "top     12936_2015_Article_885_pdf.pdf           NaN         NaN    True   \n", "freq                                 1           NaN         NaN     203   \n", "mean                               NaN      0.946833   12.984252     NaN   \n", "std                                NaN      2.646645   14.468287     NaN   \n", "min                                NaN      0.033000    1.000000     NaN   \n", "25%                                NaN      0.243000    7.000000     NaN   \n", "50%                                NaN      0.477000    9.000000     NaN   \n", "75%                                NaN      0.801000   13.000000     NaN   \n", "max                                NaN     39.900000  106.000000     NaN   \n", "\n", "        toc_entries is_scanned markdown_status  hdr_level_2  hdr_level_3  \\\n", "count    381.000000        381             383   282.000000   176.000000   \n", "unique          NaN          1               3          NaN          NaN   \n", "top             NaN      False         success          NaN          NaN   \n", "freq            NaN        381             370          NaN          NaN   \n", "mean      13.157480        NaN             NaN     7.872340     9.193182   \n", "std       14.243979        NaN             NaN     6.255362    13.501626   \n", "min        0.000000        NaN             NaN     1.000000     1.000000   \n", "25%        0.000000        NaN             NaN     1.000000     2.000000   \n", "50%        8.000000        NaN             NaN     7.000000     7.000000   \n", "75%       26.000000        NaN             NaN    12.000000    12.000000   \n", "max       55.000000        NaN             NaN    35.000000   102.000000   \n", "\n", "                                  assessment  hdr_level_1  hdr_level_4  \\\n", "count                                    381   274.000000    31.000000   \n", "unique                                     6          NaN          NaN   \n", "top     Header hierarchy appears reasonable.          NaN          NaN   \n", "freq                                     201          NaN          NaN   \n", "mean                                     NaN     4.354015     4.612903   \n", "std                                      NaN     5.270924     2.800922   \n", "min                                      NaN     1.000000     1.000000   \n", "25%                                      NaN     1.000000     2.500000   \n", "50%                                      NaN     1.000000     4.000000   \n", "75%                                      NaN     9.000000     6.000000   \n", "max                                      NaN    40.000000    14.000000   \n", "\n", "        hdr_level_5  header_level_counts  \\\n", "count           1.0                  0.0   \n", "unique          NaN                  NaN   \n", "top             NaN                  NaN   \n", "freq            NaN                  NaN   \n", "mean            2.0                  NaN   \n", "std             NaN                  NaN   \n", "min             2.0                  NaN   \n", "25%             2.0                  NaN   \n", "50%             2.0                  NaN   \n", "75%             2.0                  NaN   \n", "max             2.0                  NaN   \n", "\n", "                                        header_assessment  \n", "count                                                   2  \n", "unique                                                  1  \n", "top     Choose only one of --force-ocr, --skip-text, -...  \n", "freq                                                    2  \n", "mean                                                  NaN  \n", "std                                                   NaN  \n", "min                                                   <PERSON><PERSON>  \n", "25%                                                   NaN  \n", "50%                                                   NaN  \n", "75%                                                   NaN  \n", "max                                                   <PERSON>  "]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["df.describe(include=\"all\")"]}, {"cell_type": "code", "execution_count": 53, "id": "26f182a8", "metadata": {}, "outputs": [{"data": {"text/plain": ["has_toc  assessment                                                                                   \n", "False    Header hierarchy appears reasonable.                                                              91\n", "         No headers found.                                                                                 24\n", "         No top-level (#) header found. Headers do not start at top level.                                  4\n", "         No top-level (#) header found. Headers do not start at top level. Only one header level used.      1\n", "         Only one header level used.                                                                       58\n", "True     Header hierarchy appears reasonable.                                                             110\n", "         Header levels are too deeply nested.                                                              12\n", "         No headers found.                                                                                 11\n", "         No top-level (#) header found. Headers do not start at top level.                                 63\n", "         No top-level (#) header found. Headers do not start at top level. Only one header level used.      4\n", "         Only one header level used.                                                                        3\n", "Name: count, dtype: int64"]}, "execution_count": 53, "metadata": {}, "output_type": "execute_result"}], "source": ["# We can use this as error counts and try to tune the parameters to reduce these counts\n", "df.groupby('has_toc')[\"assessment\"].value_counts().sort_index()"]}, {"cell_type": "code", "execution_count": null, "id": "3e002745", "metadata": {}, "outputs": [{"data": {"text/plain": ["hdr_level_1  hdr_level_2  hdr_level_3  hdr_level_4  hdr_level_5\n", "True         True         False        False        False          103\n", "                          True         False        False           95\n", "             False        False        False        False           61\n", "False        True         True         False        False           48\n", "                                       True         False           19\n", "True         True         True         True         False           10\n", "False        True         False        False        False            5\n", "True         False        True         False        False            3\n", "             True         False        True         False            1\n", "                          True         True         True             1\n", "Name: count, dtype: int64"]}, "execution_count": 54, "metadata": {}, "output_type": "execute_result"}], "source": ["# Which header structure is most frequent among the docs\n", "# Something like this is faulty: True         False        True         False        False\n", "\n", "df[[\"hdr_level_1\", \"hdr_level_2\", \"hdr_level_3\", \"hdr_level_4\", \"hdr_level_5\"]].dropna(how='all').fillna(0).map(bool).value_counts()"]}, {"cell_type": "code", "execution_count": null, "id": "37f6c862", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 5}