{"cells": [{"cell_type": "markdown", "metadata": {"id": "view-in-github", "colab_type": "text"}, "source": ["<a href=\"https://colab.research.google.com/github/Extralit/papers-ocr-benchmarks/blob/main/text_ocr_doc_structure.ipynb\" target=\"_parent\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/></a>"]}, {"cell_type": "markdown", "id": "41f7e972", "metadata": {"id": "41f7e972"}, "source": ["# Explore doc OCR methods for text and header structure extraction\n", "\n", "Self-contained, Google Colab-ready.\n", "\n", "- Upload a PDF and extract markdown\n"]}, {"cell_type": "code", "execution_count": 1, "id": "5ec80635", "metadata": {"id": "5ec80635"}, "outputs": [], "source": ["# 1. Install marker-pdf and dependencies\n", "!uv pip install --quiet marker-pdf[full] docling\n", "!uv pip install -q \"mineru[all]\"\n", "!uv pip install -q \"PyMuPDF>=1.23.0\" \"pandas>=1.5.0\"\n", "!uv pip install -q pymupdf4llm pdf4llm llama_index\n", "!uv pip install -q \"matplotlib>=3.5.0\" \"seaborn>=0.11.0\" \"textdistance>=4.6.0\""]}, {"cell_type": "code", "execution_count": 2, "id": "a878649c", "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "a878649c", "outputId": "623b620c-ccb8-4890-a6ec-84dad4286a46"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\u001b[33mhint: Using 'master' as the name for the initial branch. This default branch name\u001b[m\n", "\u001b[33mhint: is subject to change. To configure the initial branch name to use in all\u001b[m\n", "\u001b[33mhint: of your new repositories, which will suppress this warning, call:\u001b[m\n", "\u001b[33mhint: \u001b[m\n", "\u001b[33mhint: \tgit config --global init.defaultBranch <name>\u001b[m\n", "\u001b[33mhint: \u001b[m\n", "\u001b[33mhint: Names commonly chosen instead of 'master' are 'main', 'trunk' and\u001b[m\n", "\u001b[33mhint: 'development'. The just-created branch can be renamed via this command:\u001b[m\n", "\u001b[33mhint: \u001b[m\n", "\u001b[33mhint: \tgit branch -m <name>\u001b[m\n", "Initialized empty Git repository in /content/.git/\n", "remote: Enumerating objects: 120, done.\u001b[K\n", "remote: Counting objects: 100% (120/120), done.\u001b[K\n", "remote: Compressing objects: 100% (87/87), done.\u001b[K\n", "remote: Total 120 (delta 53), reused 77 (delta 30), pack-reused 0 (from 0)\u001b[K\n", "Receiving objects: 100% (120/120), 15.25 MiB | 13.45 MiB/s, done.\n", "Resolving deltas: 100% (53/53), done.\n", "From https://github.com/Extralit/papers-ocr-benchmarks\n", " * branch            main       -> FETCH_HEAD\n", " * [new branch]      main       -> origin/main\n", "\u001b[2mUsing Python 3.11.13 environment at: /usr\u001b[0m\n", "\u001b[2K\u001b[2mResolved \u001b[1m1 package\u001b[0m \u001b[2min 2ms\u001b[0m\u001b[0m\n", "\u001b[2K   \u001b[36m\u001b[1mBuilding\u001b[0m\u001b[39m papers-ocr-benchmarks\u001b[2m @ file:///content\u001b[0m\n", "\u001b[2K\u001b[1A   \u001b[36m\u001b[1mBuilding\u001b[0m\u001b[39m papers-ocr-benchmarks\u001b[2m @ file:///content\u001b[0m\n", "\u001b[2K\u001b[1A   \u001b[36m\u001b[1mBuilding\u001b[0m\u001b[39m papers-ocr-benchmarks\u001b[2m @ file:///content\u001b[0m\n", "\u001b[2K\u001b[1A   \u001b[36m\u001b[1mBuilding\u001b[0m\u001b[39m papers-ocr-benchmarks\u001b[2m @ file:///content\u001b[0m\n", "\u001b[2K\u001b[1A      \u001b[32m\u001b[1mBuilt\u001b[0m\u001b[39m papers-ocr-benchmarks\u001b[2m @ file:///content\u001b[0m\n", "\u001b[2K\u001b[2mPrepared \u001b[1m1 package\u001b[0m \u001b[2min 736ms\u001b[0m\u001b[0m\n", "\u001b[2K\u001b[2mInstalled \u001b[1m1 package\u001b[0m \u001b[2min 1ms\u001b[0m\u001b[0m\n", " \u001b[32m+\u001b[39m \u001b[1mpapers-ocr-benchmarks\u001b[0m\u001b[2m==0.1.0 (from file:///content)\u001b[0m\n"]}], "source": ["!rm -rf /content/papers-ocr-benchmarks .git/ pdfs/\n", "!git init .\n", "!git remote add origin https://github.com/Extralit/papers-ocr-benchmarks.git\n", "!git pull origin main\n", "\n", "# Install the python package in `scripts/`\n", "!uv pip install -e ."]}, {"cell_type": "code", "source": ["from IPython.display import HTML, display, JSON, Markdown\n", "from pprint import pprint"], "metadata": {"id": "S9g8hddio-km"}, "id": "S9g8hddio-km", "execution_count": 3, "outputs": []}, {"cell_type": "code", "source": ["# Here's how you import code modules, but first you'll need to wrap the scripts in the file into the __main__() function otherwise it'll run the entire file, since we just want to import a module from it\n", "\n", "# from scripts.ocr_benchmark_gpu_optimized import calculate_text_metrics"], "metadata": {"id": "MkRloxk8PU_M"}, "id": "MkRloxk8PU_M", "execution_count": 4, "outputs": []}, {"cell_type": "code", "source": ["# from google.colab import files\n", "# uploaded = files.upload()\n", "# file_path = next(iter(uploaded))\n", "\n", "file_path = \"/content/pdfs/Allossogbe_et_al_2017_Mal_J.pdf\"\n", "# file_path = \"/content/pdfs/Mbogo_et_al_1996_Med_Vet_Ento.pdf\""], "metadata": {"id": "UsqxR8ezlgLr"}, "id": "UsqxR8ezlgLr", "execution_count": 4, "outputs": []}, {"cell_type": "markdown", "source": ["## MinerU"], "metadata": {"id": "D9q3Jl4Kl-YU"}, "id": "D9q3Jl4Kl-YU"}, {"cell_type": "code", "source": ["%%time\n", "!mineru -p /content/Allossogbe_et_al_2017_Mal_J.pdf -o /content/mineru_output/"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "WuFa8Vw9l_mJ", "outputId": "c182d611-d611-4720-87a9-560e959101ee"}, "id": "WuFa8Vw9l_mJ", "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\u001b[32m2025-07-08 20:03:55.332\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36mmineru.backend.pipeline.pipeline_analyze\u001b[0m:\u001b[36mdoc_analyze\u001b[0m:\u001b[36m124\u001b[0m - \u001b[1mBatch 1/1: 11 pages/11 pages\u001b[0m\n", "\u001b[32m2025-07-08 20:03:55.334\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36mmineru.backend.pipeline.pipeline_analyze\u001b[0m:\u001b[36mbatch_image_analyze\u001b[0m:\u001b[36m187\u001b[0m - \u001b[1mgpu_memory: 15 GB, batch_ratio: 8\u001b[0m\n", "\u001b[32m2025-07-08 20:03:55.334\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36mmineru.backend.pipeline.model_init\u001b[0m:\u001b[36m__init__\u001b[0m:\u001b[36m137\u001b[0m - \u001b[1mDocAnalysis init, this may take some times......\u001b[0m\n", "\u001b[32m2025-07-08 20:04:09.188\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36mmineru.backend.pipeline.model_init\u001b[0m:\u001b[36m__init__\u001b[0m:\u001b[36m182\u001b[0m - \u001b[1mDocAnalysis init done!\u001b[0m\n", "\u001b[32m2025-07-08 20:04:09.189\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36mmineru.backend.pipeline.pipeline_analyze\u001b[0m:\u001b[36mcustom_model_init\u001b[0m:\u001b[36m64\u001b[0m - \u001b[1mmodel init cost: 13.854581832885742\u001b[0m\n", "Layout Predict: 100% 11/11 [00:02<00:00,  4.03it/s]\n", "MFD Predict: 100% 11/11 [00:04<00:00,  2.23it/s]\n", "MFR Predict: 100% 140/140 [00:06<00:00, 21.88it/s]\n", "OCR-det ch: 100% 65/65 [00:09<00:00,  7.19it/s]\n", "Table Predict: 100% 4/4 [00:07<00:00,  1.76s/it]\n", "Processing pages: 100% 11/11 [00:09<00:00,  1.15it/s]\n", "OCR-rec Predict: 100% 5/5 [00:00<00:00, 44.46it/s]\n", "\u001b[32m2025-07-08 20:04:56.216\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36mmineru.cli.common\u001b[0m:\u001b[36m_process_output\u001b[0m:\u001b[36m156\u001b[0m - \u001b[1mlocal output dir is /content/mineru_output/Allossogbe_et_al_2017_Mal_J/auto\u001b[0m\n", "CPU times: user 1min 20s, sys: 3.46 s, total: 1min 23s\n", "Wall time: 1min 45s\n"]}]}, {"cell_type": "markdown", "source": ["## <PERSON><PERSON>"], "metadata": {"id": "ohTKa7CgdLJJ"}, "id": "ohTKa7CgdLJJ"}, {"cell_type": "code", "source": ["%%time\n", "from docling.document_converter import DocumentConverter\n", "converter = DocumentConverter()\n", "result = converter.convert(file_path)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 537}, "id": "ORt9zc6IdMS9", "outputId": "3f772aed-1a09-4ea3-bd67-66a2324806e6"}, "id": "ORt9zc6IdMS9", "execution_count": null, "outputs": [{"output_type": "error", "ename": "HfHubHTTPError", "evalue": "401 Client Error: Unauthorized for url: https://huggingface.co/api/models/ds4sd/docling-models/revision/v2.2.0 (Request ID: Root=1-686d9ca9-75d18db76d819b064743c2d2;f93df949-b762-4052-bc1e-259e4e8a5168)\n\nInvalid credentials in Authorization header", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mHTTPError\u001b[0m                                 Traceback (most recent call last)", "\u001b[0;32m/usr/local/lib/python3.11/dist-packages/huggingface_hub/utils/_http.py\u001b[0m in \u001b[0;36mhf_raise_for_status\u001b[0;34m(response, endpoint_name)\u001b[0m\n\u001b[1;32m    408\u001b[0m     \u001b[0;32mtry\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 409\u001b[0;31m         \u001b[0mresponse\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mraise_for_status\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    410\u001b[0m     \u001b[0;32mexcept\u001b[0m \u001b[0mHTTPError\u001b[0m \u001b[0;32mas\u001b[0m \u001b[0me\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/lib/python3.11/dist-packages/requests/models.py\u001b[0m in \u001b[0;36mraise_for_status\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m   1023\u001b[0m         \u001b[0;32mif\u001b[0m \u001b[0mhttp_error_msg\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m-> 1024\u001b[0;31m             \u001b[0;32mraise\u001b[0m \u001b[0mHTTPError\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mhttp_error_msg\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mresponse\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m   1025\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;31mHTTPError\u001b[0m: 401 Client Error: Unauthorized for url: https://huggingface.co/api/models/ds4sd/docling-models/revision/v2.2.0", "\nThe above exception was the direct cause of the following exception:\n", "\u001b[0;31mHfHubHTTPError\u001b[0m                            Traceback (most recent call last)", "\u001b[0;32m<timed exec>\u001b[0m in \u001b[0;36m<module>\u001b[0;34m\u001b[0m\n", "\u001b[0;32m/usr/local/lib/python3.11/dist-packages/pydantic/_internal/_validate_call.py\u001b[0m in \u001b[0;36mwrapper_function\u001b[0;34m(*args, **kwargs)\u001b[0m\n\u001b[1;32m     37\u001b[0m         \u001b[0;34m@\u001b[0m\u001b[0mfunctools\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mwraps\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mwrapped\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     38\u001b[0m         \u001b[0;32mdef\u001b[0m \u001b[0mwrapper_function\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m*\u001b[0m\u001b[0margs\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m---> 39\u001b[0;31m             \u001b[0;32mreturn\u001b[0m \u001b[0mwrapper\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m*\u001b[0m\u001b[0margs\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m     40\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     41\u001b[0m     \u001b[0;31m# We need to manually update this because `partial` object has no `__name__` and `__qualname__`.\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/lib/python3.11/dist-packages/pydantic/_internal/_validate_call.py\u001b[0m in \u001b[0;36m__call__\u001b[0;34m(self, *args, **kwargs)\u001b[0m\n\u001b[1;32m    134\u001b[0m             \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_create_validators\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    135\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 136\u001b[0;31m         \u001b[0mres\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m__pydantic_validator__\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mvalidate_python\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mpydantic_core\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mArgsKwargs\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0margs\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mkwargs\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    137\u001b[0m         \u001b[0;32mif\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m__return_pydantic_validator__\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    138\u001b[0m             \u001b[0;32mreturn\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m__return_pydantic_validator__\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mres\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/lib/python3.11/dist-packages/docling/document_converter.py\u001b[0m in \u001b[0;36mconvert\u001b[0;34m(self, source, headers, raises_on_error, max_num_pages, max_file_size, page_range)\u001b[0m\n\u001b[1;32m    233\u001b[0m             \u001b[0mpage_range\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mpage_range\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    234\u001b[0m         )\n\u001b[0;32m--> 235\u001b[0;31m         \u001b[0;32mreturn\u001b[0m \u001b[0mnext\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mall_res\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    236\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    237\u001b[0m     \u001b[0;34m@\u001b[0m\u001b[0mvalidate_call\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mconfig\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mConfigDict\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mstrict\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0;32mTrue\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/lib/python3.11/dist-packages/docling/document_converter.py\u001b[0m in \u001b[0;36mconvert_all\u001b[0;34m(self, source, headers, raises_on_error, max_num_pages, max_file_size, page_range)\u001b[0m\n\u001b[1;32m    256\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    257\u001b[0m         \u001b[0mhad_result\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0;32mFalse\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 258\u001b[0;31m         \u001b[0;32mfor\u001b[0m \u001b[0mconv_res\u001b[0m \u001b[0;32min\u001b[0m \u001b[0mconv_res_iter\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    259\u001b[0m             \u001b[0mhad_result\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0;32mTrue\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    260\u001b[0m             if raises_on_error and conv_res.status not in {\n", "\u001b[0;32m/usr/local/lib/python3.11/dist-packages/docling/document_converter.py\u001b[0m in \u001b[0;36m_convert\u001b[0;34m(self, conv_input, raises_on_error)\u001b[0m\n\u001b[1;32m    291\u001b[0m             \u001b[0;31m# Note: PDF backends are not thread-safe, thread pool usage was disabled.\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    292\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 293\u001b[0;31m             for item in map(\n\u001b[0m\u001b[1;32m    294\u001b[0m                 \u001b[0mpartial\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_process_document\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mraises_on_error\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mraises_on_error\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    295\u001b[0m                 \u001b[0minput_batch\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/lib/python3.11/dist-packages/docling/document_converter.py\u001b[0m in \u001b[0;36m_process_document\u001b[0;34m(self, in_doc, raises_on_error)\u001b[0m\n\u001b[1;32m    337\u001b[0m         )\n\u001b[1;32m    338\u001b[0m         \u001b[0;32mif\u001b[0m \u001b[0mvalid\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 339\u001b[0;31m             \u001b[0mconv_res\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_execute_pipeline\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0min_doc\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mraises_on_error\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mraises_on_error\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    340\u001b[0m         \u001b[0;32melse\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    341\u001b[0m             \u001b[0merror_message\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0;34mf\"File format not allowed: {in_doc.file}\"\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/lib/python3.11/dist-packages/docling/document_converter.py\u001b[0m in \u001b[0;36m_execute_pipeline\u001b[0;34m(self, in_doc, raises_on_error)\u001b[0m\n\u001b[1;32m    358\u001b[0m     ) -> ConversionResult:\n\u001b[1;32m    359\u001b[0m         \u001b[0;32mif\u001b[0m \u001b[0min_doc\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mvalid\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 360\u001b[0;31m             \u001b[0mpipeline\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_get_pipeline\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0min_doc\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mformat\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    361\u001b[0m             \u001b[0;32mif\u001b[0m \u001b[0mpipeline\u001b[0m \u001b[0;32mis\u001b[0m \u001b[0;32mnot\u001b[0m \u001b[0;32mNone\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    362\u001b[0m                 \u001b[0mconv_res\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mpipeline\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mexecute\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0min_doc\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mraises_on_error\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mraises_on_error\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/lib/python3.11/dist-packages/docling/document_converter.py\u001b[0m in \u001b[0;36m_get_pipeline\u001b[0;34m(self, doc_format)\u001b[0m\n\u001b[1;32m    320\u001b[0m                 \u001b[0;34mf\"Initializing pipeline for {pipeline_class.__name__} with options hash {options_hash}\"\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    321\u001b[0m             )\n\u001b[0;32m--> 322\u001b[0;31m             self.initialized_pipelines[cache_key] = pipeline_class(\n\u001b[0m\u001b[1;32m    323\u001b[0m                 \u001b[0mpipeline_options\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mpipeline_options\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    324\u001b[0m             )\n", "\u001b[0;32m/usr/local/lib/python3.11/dist-packages/docling/pipeline/standard_pdf_pipeline.py\u001b[0m in \u001b[0;36m__init__\u001b[0;34m(self, pipeline_options)\u001b[0m\n\u001b[1;32m     78\u001b[0m             \u001b[0mocr_model\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     79\u001b[0m             \u001b[0;31m# Layout model\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m---> 80\u001b[0;31m             LayoutModel(\n\u001b[0m\u001b[1;32m     81\u001b[0m                 \u001b[0martifacts_path\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0martifacts_path\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     82\u001b[0m                 \u001b[0maccelerator_options\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mpipeline_options\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0maccelerator_options\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/lib/python3.11/dist-packages/docling/models/layout_model.py\u001b[0m in \u001b[0;36m__init__\u001b[0;34m(self, artifacts_path, accelerator_options, options)\u001b[0m\n\u001b[1;32m     62\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     63\u001b[0m         \u001b[0;32mif\u001b[0m \u001b[0martifacts_path\u001b[0m \u001b[0;32mis\u001b[0m \u001b[0;32mNone\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m---> 64\u001b[0;31m             \u001b[0martifacts_path\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mdownload_models\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m \u001b[0;34m/\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_model_path\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m     65\u001b[0m         \u001b[0;32melse\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     66\u001b[0m             \u001b[0;31m# will become the default in the future\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/lib/python3.11/dist-packages/docling/models/layout_model.py\u001b[0m in \u001b[0;36mdownload_models\u001b[0;34m(local_dir, force, progress)\u001b[0m\n\u001b[1;32m     92\u001b[0m         \u001b[0mprogress\u001b[0m\u001b[0;34m:\u001b[0m \u001b[0mbool\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0;32mFalse\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     93\u001b[0m     ) -> Path:\n\u001b[0;32m---> 94\u001b[0;31m         return download_hf_model(\n\u001b[0m\u001b[1;32m     95\u001b[0m             \u001b[0mrepo_id\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0;34m\"ds4sd/docling-models\"\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     96\u001b[0m             \u001b[0mrevision\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0;34m\"v2.2.0\"\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/lib/python3.11/dist-packages/docling/models/utils/hf_model_download.py\u001b[0m in \u001b[0;36mdownload_hf_model\u001b[0;34m(repo_id, local_dir, force, progress, revision)\u001b[0m\n\u001b[1;32m     18\u001b[0m     \u001b[0;32mif\u001b[0m \u001b[0;32mnot\u001b[0m \u001b[0mprogress\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     19\u001b[0m         \u001b[0mdisable_progress_bars\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m---> 20\u001b[0;31m     download_path = snapshot_download(\n\u001b[0m\u001b[1;32m     21\u001b[0m         \u001b[0mrepo_id\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mrepo_id\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     22\u001b[0m         \u001b[0mforce_download\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mforce\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/lib/python3.11/dist-packages/huggingface_hub/utils/_validators.py\u001b[0m in \u001b[0;36m_inner_fn\u001b[0;34m(*args, **kwargs)\u001b[0m\n\u001b[1;32m    112\u001b[0m             \u001b[0mkwargs\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0msmoothly_deprecate_use_auth_token\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mfn_name\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mfn\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m__name__\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mhas_token\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mhas_token\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mkwargs\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    113\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 114\u001b[0;31m         \u001b[0;32mreturn\u001b[0m \u001b[0mfn\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m*\u001b[0m\u001b[0margs\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    115\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    116\u001b[0m     \u001b[0;32mreturn\u001b[0m \u001b[0m_inner_fn\u001b[0m  \u001b[0;31m# type: ignore\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/lib/python3.11/dist-packages/huggingface_hub/_snapshot_download.py\u001b[0m in \u001b[0;36msnapshot_download\u001b[0;34m(repo_id, repo_type, revision, cache_dir, local_dir, library_name, library_version, user_agent, proxies, etag_timeout, force_download, token, local_files_only, allow_patterns, ignore_patterns, max_workers, tqdm_class, headers, endpoint, local_dir_use_symlinks, resume_download)\u001b[0m\n\u001b[1;32m    243\u001b[0m         ):\n\u001b[1;32m    244\u001b[0m             \u001b[0;31m# Repo not found, gated, or specific authentication error => let's raise the actual error\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 245\u001b[0;31m             \u001b[0;32mraise\u001b[0m \u001b[0mapi_call_error\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    246\u001b[0m         \u001b[0;32melse\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    247\u001b[0m             \u001b[0;31m# Otherwise: most likely a connection issue or Hub downtime => let's warn the user\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/lib/python3.11/dist-packages/huggingface_hub/_snapshot_download.py\u001b[0m in \u001b[0;36msnapshot_download\u001b[0;34m(repo_id, repo_type, revision, cache_dir, local_dir, library_name, library_version, user_agent, proxies, etag_timeout, force_download, token, local_files_only, allow_patterns, ignore_patterns, max_workers, tqdm_class, headers, endpoint, local_dir_use_symlinks, resume_download)\u001b[0m\n\u001b[1;32m    163\u001b[0m         \u001b[0;32mtry\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    164\u001b[0m             \u001b[0;31m# if we have internet connection we want to list files to download\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 165\u001b[0;31m             \u001b[0mrepo_info\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mapi\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mrepo_info\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mrepo_id\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mrepo_id\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mrepo_type\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mrepo_type\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mrevision\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mrevision\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    166\u001b[0m         \u001b[0;32mexcept\u001b[0m \u001b[0;34m(\u001b[0m\u001b[0mrequests\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mexceptions\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mSSLError\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mrequests\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mexceptions\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mProxyError\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    167\u001b[0m             \u001b[0;31m# Actually raise for those subclasses of ConnectionError\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/lib/python3.11/dist-packages/huggingface_hub/utils/_validators.py\u001b[0m in \u001b[0;36m_inner_fn\u001b[0;34m(*args, **kwargs)\u001b[0m\n\u001b[1;32m    112\u001b[0m             \u001b[0mkwargs\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0msmoothly_deprecate_use_auth_token\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mfn_name\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mfn\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m__name__\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mhas_token\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mhas_token\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mkwargs\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    113\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 114\u001b[0;31m         \u001b[0;32mreturn\u001b[0m \u001b[0mfn\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m*\u001b[0m\u001b[0margs\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    115\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    116\u001b[0m     \u001b[0;32mreturn\u001b[0m \u001b[0m_inner_fn\u001b[0m  \u001b[0;31m# type: ignore\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/lib/python3.11/dist-packages/huggingface_hub/hf_api.py\u001b[0m in \u001b[0;36mrepo_info\u001b[0;34m(self, repo_id, revision, repo_type, timeout, files_metadata, expand, token)\u001b[0m\n\u001b[1;32m   2842\u001b[0m         \u001b[0;32melse\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   2843\u001b[0m             \u001b[0;32mraise\u001b[0m \u001b[0mValueError\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m\"Unsupported repo type.\"\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m-> 2844\u001b[0;31m         return method(\n\u001b[0m\u001b[1;32m   2845\u001b[0m             \u001b[0mrepo_id\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   2846\u001b[0m             \u001b[0mrevision\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mrevision\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/lib/python3.11/dist-packages/huggingface_hub/utils/_validators.py\u001b[0m in \u001b[0;36m_inner_fn\u001b[0;34m(*args, **kwargs)\u001b[0m\n\u001b[1;32m    112\u001b[0m             \u001b[0mkwargs\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0msmoothly_deprecate_use_auth_token\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mfn_name\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mfn\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m__name__\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mhas_token\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mhas_token\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mkwargs\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    113\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 114\u001b[0;31m         \u001b[0;32mreturn\u001b[0m \u001b[0mfn\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m*\u001b[0m\u001b[0margs\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    115\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    116\u001b[0m     \u001b[0;32mreturn\u001b[0m \u001b[0m_inner_fn\u001b[0m  \u001b[0;31m# type: ignore\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/lib/python3.11/dist-packages/huggingface_hub/hf_api.py\u001b[0m in \u001b[0;36mmodel_info\u001b[0;34m(self, repo_id, revision, timeout, securityStatus, files_metadata, expand, token)\u001b[0m\n\u001b[1;32m   2627\u001b[0m             \u001b[0mparams\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;34m\"expand\"\u001b[0m\u001b[0;34m]\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mexpand\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   2628\u001b[0m         \u001b[0mr\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mget_session\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mget\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mpath\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mheaders\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mheaders\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mtimeout\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mtimeout\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mparams\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mparams\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m-> 2629\u001b[0;31m         \u001b[0mhf_raise_for_status\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mr\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m   2630\u001b[0m         \u001b[0mdata\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mr\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mjson\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   2631\u001b[0m         \u001b[0;32mreturn\u001b[0m \u001b[0mModelInfo\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m**\u001b[0m\u001b[0mdata\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/lib/python3.11/dist-packages/huggingface_hub/utils/_http.py\u001b[0m in \u001b[0;36mhf_raise_for_status\u001b[0;34m(response, endpoint_name)\u001b[0m\n\u001b[1;32m    480\u001b[0m         \u001b[0;31m# Convert `HTTPError` into a `HfHubHTTPError` to display request information\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    481\u001b[0m         \u001b[0;31m# as well (request id and/or server error message)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 482\u001b[0;31m         \u001b[0;32mraise\u001b[0m \u001b[0m_format\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mHfHubHTTPError\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mstr\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0me\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mresponse\u001b[0m\u001b[0;34m)\u001b[0m \u001b[0;32mfrom\u001b[0m \u001b[0me\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    483\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    484\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;31mHfHubHTTPError\u001b[0m: 401 Client Error: Unauthorized for url: https://huggingface.co/api/models/ds4sd/docling-models/revision/v2.2.0 (Request ID: Root=1-686d9ca9-75d18db76d819b064743c2d2;f93df949-b762-4052-bc1e-259e4e8a5168)\n\nInvalid credentials in Authorization header"]}]}, {"cell_type": "code", "source": ["Markdown(result.document.export_to_markdown())"], "metadata": {"id": "rJlO9yX0uFZ8"}, "id": "rJlO9yX0uFZ8", "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["## PyMuPDF4LLM\n", "\n", "Read https://pymupdf.readthedocs.io/en/latest/pymupdf4llm/api.html#pymupdf4llm-api\n", "\n"], "metadata": {"id": "CDgqMdU7cTSK"}, "id": "CDgqMdU7cTSK"}, {"cell_type": "code", "source": ["import pymupdf\n", "import pymupdf4llm\n", "from pymupdf4llm import IdentifyHeaders\n", "\n", "doc = pymupdf.open(file_path)\n", "\n", "doc.get_toc()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "Y6YDvVaScXKN", "outputId": "c927a19e-52b7-4e62-b150-702b15509e7e"}, "id": "Y6YDvVaScXKN", "execution_count": 5, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["[[1,\n", "  'WHO cone bio-assays of\\xa0classical and\\xa0new-generation long-lasting insecticidal nets call for\\xa0innovative insecticides targeting the knock-down resistance mechanism in\\xa0Benin',\n", "  1],\n", " [2, 'Abstract ', 1],\n", " [3, 'Background: ', 1],\n", " [3, 'Methods: ', 1],\n", " [3, 'Results: ', 1],\n", " [3, 'Conclusion: ', 1],\n", " [2, 'Background', 2],\n", " [2, 'Methods', 2],\n", " [3, 'Study design', 2],\n", " [3, 'Study sites', 3],\n", " [4, 'Malan<PERSON>', 3],\n", " [4, '<PERSON><PERSON><PERSON>', 3],\n", " [4, '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', 3],\n", " [4, '<PERSON><PERSON><PERSON>', 3],\n", " [4, 'Porto-Novo', 3],\n", " [4, '<PERSON><PERSON><PERSON>', 3],\n", " [4, '<PERSON><PERSON><PERSON><PERSON>', 3],\n", " [3, '<PERSON><PERSON><PERSON> collection', 3],\n", " [3, 'Highlighting resistance mechanisms', 3],\n", " [3, 'Mosquito nets', 3],\n", " [3, 'Cone test', 4],\n", " [3, 'Data analysis', 4],\n", " [2, 'Results', 4],\n", " [3, 'Characteristics of\\xa0the studied mosquito populations', 4],\n", " [3, 'Knock-down (KD) and\\xa0mortality of\\xa0laboratory strains', 4],\n", " [3, 'Inhibition of\\xa0mortality conferred by\\xa0the kdr resistance gene', 4],\n", " [3,\n", "  'Knock-down (Kd) effect and\\xa0mortality induced by\\xa0mosquito nets on\\xa0local An. gambiae s.l.',\n", "  4],\n", " [3,\n", "  'Knock-down (KD) and\\xa0mortality induced by\\xa0the LLINs on\\xa0mono-resistance mosquito strains',\n", "  6],\n", " [3,\n", "  'Inhibition of\\xa0mortality in\\xa0mono-resistant An. gambiae s.l. strains',\n", "  6],\n", " [3,\n", "  'Knock-down (KD) and\\xa0mortality induced by\\xa0the LLINs on\\xa0multi-resistant mosquito strains (carrying kdr and\\xa0biochemical resistance mutations)',\n", "  6],\n", " [3, 'Inhibition of\\xa0mortality in\\xa0multi-resistant strains', 7],\n", " [3, 'Knock-down time of\\xa0LLINs on\\xa0local An. gambiae s.l. strains', 7],\n", " [2, 'Discussion', 7],\n", " [2, 'Conclusion', 9],\n", " [2, 'Authors’ contributions', 9],\n", " [2, 'References', 10]]"]}, "metadata": {}, "execution_count": 5}]}, {"cell_type": "code", "source": ["# We can extract the paper title here\n", "doc.metadata"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "Vcw6tftvCpRn", "outputId": "6333171b-5e34-40b7-91ae-a91ea93ee0c0"}, "id": "Vcw6tftvCpRn", "execution_count": 7, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["{'format': 'PDF 1.7',\n", " 'title': 'WHO cone bio-assays of classical and new-generation long-lasting insecticidal nets call for innovative insecticides targeting the knock-down resistance mechanism in Benin',\n", " 'author': '<PERSON>',\n", " 'subject': 'Malaria Journal, doi:10.1186/s12936-017-1727-x',\n", " 'keywords': 'LLINs,Bio-efficacy,Piperonyl butoxide,Resistant mosquitoes',\n", " 'creator': 'ocrmypdf 16.1.2 / Tesseract OCRhOCR 5.3.4',\n", " 'producer': 'pikepdf 8.13.0',\n", " 'creationDate': \"D:20170214204129+05'30'\",\n", " 'modDate': \"D:20240423063541+00'00'\",\n", " 'trapped': '',\n", " 'encryption': None}"]}, "metadata": {}, "execution_count": 7}]}, {"cell_type": "code", "source": ["%%time\n", "\n", "# header parse method 1: Custom logic\n", "def custom_header_logic(span, page=None):\n", "    # Example: treat bold, all-caps lines as headers\n", "    text = span[\"text\"].strip()\n", "    is_bold = \"Bold\" in span.get(\"font\", \"\")\n", "    is_all_caps = text.isupper()\n", "    font_size = span[\"size\"]\n", "\n", "    if is_bold and is_all_caps and len(text) > 3:\n", "        return \"# \"\n", "    elif is_bold and font_size > 12:\n", "        return \"## \"\n", "    else:\n", "        return \"\"\n", "\n", "# header parse method 2\n", "my_headers = IdentifyHeaders(doc, max_levels=4)\n", "\n", "# header parse method 3\n", "toc = doc.get_toc()\n", "def toc_header_logic(span, page=None):\n", "    toc_items = [t for t in toc if t[-1] == page.number + 1]\n", "    for lvl, title, _ in toc_items:\n", "        if span[\"text\"].strip().startswith(title): # Improve string matching here\n", "            return \"#\" * lvl + \" \"\n", "    return \"\"\n", "\n", "md_text = pymupdf4llm.to_markdown(\n", "    doc,\n", "\n", "    hdr_info=toc_header_logic,\n", "    # hdr_info=my_headers,\n", "    # page_chunks=\"toc_items\",\n", "    # table_strategy=None\n", ")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "_58hKTuC1FR6", "outputId": "43cdbc07-7529-4772-8292-a0d28d21909f"}, "id": "_58hKTuC1FR6", "execution_count": 8, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["CPU times: user 9.54 s, sys: 68.1 ms, total: 9.61 s\n", "Wall time: 9.82 s\n"]}]}, {"cell_type": "code", "source": ["print(md_text)"], "metadata": {"id": "UdZH72URca_w"}, "id": "UdZH72URca_w", "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["#### pymupdf4llm.LlamaMarkdownReader\n", "\n", "This is nice since it parses into LlamaDocument objects for us, which can directly feed into the vector db. But, it doesn't work so well because we need the documents to chunk by section headers, not by page number."], "metadata": {"id": "Pko9R6EP41b9"}, "id": "Pko9R6EP41b9"}, {"cell_type": "code", "source": ["%%time\n", "llama_reader = pymupdf4llm.LlamaMarkdownReader()\n", "llama_docs = llama_reader.load_data(file_path, )"], "metadata": {"id": "MnVH0WlXrq_f", "outputId": "8bb895d7-3d85-4962-ce0c-d941e15d23f9", "colab": {"base_uri": "https://localhost:8080/", "height": 223}}, "id": "MnVH0WlXrq_f", "execution_count": 15, "outputs": [{"output_type": "error", "ename": "TypeError", "evalue": "PDFMarkdownReader.__init__() got an unexpected keyword argument 'hdr_info'", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mTypeError\u001b[0m                                 Traceback (most recent call last)", "\u001b[0;32m<timed exec>\u001b[0m in \u001b[0;36m<module>\u001b[0;34m\u001b[0m\n", "\u001b[0;32m/usr/local/lib/python3.11/dist-packages/pymupdf4llm/__init__.py\u001b[0m in \u001b[0;36mLlamaMarkdownReader\u001b[0;34m(*args, **kwargs)\u001b[0m\n\u001b[1;32m     14\u001b[0m     \u001b[0;32mfrom\u001b[0m \u001b[0;34m.\u001b[0m\u001b[0mllama\u001b[0m \u001b[0;32mimport\u001b[0m \u001b[0mpdf_markdown_reader\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     15\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m---> 16\u001b[0;31m     \u001b[0;32mreturn\u001b[0m \u001b[0mpdf_markdown_reader\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mPDFMarkdownReader\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m*\u001b[0m\u001b[0margs\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m", "\u001b[0;31mTypeError\u001b[0m: PDFMarkdownReader.__init__() got an unexpected keyword argument 'hdr_info'"]}]}, {"cell_type": "code", "source": ["len(llama_docs)"], "metadata": {"id": "Xsg_MOszYgZq", "outputId": "d5df14a6-4c22-4a2b-ad1b-085fb4f6dd46", "colab": {"base_uri": "https://localhost:8080/"}}, "id": "Xsg_MOszYgZq", "execution_count": 13, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["11"]}, "metadata": {}, "execution_count": 13}]}, {"cell_type": "code", "source": ["[(display(p.metadata), print(p.text)) for p in llama_docs]"], "metadata": {"id": "nuAENj6irwA9", "outputId": "40f87273-96aa-4222-b3e3-eaa20538d046", "colab": {"base_uri": "https://localhost:8080/", "height": 1000}}, "id": "nuAENj6irwA9", "execution_count": 11, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["{'format': 'PDF 1.7',\n", " 'title': 'WHO cone bio-assays of classical and new-generation long-lasting insecticidal nets call for innovative insecticides targeting the knock-down resistance mechanism in Benin',\n", " 'author': '<PERSON>',\n", " 'subject': 'Malaria Journal, doi:10.1186/s12936-017-1727-x',\n", " 'keywords': 'LLINs,Bio-efficacy,Piperonyl butoxide,Resistant mosquitoes',\n", " 'creator': 'ocrmypdf 16.1.2 / Tesseract OCRhOCR 5.3.4',\n", " 'producer': 'pikepdf 8.13.0',\n", " 'creationDate': \"D:20170214204129+05'30'\",\n", " 'modDate': \"D:20240423063541+00'00'\",\n", " 'trapped': '',\n", " 'encryption': None,\n", " 'page': 1,\n", " 'total_pages': 11,\n", " 'file_path': '/content/pdfs/Allossogbe_et_al_2017_Mal_J.pdf'}"]}, "metadata": {}}, {"output_type": "stream", "name": "stdout", "text": ["<PERSON><PERSON><PERSON><PERSON> et al. Malar J (2017) 16:77\n", "DOI 10.1186/s12936-017-1727-x\n", "\n", "### **RESEARCH**\n", "\n", "\n", "## Malaria Journal\n", "\n", "### **Open Access**\n", "\n", "\n", "# WHO cone bio‑assays of classical and new‑generation long‑lasting insecticidal nets call for innovative insecticides targeting the knock‑down resistance mechanism in Benin\n", "\n", "<PERSON> [1,2*], <PERSON><PERSON> [1,2], <PERSON><PERSON><PERSON> [1,2], <PERSON> [1], <PERSON><PERSON><PERSON> [1,2],\n", "<PERSON><PERSON><PERSON> [1,2], <PERSON> [3], <PERSON> [1,2] and <PERSON> [1,2]\n", "\n", "\n", "**Abstract**\n", "\n", "**Background:** To increase the effectiveness of insecticide-treated nets (ITN) in areas of high resistance, new longlasting insecticidal nets (LLINs) called new-generation nets have been developed. These nets are treated with the\n", "piperonyl butoxide (PBO) synergist which inhibit the action of detoxification enzymes. The effectiveness of the\n", "new-generation nets has been proven in some studies, but their specific effect on mosquitoes carrying detoxification enzymes and those carrying both detoxification enzymes and the knock-down resistance gene in Benin is not\n", "well known. Thus, the objective of this study is to evaluate the efficacy of LLINs treated with PBO on multi-resistant\n", "Anopheles gambiae s.l.\n", "\n", "**Methods:** The study occurred in seven cities in Benin, Abomey, Cotonou, Porto-Novo, Zangnanado, Parakou, Malanville and Tanguiéta, and included ten locations selected on a north–south transect. Mosquito larvae were collected\n", "from these sites, and adult females from these larvae were exposed to single-pyrethroid-treated nets (LifeNet, PermaNet 2.0, Olyset Net) and bi-treated nets (PermaNet 3.0 and Olyset Plus) based on their level of resistance and using\n", "WHO cone tests following WHO guidelines.\n", "\n", "**Results:** The different LLINs showed 100% mortality of the susceptible laboratory strain Kisumu and the resistant strain Ace-1R Kisumu. However, with the resistant laboratory strain kdr-Kisumu, mortality was low (16–32%) for\n", "all LLINs except PermaNet 3.0 (82.9%). The mortality of local strains carrying only the kdr mechanism varied from 0\n", "to 47% for the single-pyrethroid-treated LLINs and 9 to 86% for bi-treated LLINs. With local strains carrying several\n", "mechanisms of resistance (kdr + detoxification enzymes), the observed mortality with different LLINs was also low\n", "except for PermaNet 3.0, which induced significantly higher mortality, usually greater than 75% (p < 0.001), with multiresistant strains. The inhibition of the mortalities induced by the LLINs (11–96%) on multi-resistant field populations\n", "was similar to the inhibition observed with the laboratory strain carrying only the knock-down resistance mechanism\n", "(kdr-<PERSON><PERSON><PERSON>) (p > 0.05).\n", "\n", "**Conclusion:** This study showed that the new-generation LLINs treated with pyrethroids and PBO showed better\n", "efficacy compared to conventional LLINs. Although the addition of PBO significantly increased the mortality of mosquitoes, the significant role of the kdr resistance gene in the low efficacy of LLINs calls for LLIN technology innovation\n", "that specifically targets this mechanism.\n", "\n", "**Keywords:** LLINs, Bio-efficacy, Piperonyl butoxide, Resistant mosquitoes\n", "\n", "\n", "*Correspondence: <EMAIL>\n", "1 Centre de Recherche Entomologique de Cotonou (CREC), Cotonou,\n", "Benin\n", "\n", "Full list of author information is available at the end of the article\n", "\n", "\n", "© The Author(s) 2017. This article is distributed under the terms of the Creative Commons Attribution 4.0 International License\n", "[(http://creativecommons.org/licenses/by/4.0/), which permits unrestricted use, distribution, and reproduction in any medium,](http://creativecommons.org/licenses/by/4.0/)\n", "provided you give appropriate credit to the original author(s) and the source, provide a link to the Creative Commons license,\n", "[and indicate if changes were made. The Creative Commons Public Domain Dedication waiver (http://creativecommons.org/](http://creativecommons.org/publicdomain/zero/1.0/)\n", "[publicdomain/zero/1.0/) applies to the data made available in this article, unless otherwise stated.](http://creativecommons.org/publicdomain/zero/1.0/)\n", "\n", "\n", "\n"]}, {"output_type": "display_data", "data": {"text/plain": ["{'format': 'PDF 1.7',\n", " 'title': 'WHO cone bio-assays of classical and new-generation long-lasting insecticidal nets call for innovative insecticides targeting the knock-down resistance mechanism in Benin',\n", " 'author': '<PERSON>',\n", " 'subject': 'Malaria Journal, doi:10.1186/s12936-017-1727-x',\n", " 'keywords': 'LLINs,Bio-efficacy,Piperonyl butoxide,Resistant mosquitoes',\n", " 'creator': 'ocrmypdf 16.1.2 / Tesseract OCRhOCR 5.3.4',\n", " 'producer': 'pikepdf 8.13.0',\n", " 'creationDate': \"D:20170214204129+05'30'\",\n", " 'modDate': \"D:20240423063541+00'00'\",\n", " 'trapped': '',\n", " 'encryption': None,\n", " 'page': 2,\n", " 'total_pages': 11,\n", " 'file_path': '/content/pdfs/Allossogbe_et_al_2017_Mal_J.pdf'}"]}, "metadata": {}}, {"output_type": "stream", "name": "stdout", "text": ["<PERSON><PERSON><PERSON><PERSON> et al. <PERSON> J (2017) 16:77 Page 2 of 11\n", "\n", "\n", "\n", "**Background**\n", "Malaria is a major public health problem worldwide,\n", "and particularly so in Benin. It remains a permanent\n", "threat from its high morbidity (214 million) and mortality (438,000). Africa is the most endemic region\n", "affected (395,000 deaths per year) [1]. It affects onefifth of the world population. However, this proportion\n", "has decreased significantly by 37% between 2000 and\n", "2015 due to the effect of malaria prevention and treatment methods, including long-lasting insecticidal nets\n", "(LLINs), indoor residual spraying of residual insecticides\n", "(IRS), chemo-prevention for pregnant women and children, and therapeutic treatment with artemisinin-based\n", "combinations.\n", "\n", "Among these prevention methods, LLINs have\n", "emerged in recent years as a privileged tool to prevent\n", "malaria. Te insecticides selected by the World Health\n", "Organization (WHO) for LLIN treatment are pyrethroids, which have little toxicity to humans, are effective at low doses, are fast acting (knock-down effect)\n", "and, along with repellants, have an irritant effect [2]. Te\n", "Abuja Conference, which brought together all the leaders\n", "of Africa and other UN representative states, donors and\n", "NGOs in April 2000, gave impetus to a political commitment to the fight against malaria with the use of insecticide treated nets (ITNs) [3]. Efforts are being made to\n", "increase accessibility for populations, especially pregnant\n", "women and children under five, who are vulnerable to\n", "malaria, a major cause of perinatal mortality, low birth\n", "weight and maternal anaemia [1].\n", "Several research studies have been conducted and\n", "have shown the effectiveness of ITNs in the fight against\n", "malaria in Burkina Faso [4], Cameroon [5], Gambia [6–\n", "9], the Democratic Republic of Congo [10], Kenya [11],\n", "Ghana [12], Benin [13] and Côte d’Ivoire [14].\n", "However, several studies have shown that <PERSON><PERSON><PERSON><PERSON>\n", "gambiae s.l. has developed strong resistance to pyrethroids and DDT in Benin, with a very high knock-down\n", "resistance frequency of approximately 80% in the urban\n", "areas of Cotonou and in rural areas [15–23].\n", "Despite this resistance developed by An. gam<PERSON> s.l. to\n", "pyrethroids, LLINs remain effective in vector resistance\n", "areas [24] and provide protection through their mechanical barrier role [25]. However, <PERSON><PERSON><PERSON> et al. [26] showed a\n", "decrease in their effectiveness in areas of high resistance\n", "of Anopheles in southern Benin. Major developed resistance mechanisms are the targets of modification (kdr\n", "resistance and ace-1R) and metabolic resistance (overexpression of detoxification enzymes, oxidases, esterases,\n", "GST) [27]. Te kdr mutation is associated with pyrethroid and DDT resistance, and ace-1R is associated with\n", "organophosphate and carbamate resistance (two classes\n", "of insecticides which are not used to treat LLINs) [15, 28].\n", "\n", "\n", "\n", "To increase the effectiveness of ITNs in areas of high\n", "resistance, new nets treated with a so-called new-generation of chemicals has been developed. Tey are treated\n", "with a synergist called piperonyl butoxide (PBO). For\n", "some LLINs, the PBO is used on all sides of the net\n", "(Olyset Plus [®] ). For others, only the upper part of the\n", "net is processed (PermaNet [®] 3.0). Te principle of an\n", "ITN synergist is to inhibit the action of detoxification\n", "enzymes, which will result in increasing the effectiveness of the insecticide against resistant populations of\n", "mosquitoes.\n", "Evidence of the efficacy of PermaNet 3.0 has been\n", "shown in some studies, particularly in Tanzania [29],\n", "but we do not know its specific action on mosquitoes\n", "carrying detoxification enzymes and on those carrying\n", "both detoxification and kdr mechanisms in West Africa,\n", "particularly in Benin. Tere have been limited data on\n", "the bio-efficacy of new-generation LLINs against multiresistant mosquitoes in Africa in general and particularly\n", "in Benin. Tus, the objective of this study is to evaluate the efficacy of long-lasting insecticidal nets (LLINs)\n", "treated with PBO on multi-resistant An. gambiae s.l.\n", "populations in Benin. It aims to assess the bio-efficacy\n", "of LLINs in areas with a high frequency of molecular\n", "resistance genes (kdr and ace-1R) and over-expression of\n", "detoxification enzymes (oxidases, esterases, GST). Te\n", "efficacy of the new-generation LLINs against pyrethroidresistant Anopheles was also compared to that of conventional LLINs.\n", "\n", "\n", "**Methods**\n", "\n", "**Study design**\n", "Tis study is transversal and compares variability of the\n", "efficacy of two different types of LLINs against An. gambiae s.l. carrying kdr resistance mutations and detoxification enzymes in Benin. Te two types of LLINs included\n", "conventional LLINs only treated with pyrethroids (Olyset\n", "Net, LifeNet, and PermaNet 2.0) and a second type of\n", "new-generation LLIN treated with pyrethroids and\n", "piperonyl butoxide (PBO), which inhibits the action of\n", "enzymes, particularly oxidases.\n", "Te study was conducted in Benin, a West African\n", "country from June 2015 to March 2016. Among the 12\n", "departments of Benin surveyed, seven were selected in\n", "this study (Atlantique, Littoral, Oueme, Zou, Borgou,\n", "Atacora and Alibori). Priority was given to areas where\n", "higher oxidase activity was observed compared to the\n", "susceptible strain An. gambiae <PERSON>. Tey were represented by Abomey, Cotonou, Porto-Novo, Zangnanado, Parakou, Malanville and Tanguiéta districts. Te\n", "assessment of oxidase activity was conducted on 50 An.\n", "gambiae s.l. collected from each district using haem-peroxidase assay as described by <PERSON><PERSON><PERSON> et al. [30].\n", "\n", "\n", "\n"]}, {"output_type": "display_data", "data": {"text/plain": ["{'format': 'PDF 1.7',\n", " 'title': 'WHO cone bio-assays of classical and new-generation long-lasting insecticidal nets call for innovative insecticides targeting the knock-down resistance mechanism in Benin',\n", " 'author': '<PERSON>',\n", " 'subject': 'Malaria Journal, doi:10.1186/s12936-017-1727-x',\n", " 'keywords': 'LLINs,Bio-efficacy,Piperonyl butoxide,Resistant mosquitoes',\n", " 'creator': 'ocrmypdf 16.1.2 / Tesseract OCRhOCR 5.3.4',\n", " 'producer': 'pikepdf 8.13.0',\n", " 'creationDate': \"D:20170214204129+05'30'\",\n", " 'modDate': \"D:20240423063541+00'00'\",\n", " 'trapped': '',\n", " 'encryption': None,\n", " 'page': 3,\n", " 'total_pages': 11,\n", " 'file_path': '/content/pdfs/Allossogbe_et_al_2017_Mal_J.pdf'}"]}, "metadata": {}}, {"output_type": "stream", "name": "stdout", "text": ["<PERSON><PERSON><PERSON><PERSON> et al. Malar J (2017) 16:77 Page 3 of 11\n", "\n", "\n", "\n", "Te larvae of these mosquito populations were collected\n", "in different ecological areas (vegetable, urban, rice and\n", "cotton areas). Te study was also conducted on resistant\n", "laboratory strains (kdr-Kisumu and ace-1R-Kisumu).\n", "\n", "\n", "**Study sites**\n", "\n", "**Malanville**\n", "\n", "Malanville district is bordered on the north by the Republic of Niger, on the south by Kandi and Segbana districts,\n", "on the west by Karimama district and on the east by the\n", "Republic of Nigeria. It has an area of 3016 km [2] and had a\n", "population of 144,843 inhabitants in 2013 (Fig. 1).\n", "\n", "\n", "**Tanguieta**\n", "It is bordered on the north by the Republic of Burkina\n", "Faso, on the south by Boukoumbe district, on the east by\n", "Kerou, Kouande and Tounkountouna districts and on the\n", "west by Materi and Cobly districts. It covers an area of\n", "5456 km [2] and had a population of 77,987 inhabitants in\n", "2013 (Fig. 1).\n", "\n", "\n", "**Abo<PERSON>y-Calavi**\n", "Abomey-Calavi is bounded on the north by Ze district,\n", "on the south by the Atlantic Ocean, on the east by Cotonou and So-Ava districts, and on the west Ouidah and\n", "Tori-Bossito districts. It has an area of 539 km [2] and had a\n", "population of 438,564 inhabitants in 2013 (Fig. 1).\n", "\n", "\n", "**Cotonou**\n", "\n", "Cotonou is bordered on the North by So-Ava district and\n", "Nokoue lake, on the south by the Atlantic Ocean, on the\n", "east by Seme-Podji and on the west by Abomey-Calavi\n", "district. It has an area of 79 km [2] and had a population of\n", "947,917 inhabitants in 2013 (Fig. 1).\n", "\n", "\n", "**Porto-Novo**\n", "\n", "Porto-Novo is bounded on the north by Akpro-Missérete\n", "and Avrankou districts, on the south by Seme-Podji, on\n", "the west by Aguegues district and on the east by Adjarra\n", "district. It covers an area of 223,552 km [2] and had a population of 318,608 inhabitants in 2013 (Fig. 1).\n", "\n", "\n", "**Parakou**\n", "\n", "It is bordered on the north by N’Dali district and on the\n", "south, east and west by Tchaourou district; it has an area\n", "of 441 km [2] and had a population of 213,498 inhabitants\n", "in 2013 (Fig. 1).\n", "\n", "\n", "**<PERSON><PERSON><PERSON><PERSON>**\n", "Tis town is bounded on the north by Dassa-Zoume district, on the south by Ouinhi and Zogbodomey districts,\n", "on the west by Cove, Zakpota and Djidja districts and\n", "on the east by Ketou and Adja-Ouere. It has an area of\n", "\n", "\n", "\n", "540 km [2] and had a population of 52,387 inhabitants in\n", "2013 (Fig. 1).\n", "\n", "\n", "**Larvae collection**\n", "\n", "Bio-efficacy tests were conducted at various selected\n", "sites. Such tests required mosquitoes of 2–5 days old,\n", "so the larvae were collected. Tese collections were\n", "conducted in the different localities mentioned above.\n", "Anopheles gambiae s.l. larvae and pupae were collected\n", "from different locations at each site and carried to the\n", "insectarium of the Entomological Research Center of\n", "Cotonou (CREC), where they were reared to adult stage\n", "at a relative humidity of 70–80% and a temperature of\n", "25–30 °C. Female adults aged 2–5 days were used for bioefficacy tests.\n", "\n", "\n", "**Highlighting resistance mechanisms**\n", "Before the bioassays, living and dead mosquito populations kept after susceptibility testing were analyzed by\n", "PCR to detect the genotypes of the kdr gene. Te detection of kdr mutation L1014F was performed according to\n", "the method of <PERSON> et al. [31].\n", "For the molecular characterization of insecticide resist\n", "ance, two molecular markers were used for characterization of the resistance genes, kdr and ace-1R.\n", "Similarly, for the biochemical characterization of resistance mechanisms, biochemical assays were performed\n", "to compare the activity levels of mixed function oxidases\n", "(MFO), non-specific esterases (NSE) and glutathione\n", "S-transferases (GST) according to the protocol described\n", "by <PERSON><PERSON><PERSON> et al. [32] in susceptible Ki<PERSON><PERSON> and field\n", "An. gambiae strains. Te mosquitoes used for biochemical analysis had not been exposed to insecticides before\n", "the biochemical assessment. Tese enzyme activities\n", "were measured using a sample of 50 mosquitoes per site.\n", "\n", "\n", "**Mosquito nets**\n", "Five types of long-lasting insecticidal nets were evaluated in this study. Te group of mono-treated LLINs\n", "included LifeNet (polypropylene LLIN with fiber coated\n", "with 340 mg/m [2] ± 25% deltamethrin), Olyset Net (polyethylene LLIN with permethrin incorporated into the\n", "fibers at 20 ± 3 g/kg), and PermaNet 2.0 (polyester LLIN\n", "with fiber coated with deltamethrin at 55 mg/m [2] ± 25%).\n", "Te group of new-generation LLINs included: Olyset\n", "Plus (same characteristics as Olyset Net but with PBO\n", "incorporated throughout the LLIN) and PermaNet 3.0\n", "(polyethylene roof with deltamethrin at 2.8 g/kg ± 25%\n", "and PBO at 4.0 g/kg ± 25% incorporated into the fibers, and polyester lateral sides with the fibers coated\n", "with deltamethrin at 2.8 g/kg ± 25%). All these nets were\n", "obtained from local markets. All nets included in the\n", "\n", "study are rectangular and were selected by type.\n", "\n", "\n", "\n"]}, {"output_type": "display_data", "data": {"text/plain": ["{'format': 'PDF 1.7',\n", " 'title': 'WHO cone bio-assays of classical and new-generation long-lasting insecticidal nets call for innovative insecticides targeting the knock-down resistance mechanism in Benin',\n", " 'author': '<PERSON>',\n", " 'subject': 'Malaria Journal, doi:10.1186/s12936-017-1727-x',\n", " 'keywords': 'LLINs,Bio-efficacy,Piperonyl butoxide,Resistant mosquitoes',\n", " 'creator': 'ocrmypdf 16.1.2 / Tesseract OCRhOCR 5.3.4',\n", " 'producer': 'pikepdf 8.13.0',\n", " 'creationDate': \"D:20170214204129+05'30'\",\n", " 'modDate': \"D:20240423063541+00'00'\",\n", " 'trapped': '',\n", " 'encryption': None,\n", " 'page': 4,\n", " 'total_pages': 11,\n", " 'file_path': '/content/pdfs/Allossogbe_et_al_2017_Mal_J.pdf'}"]}, "metadata": {}}, {"output_type": "stream", "name": "stdout", "text": ["<PERSON><PERSON><PERSON><PERSON> et al. Malar J (2017) 16:77 Page 4 of 11\n", "\n", "\n", "\n", "**Cone test**\n", "\n", "Te cone test is used to assess the effectiveness of an\n", "insecticide and its persistence on the net. It was conducted following the WHO protocol. Tis test aims to\n", "compare the behaviour of mosquitoes while in contact\n", "with treated mosquito nets without PBO or with PBO.\n", "Cone tests were performed on five types of nets (Olyset\n", "Plus, Olyset Net, LifeNet, PermaNet 2.0 and PermaNet\n", "3.0). Tese tests were carried out using fragments of\n", "LLINs (30 cm × 30 cm) cut from five (05) positions on\n", "each net. Two standard cones were fixed with a plastic\n", "sheet on each of the five (05) screen fragments. For PermaNet 3.0 LLIN, an additional two cones were added on\n", "the PBO-containing roof. Five unfed An. gambiae females\n", "aged 2–5 days (Kisumu or wild type) were introduced\n", "into each cone placed on the LLIN for 3 min. After exposure, the mosquitoes were removed from the cones using\n", "a mouth aspirator and then transferred into paper cups\n", "and provided 10% sugar solution. Mosquito knock-down\n", "was recorded every 5 min for 60 min. A negative control\n", "(untreated net) was included in each series of cone tests.\n", "After 24 h of observation, mortality post exposure was\n", "recorded. No correction of mortality with <PERSON>’s formula was used as mortality in the control was <5%. All\n", "these operations were carried out at a temperature of\n", "25 ± 2 °C and a humidity of 70 ± 10%.\n", "\n", "\n", "**Data analysis**\n", "According to the WHO, the bio-effectiveness threshold is\n", "95% knock-down and 80% mortality for laboratory mosquitoes; but for resistant field mosquito populations, we\n", "used a threshold of 70% knock-down and 50% mortality. Terefore, all nets showing less than 95% knockdown\n", "for laboratory mosquitoes and 70% for field mosquitoes\n", "after 60 min, or less than 50% mortality for laboratory\n", "mosquitoes and 50% for field mosquitoes after 24 h of\n", "observation, were considered ineffective. Tese knockdown thresholds were chosen taking into account the\n", "kdr resistance level observed in the country in general\n", "(>50%).\n", "Te inhibition of mortality induced by resistance\n", "mechanisms was estimated using the following equation:\n", "\n", "\n", "Inhibition = 1 − (p1 / p2) × 100\n", "\n", "\n", "=\n", "where p1 proportion of resistant mosquitoes dead and\n", "p2 = proportion of susceptible Kisumu mosquitoes dead.\n", "To determine if there was any significance difference\n", "between the outcome variables (knock-down, mortality\n", "and inhibition), Poisson regression (for numeric data)\n", "and logistic regression (for proportional data) were used.\n", "Te 50 and 95% knock-down times and their confidence\n", "intervals were obtained after log-probit regression using\n", "the method described by <PERSON><PERSON> [33].\n", "\n", "\n", "\n", "**Results**\n", "\n", "**Characteristics of the studied mosquito populations**\n", "Te majority of female mosquitoes were collected and\n", "identified morphologically as An. gam<PERSON> s.l. Te biochemical and molecular analyses indicated that among\n", "ten sites, five showed significantly higher oxidase activity than the susceptible strain Kisumu (Table 1). Esterases\n", "were significantly expressed in the Tanguieta mosquito\n", "population (Table 1). Over-expression of glutathioneS-transferase was observed at four sites (Table 1). However, the allelic frequency of the kdr mutation was high at\n", "almost all sites and ranged from 0.03 to 0.93.\n", "\n", "\n", "**Knock-down (KD) and mortality of laboratory strains**\n", "Figure 2 shows the proportion of laboratory mosquitoes\n", "(ace-1<PERSON>-<PERSON><PERSON><PERSON>, kdr-<PERSON><PERSON><PERSON>, and susceptible <PERSON><PERSON><PERSON>)\n", "knocked down after 60 min for each LLIN. <PERSON>\n", "Plus and PermaNet 3.0 LLINs induced 100% knock-down\n", "of <PERSON>. gam<PERSON><PERSON>. Te knock-down effect was\n", "96.15% for Olyset, 90.2% for LifeNet and 93.22% for PermaNet 2.0.\n", "\n", "With the ace-1R-Kisumu strain, which carries the acetylcholinesterase-1 resistance gene, there was a knockdown effect greater than 95% for all nets, with 98.11% for\n", "LifeNet, 100% for Olyset, 98.18% for Olyset Plus, 97.96%\n", "for PermaNet 2.0, and 98.78% for PermaNet 3.0 (Fig. 2).\n", "For the kdr-Kisumu strain (carrying the resistance\n", "knock-down), the knock-down effects observed were\n", "89.29% for LifeNet, 63.64 for Olyset Net, 71.43% for\n", "Olyset Plus, 45.78 for PermaNet 2.0 and 71.05% for PermaNet 3.0 (Fig. 2).\n", "<PERSON><PERSON><PERSON> and ace-1R-Ki<PERSON><PERSON> (Fig. 3). With the kdrKisumu strain, mortality was 16% for Olyset Net, 26% for\n", "PermaNet 2.0, 28% for LifeNet, and 32.1% for Olyset Plus\n", "but was more than 82.9% for PermaNet 3.0. Terefore,\n", "based on the bio-efficacy threshold set by WHO (80%),\n", "PermaNet 3.0 was effective on all laboratory strains, and\n", "Olyset Plus was only effective on the susceptible and aceR1-Kisumu strains (Fig. 3).\n", "\n", "\n", "**Inhibition of mortality conferred by the kdr resistance gene**\n", "Comparing the mortality observed with the susceptible Kisumu strain with that of the resistant kdr-Kisumu\n", "\n", "strain, the inhibition of mortality induced by the kdr gene\n", "regarding the effectiveness of LLINs was 84% for Olyset\n", "Net, 74% for PermaNet 2.0, 72% for LifeNet, 68% for\n", "Olyset Plus and 17% for PermaNet 3.0.\n", "\n", "\n", "**Knock-down (Kd) effect and mortality induced**\n", "**by mosquito nets on local An. gambiae s.l.**\n", "Approximately 2819 local An. gambiae s.l. mosquitoes\n", "and 889 An. gam<PERSON>e <PERSON> laboratory strain mosquitoes were tested on different types of LLINs. Tables 2 and\n", "\n", "\n", "\n"]}, {"output_type": "display_data", "data": {"text/plain": ["{'format': 'PDF 1.7',\n", " 'title': 'WHO cone bio-assays of classical and new-generation long-lasting insecticidal nets call for innovative insecticides targeting the knock-down resistance mechanism in Benin',\n", " 'author': '<PERSON>',\n", " 'subject': 'Malaria Journal, doi:10.1186/s12936-017-1727-x',\n", " 'keywords': 'LLINs,Bio-efficacy,Piperonyl butoxide,Resistant mosquitoes',\n", " 'creator': 'ocrmypdf 16.1.2 / Tesseract OCRhOCR 5.3.4',\n", " 'producer': 'pikepdf 8.13.0',\n", " 'creationDate': \"D:20170214204129+05'30'\",\n", " 'modDate': \"D:20240423063541+00'00'\",\n", " 'trapped': '',\n", " 'encryption': None,\n", " 'page': 5,\n", " 'total_pages': 11,\n", " 'file_path': '/content/pdfs/Allossogbe_et_al_2017_Mal_J.pdf'}"]}, "metadata": {}}, {"output_type": "stream", "name": "stdout", "text": ["<PERSON><PERSON><PERSON><PERSON> et al. <PERSON> J (2017) 16:77 Page 5 of 11\n", "\n", "\n", "**Fig. 1** Map of Benin showing the study locations\n", "\n", "\n", "\n"]}, {"output_type": "display_data", "data": {"text/plain": ["{'format': 'PDF 1.7',\n", " 'title': 'WHO cone bio-assays of classical and new-generation long-lasting insecticidal nets call for innovative insecticides targeting the knock-down resistance mechanism in Benin',\n", " 'author': '<PERSON>',\n", " 'subject': 'Malaria Journal, doi:10.1186/s12936-017-1727-x',\n", " 'keywords': 'LLINs,Bio-efficacy,Piperonyl butoxide,Resistant mosquitoes',\n", " 'creator': 'ocrmypdf 16.1.2 / Tesseract OCRhOCR 5.3.4',\n", " 'producer': 'pikepdf 8.13.0',\n", " 'creationDate': \"D:20170214204129+05'30'\",\n", " 'modDate': \"D:20240423063541+00'00'\",\n", " 'trapped': '',\n", " 'encryption': None,\n", " 'page': 6,\n", " 'total_pages': 11,\n", " 'file_path': '/content/pdfs/Allossogbe_et_al_2017_Mal_J.pdf'}"]}, "metadata": {}}, {"output_type": "stream", "name": "stdout", "text": ["<PERSON><PERSON><PERSON><PERSON> et al. <PERSON> J (2017) 16:77 Page 6 of 11\n", "\n", "\n", "**Table 1 Biochemical and molecular characteristics of the Anopheles gambiae s.l. populations tested**\n", "\n", "\n", "\n", "**Strains of An. gambiae** **Average oxidase activ-** **Average α esterase**\n", "**s.l.** **ity (min/mg protein)** **activity (min/mg**\n", "**protein)**\n", "\n", "\n", "\n", "**Average β esterase**\n", "**activity (min/mg**\n", "**protein)**\n", "\n", "\n", "\n", "**Average glutathione-**\n", "**S-transferase activity**\n", "**(min/mg protein)**\n", "\n", "\n", "\n", "**kdr frequency**\n", "\n", "\n", "\n", "Kisumu 0.1015 [a] 0.07409 [a] 0.07655 [a] 0.3846 [a] 0 [a]\n", "\n", "\n", "Agblangandan 0.07966 [a] 0.07883 [a] 0.06117 [a] 0.7319 [b] 0.03 [a]\n", "\n", "Abomey-Calavi 0.08454 [a] 0.07149 [a] 0.05929 [a] 0.4295 [a] 0.93 [b]\n", "\n", "Akron 0.1604 [b] 0.08589 [a] 0.07897 [a] 2.221 [b] 0.74 [b]\n", "\n", "\n", "Houeyiho 0.17.39 [b] 0.07694 [a] 0.08774 [a] 0.4042 [a] 0.9 [b]\n", "\n", "Vossa 0.07566 [a] 0.06897 [a] 0.06389 [a] 0.7078 [a] 0.84 [b]\n", "\n", "\n", "Ladji 0.1737 [b] 0.07146 [a] 0.0774 [a] 1.194 [b] 0.92 [b]\n", "\n", "Bame 0.1106 [a] 0.0588 [a] 0.06223 [a] 0.2901 [a] 0.78 [b]\n", "\n", "\n", "Malanville 0.06549 [a] 0.04949 [a] 0.04871 [a] 0.1723 [a] 0.90 [b]\n", "\n", "\n", "Parakou 0.1536 [b] 0.08124 [a] 0.08871 [a] 0.4698 [a] 0.74 [b]\n", "\n", "\n", "Tanguieta 0.2267 [b] 0.1585 [b] 0.1442 [b] 1.182 [b] 0.85 [b]\n", "\n", "\n", "a, b Values with the same superscript do not differ significantly at α = 0.05\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "**Knock-down (KD) and mortality induced by the LLINs**\n", "**on mono-resistance mosquito strains**\n", "Only PermaNet 3.0, Olyset Plus and LifeNet LLINs\n", "showed a knock-down effect greater than 50% at Agblangandan, Vossa, Zangnanado and Malanville (areas of low\n", "resistance) (Table 2). Tese knock-down values varied\n", "between 51 and 95%. At Abomey, only PermaNet 3.0 and\n", "Olyset Plus LLINs showed a knock-down effect greater\n", "than 50%.\n", "PermaNet 3.0 was the only LLIN that showed significantly higher mortality of greater than 50% in all localities\n", "where mosquitoes carried only the kdr gene. Te average\n", "mortality for other types of LLINs tested in these areas\n", "varied from 5 to 47% (Table 2). Tese mortality rates varied from 0 to 14% for Olyset, 7 to 27% for LifeNet, from 9\n", "to 22% for Olyset Plus, from 24 to 47% for PermaNet 2.0\n", "and from 40 to 86% for PermaNet 3.0.\n", "\n", "\n", "**Inhibition of mortality in mono-resistant An. gambiae s.l.**\n", "\n", "**strains**\n", "\n", "Te observed inhibition of mortality induced by kdr\n", "resistance of local mosquito strains on LLIN effectiveness\n", "was 100–86% for Olyset, 92–73% for LifeNet, 53–76% for\n", "PermaNet 2.0, 78–91% for Olyset Plus and 14–60% for\n", "PermaNet 3.0. Tese inhibition rates are similar to those\n", "\n", "observed with the kdr-Kisumu strain (p > 0.05).\n", "\n", "\n", "\n", "\n", "\n", "3 show the percentage of local strain mosquitoes knocked\n", "down after 60 min for LifeNet, Olyset Net, Olyset Plus,\n", "PermaNet 2.0, and PermaNet 3.0.\n", "\n", "\n", "\n", "**Knock-down (KD) and mortality induced by the LLINs**\n", "**on multi-resistant mosquito strains (carrying kdr**\n", "**and biochemical resistance mutations)**\n", "In areas with multi-resistance, the knock-down effects\n", "observed were also low (Table 3).\n", "At Akron, the percentage of mosquitoes knocked\n", "down after 60 min was 31.48% [19.52–45.55] and\n", "74.55% [60.99–85.33] for Olyset Net and Olyset Plus,\n", "\n", "\n", "\n"]}, {"output_type": "display_data", "data": {"text/plain": ["{'format': 'PDF 1.7',\n", " 'title': 'WHO cone bio-assays of classical and new-generation long-lasting insecticidal nets call for innovative insecticides targeting the knock-down resistance mechanism in Benin',\n", " 'author': '<PERSON>',\n", " 'subject': 'Malaria Journal, doi:10.1186/s12936-017-1727-x',\n", " 'keywords': 'LLINs,Bio-efficacy,Piperonyl butoxide,Resistant mosquitoes',\n", " 'creator': 'ocrmypdf 16.1.2 / Tesseract OCRhOCR 5.3.4',\n", " 'producer': 'pikepdf 8.13.0',\n", " 'creationDate': \"D:20170214204129+05'30'\",\n", " 'modDate': \"D:20240423063541+00'00'\",\n", " 'trapped': '',\n", " 'encryption': None,\n", " 'page': 7,\n", " 'total_pages': 11,\n", " 'file_path': '/content/pdfs/Allossogbe_et_al_2017_Mal_J.pdf'}"]}, "metadata": {}}, {"output_type": "stream", "name": "stdout", "text": ["<PERSON><PERSON><PERSON><PERSON> et al. <PERSON> J (2017) 16:77 Page 7 of 11\n", "\n", "\n", "**Table 2 Distribution of the knock-down rate observed in localities where there was only one resistance mechanism (kdr)**\n", "\n", "\n", "**Strains** **LLINs** **N mosquito tested** **KD after 60 min** **95% CI** **Mortality after 24 h (%)**\n", "\n", "\n", "Malanville LifeNet 55 72.27 [59.03–83.86] 27.27\n", "\n", "\n", "Olyset Net 53 30.19 [18.34–44.34] 05.56\n", "\n", "\n", "Olyset Plus 51 54.9 [40.34–68.87] 21.56\n", "\n", "\n", "PermaNet 2.0 59 28.81 [17.76–42.08] 47.46\n", "\n", "\n", "PermaNet 3.0 84 95.24 [88.25–98.69] 61.90\n", "\n", "\n", "Abomey-Calavi LifeNet 53 9.43 [3.13–20.66] 7.54\n", "\n", "\n", "Olyset Net 54 11.11 [4.18–22.63] 5.56\n", "\n", "\n", "Olyset Plus 55 29.09 [17.62–49.90] 20\n", "\n", "\n", "PermaNet 2.0 52 70.49 [57.43–81.84] 26.92\n", "\n", "\n", "PermaNet 3.0 72 81.94 [71.1–90.02] 86.11\n", "\n", "\n", "<PERSON><PERSON><PERSON><PERSON> (Bamè) LifeNet 58 68.97 [55.45–80.46] 10.34\n", "\n", "\n", "Olyset Net 54 23.08 [12.53–36.84] 00\n", "\n", "\n", "Olyset Plus 55 33.96 [21.51–46.27] 09.43\n", "\n", "\n", "PermaNet 2.0 53 52.83 [38.63–66.7] 03.77\n", "\n", "\n", "PermaNet 3.0 75 63.93 [57.61–79.47] 62.67\n", "\n", "\n", "Vossa LifeNet 54 62.96 [48.74–75.71] 20.37\n", "\n", "\n", "Olyset Net 57 21.05 [11.37–33.89] 14.03\n", "\n", "\n", "Olyset Plus 53 41.51 [28.13–55.87] 15.05\n", "\n", "\n", "PermaNet 2.0 51 52.94 [38.45–67.07] 23.52\n", "\n", "\n", "PermaNet 3.0 73 79.45 [68.38–88.02] 39.75\n", "\n", "\n", "N number, KD knock-down, min minutes, CI confidence interval, h hours\n", "\n", "\n", "\n", "respectively; 70.49% [57.43–81.84] and 81.71% [71.63–\n", "89.38] for PermaNet 2.0 and PermaNet 3.0, respectively,\n", "and 30.77% [18.71–45.1] for LifeNet. At Houéyiho, the\n", "knock-down effect was 23.08% [12.53–36.84] and 49.15%\n", "\n", "[35.89–62.5] for Olyset Net and Olyset Plus, respectively;\n", "46.3% [32.62–60.39] and 73.5% [61.46–83.97] for PermaNet 2.0 and PermaNet 3.0, respectively, and 61.11%\n", "\n", "[46.87–74.08] for LifeNet. It was generally observed that\n", "knock-down was significantly higher with Olyset Plus\n", "than with Olyset on multi-resistant Akron and Houéyiho strains (p < 0.05). Te same observation was made\n", "with PermaNet 3.0, whose knock-down was significantly\n", "higher than that observed with PermaNet 2.0.\n", "Te same observations were made at Ladji, Parakou\n", "and Tanguiéta, where the KD induced by Olyset Plus was\n", "higher than that of Olyset. Similarly, PermaNet 3.0 (98%)\n", "was more effective than PermaNet 2.0 (39%) (Table 3).\n", "However, at Tanguieta, only three LLINs were tested.\n", "Te three types of mosquitoes tested showed a KD effect\n", "≥75%. Overall, in areas where there was high activity of\n", "oxidase enzymes associated with the kdr gene, only three\n", "LLINs (LifeNet, Olyset Plus, and PermaNet 3.0) showed\n", "a KD effect that was generally high. However, the mortality observed in these populations was generally low\n", "(Table 3). Only the PermaNet 3.0 LLIN induced significantly higher mortality (p < 0.001) that was generally\n", "greater than 75% (Table 3).\n", "\n", "\n", "\n", "**Inhibition of mortality in multi-resistant strains**\n", "Te inhibition of the mortality induced by LLINs\n", "observed with strains carrying several resistance mechanisms (compared to the susceptible strain Ki<PERSON>mu)\n", "ranged from 60 to 96% for Olyset, 53 to 90.2% for\n", "LifeNet, 45 to 86% for PermaNet 2.0, 59 to 76% for Olyset\n", "Plus and 11 to 55% for Permanet 3.0. <PERSON><PERSON> inhibition\n", "\n", "rates are similar to those observed with the kdr-Kisumu\n", "\n", "strains (p > 0.05).\n", "\n", "\n", "**Knock-down time of LLINs on local An. gambiae s.l. strains**\n", "Te average time estimated for knock-down of 50% of\n", "resistant local An. gambiae s.l. populations was significantly shorter with PermaNet 3.0 (12 min) (p < 0.001),\n", "followed by Olyset Plus and LifeNet (33 min). However, the time required for 95% of mosquitoes to be\n", "knocked down was high for all LLINs. Generally, there\n", "was a slower effect with LLINs treated with permethrin\n", "(Table 4).\n", "\n", "\n", "**Discussion**\n", "\n", "Tis study is one of the first conducted in Benin to compare the response of local malaria vectors in Benin to\n", "several LLINs recommended by the WHO. It helps to\n", "observe the variation in mortality of vectors submitted\n", "to different types of LLINs. Tis mortality was generally\n", "low, especially with LLINs only treated with pyrethroids.\n", "\n", "\n", "\n"]}, {"output_type": "display_data", "data": {"text/plain": ["{'format': 'PDF 1.7',\n", " 'title': 'WHO cone bio-assays of classical and new-generation long-lasting insecticidal nets call for innovative insecticides targeting the knock-down resistance mechanism in Benin',\n", " 'author': '<PERSON>',\n", " 'subject': 'Malaria Journal, doi:10.1186/s12936-017-1727-x',\n", " 'keywords': 'LLINs,Bio-efficacy,Piperonyl butoxide,Resistant mosquitoes',\n", " 'creator': 'ocrmypdf 16.1.2 / Tesseract OCRhOCR 5.3.4',\n", " 'producer': 'pikepdf 8.13.0',\n", " 'creationDate': \"D:20170214204129+05'30'\",\n", " 'modDate': \"D:20240423063541+00'00'\",\n", " 'trapped': '',\n", " 'encryption': None,\n", " 'page': 8,\n", " 'total_pages': 11,\n", " 'file_path': '/content/pdfs/Allossogbe_et_al_2017_Mal_J.pdf'}"]}, "metadata": {}}, {"output_type": "stream", "name": "stdout", "text": ["<PERSON><PERSON><PERSON><PERSON> et al. <PERSON> J (2017) 16:77 Page 8 of 11\n", "\n", "\n", "**Table 3 Distribution of the knock-down rate observed in localities where there were several resistance mechanisms**\n", "\n", "**(kdr** **+ metabolic resistance)**\n", "\n", "\n", "**Strains** **LLINs** **N mosquito tested** **KD after 60 min** **95% CI** **Mortality (%)**\n", "\n", "\n", "Agblangandan LifeNet 53 50.94 [36.83–64.96] 15.09\n", "\n", "\n", "Olyset Net 54 20.75 [10.84–34.11] 07.4\n", "\n", "\n", "Olyset Plus 55 50.91 [37.07–64.65] 34.72\n", "\n", "\n", "PermaNet 2.0 47 36.17 [22.67–51.58] 17.02\n", "\n", "\n", "PermaNet 3.0 66 60.61 [47.80–72.42] 65.15\n", "\n", "\n", "Ladji LifeNet 57 85.96 [74.2–93.74] 47.36\n", "\n", "\n", "Olyset Net 57 50.88 [37.28–64.37] 40.35\n", "\n", "\n", "Olyset Plus 56 42.86 [29.71–56.78] 41.07\n", "\n", "\n", "PermaNet 2.0 50 66 [51.23–78.79] 14\n", "\n", "\n", "PermaNet 3.0 69 88.41 [78.42–94.86] 44.93\n", "\n", "\n", "Akron LifeNet 52 30.77 [18.71–45.1] 15.38\n", "\n", "\n", "Olyset Net 54 31.48 [19.52–45.55] 5.56\n", "\n", "\n", "Olyset Plus 55 74.55 [60.99–85.33] 25.45\n", "\n", "\n", "PermaNet 2.0 61 70.49 [57.43–81.84] 54.09\n", "\n", "\n", "PermaNet 3.0 82 81.71 [71.63–89.38] 89.02\n", "\n", "\n", "Parakou LifeNet 51 43.14 [29.34–57.75] 09.80\n", "\n", "\n", "Olyset Net 52 26.92 [15.56–4 1.02] 07.69\n", "\n", "\n", "Olyset Plus 50 66 [51.23–78.79] 28\n", "\n", "\n", "<PERSON><PERSON><PERSON> 2.0 56 39.29 [26.49–53.25] 37.50\n", "\n", "\n", "<PERSON><PERSON><PERSON> 3.0 88 98.86 [93.83–99.97] 82.95\n", "\n", "\n", "Houeyiho LifeNet 54 61.11 [46.87–74.08] 14.81\n", "\n", "\n", "Olyset Net 52 23.08 [12.53–36.84] 3.84\n", "\n", "\n", "Olyset Plus 59 49.15 [35.89–62.5] 23.72\n", "\n", "\n", "<PERSON><PERSON><PERSON> 2.0 54 46.3 [32.62–60.39] 22.22\n", "\n", "\n", "<PERSON><PERSON><PERSON> 3.0 65 73.85 [61.46–83.97] 61.54\n", "\n", "\n", "Tanguieta LifeNet – – – –\n", "\n", "\n", "– – – –\n", "Olyset Net\n", "\n", "\n", "Olyset Plus 51 74.51 [60.36–85.67] 56.86\n", "\n", "\n", "PermaNet 2.0 62 75.81 [63.25–85.78] 32.26\n", "\n", "\n", "PermaNet 3.0 86 100 [88.78–100] 78.82\n", "\n", "\n", "N number, KD knock-down, min minutes, CI confidence interval, h hours\n", "\n", "\n", "\n", "**Table 4 Probable time for 50 and 95% knock-down**\n", "\n", "**of Anopheles gambiae s.l. per <PERSON>IN**\n", "\n", "\n", "**LLINs** **50% KDT** **95% CI** **95% KDT** **95% CI**\n", "\n", "**(min)** **(min)**\n", "\n", "\n", "LifeNet 33.12 [32.5–33.91] 425.13 [385.6–\n", "468.69]\n", "\n", "\n", "Olyset Net 98.74 [90.4– 10,257.58 [7090.39–\n", "107.85] 14,839.5]\n", "\n", "\n", "Olyset Plus 33.44 [32.56– 674.68 [595.91–\n", "34.34] 763.86]\n", "\n", "\n", "PermaNet 42.3 [41.26– 468.28 [424.57–\n", "2.0 43.37] 516.49]\n", "\n", "\n", "PermaNet3.0 12.61 [12.30– 137.99 [131.6–\n", "12.93] 144.69]\n", "\n", "\n", "%KDT knock down time, IC 95% confidence interval at 95%, min minutes, CI\n", "confidence interval\n", "\n", "\n", "\n", "Cone tests showed that LLINs treated with piperonyl\n", "butoxide and pyrethroids (especially PermaNet 3.0) have\n", "optimum efficacy on all strains of An. gambiae s.l. (mono\n", "and multi-resistant).\n", "Several studies have shown a decrease in the bio-efficacy of LLINs against local pyrethroid-resistant vectors\n", "\n", "[34, 35]. Te effectiveness of LLINs treated only with deltamethrin (PermaNet 2.0 and LifeNet) was found to be\n", "significantly lower compared to that of nets treated with\n", "deltamethrin and PBO. Te same observation was made\n", "with the LLINs treated with permethrin only (Olyset Net)\n", "and those treated with permethrin and PBO. However,\n", "the effectiveness of LLINs treated with permethrin was\n", "generally lower than that of LLINs treated with deltamethrin, with lower mortality and a very slow knock-down\n", "\n", "\n", "\n"]}, {"output_type": "display_data", "data": {"text/plain": ["{'format': 'PDF 1.7',\n", " 'title': 'WHO cone bio-assays of classical and new-generation long-lasting insecticidal nets call for innovative insecticides targeting the knock-down resistance mechanism in Benin',\n", " 'author': '<PERSON>',\n", " 'subject': 'Malaria Journal, doi:10.1186/s12936-017-1727-x',\n", " 'keywords': 'LLINs,Bio-efficacy,Piperonyl butoxide,Resistant mosquitoes',\n", " 'creator': 'ocrmypdf 16.1.2 / Tesseract OCRhOCR 5.3.4',\n", " 'producer': 'pikepdf 8.13.0',\n", " 'creationDate': \"D:20170214204129+05'30'\",\n", " 'modDate': \"D:20240423063541+00'00'\",\n", " 'trapped': '',\n", " 'encryption': None,\n", " 'page': 9,\n", " 'total_pages': 11,\n", " 'file_path': '/content/pdfs/Allossogbe_et_al_2017_Mal_J.pdf'}"]}, "metadata": {}}, {"output_type": "stream", "name": "stdout", "text": ["<PERSON><PERSON><PERSON><PERSON> et al. <PERSON> J (2017) 16:77 Page 9 of 11\n", "\n", "\n", "\n", "time (KDT 50 and 95%) compared to other LLINs. In a\n", "recent study conducted in Benin [36], Olyset Plus, treated\n", "with permethrin + PBO, demonstrated a higher efficacy\n", "than Olyset Net against wild multi-resistant An. gambiae\n", "s.l. in experimental huts, as observed in WHO cone tests\n", "used in the present study. In south-western Ethiopia [35]\n", "and in Uganda [34], a reduced efficacy of mono-treated\n", "LLINs was also observed against wild resistant An. gambiae s.l. in comparison with Permanet 3.0 treated with\n", "deltamethrin + PBO. Te results are similar to those\n", "observed in this study. However, these studies did not\n", "include Olyset Plus, the second type of new-generation\n", "LLINs treated with permethrin + PBO.\n", "Te reduced efficacy of LLINs treated with permethrin\n", "would be related to the strong resistance of the local vectors to permethrin due to the resistance selection pressures generated by the use of the same class of insecticide\n", "for malaria vector control in public health and for pest\n", "control in agriculture [16, 17, 23, 37, 38].\n", "Te comparison of LLIN bio-efficacy performed in this\n", "study provides the necessary information for the selection of appropriate LLINs for mass distribution. Te optimal and constant efficacy of PermaNet 3.0 LLINs on all\n", "vector populations shows that this combination of deltamethrin and PBO on LLINs is a most successful strat\n", "egy against pyrethroid resistance in Benin. Variations in\n", "the mortality of vectors also showed that certain types\n", "of LLINs are more appropriate than others for distribution in specific regions. Tis is related to the fact that\n", "the effectiveness of an LLIN depends on the characteristics of the mosquito population tested and the chemical\n", "structure of the molecule (insecticide) used.\n", "Te mosquito populations assessed in the present study\n", "were characterized by a high frequency of the kdr gene.\n", "Tis high frequency was probably due to the massive use\n", "of pyrethroids in agriculture and public health. In some\n", "areas, such as Tanguieta, Parakou, Houeyiho, Akron,\n", "and Ladji, farmers and gardeners use huge amounts of\n", "insecticides to reduce pests in their crops, which explains\n", "the presence and strong expression of several resistance\n", "mechanisms in the mosquito populations [39, 40]. Overproduction of resistance enzymes in these areas would be\n", "linked to pressure on mosquito larvae from insecticides\n", "used by farmers to protect vegetable crops [41–43]. Tis\n", "expression of the kdr resistance gene induced a 17–84%\n", "reduction in LLIN efficacy against laboratory strains.\n", "Tese frequencies are similar to those observed in natural populations of An. gambiae s.l. Tis observation\n", "shows that the kdr gene is the main mechanism involved\n", "in the reduction of the effectiveness of LLINs. Although\n", "detoxification enzymes contribute to resistance, their\n", "impact is successfully inhibited by the presence of PBO\n", "on new-generation LLINs and the remaining part is more\n", "\n", "\n", "\n", "likely related to the presence of kdr gene in the mosquito\n", "populations. <PERSON><PERSON> also suggests that the search for new\n", "molecules or combinations of molecules that target the\n", "kdr resistance mechanism should be promoted.\n", "Te WHO recommends preventive measures against\n", "vector resistance to insecticides [44]. Te results of this\n", "study therefore constitute important evidence that can\n", "guide decision making in the selection and distribution of\n", "high efficacy LLINs in specific regions of Benin. Te use\n", "of LLINs that showed high bio-efficacy against the local\n", "vector populations should be encouraged to contribute\n", "substantively to reducing the transmission of malaria in\n", "Benin.\n", "\n", "Tis study also suggests the need to develop a routine\n", "for monitoring the bio-efficacy of LLINs against local\n", "malaria vectors for the replacement of ineffective LLINs.\n", "However, community studies would be needed to evaluate the epidemiological impact of these LLINs to confirm\n", "whether or not the low efficacy observed is followed by a\n", "loss of the epidemiological impact of these nets.\n", "Although the important results of this study, it had\n", "certain limitations. Strong evaluation would have been\n", "possible if tunnel tests were conducted on LLINs that\n", "did not meet the criteria of 80% mortality with resistant mosquito strains. In addition, a chemical analysis of\n", "the LLINs prior to the start of the study would also have\n", "improved the quality of the results. However, all the\n", "LLINs demonstrated a good performance with susceptible laboratory stain <PERSON><PERSON><PERSON> (mortality > 80%), as recommended by WHO [45], and the focus of this study was\n", "to demonstrate the important role of resistance mechanisms on LLINs efficacy.\n", "\n", "\n", "**Conclusion**\n", "\n", "Tis study showed variable effectiveness of LLINs on An.\n", "gambiae s.l. populations from different localities surveyed\n", "from north to south in Benin. Te new-generation LLINs\n", "with pyrethroids and PBO (PermaNet 3.0 and Olyset Plus)\n", "showed higher efficacy than conventional LLINs (PermaNet 2.0, LifeNet and Olyset net). However, the strong\n", "resistance of local vectors to permethrin suggests that the\n", "combination of deltamethrin + PBO is the most appropriate strategy against local vectors in Benin. Although the\n", "addition of PBO (targeting many biochemical mechanisms\n", "of resistance) significantly increased the mortality of mosquitoes, the significantly high role of the kdr resistance\n", "gene in the low efficacy of LLINs calls for LLIN technology\n", "innovation that specifically targets this mechanism.\n", "\n", "\n", "**Authors’ contributions**\n", "\n", "MA, VG and MCA designed the study, supervised laboratory work, analyzed\n", "data and wrote the manuscript. BY, RA, FA and BA conducted field collections,\n", "laboratory tests and contributed in the writing of the manuscript. AH and GGP\n", "helped in the study design and revising the manuscript. All authors read and\n", "approved the final manuscript.\n", "\n", "\n", "\n"]}, {"output_type": "display_data", "data": {"text/plain": ["{'format': 'PDF 1.7',\n", " 'title': 'WHO cone bio-assays of classical and new-generation long-lasting insecticidal nets call for innovative insecticides targeting the knock-down resistance mechanism in Benin',\n", " 'author': '<PERSON>',\n", " 'subject': 'Malaria Journal, doi:10.1186/s12936-017-1727-x',\n", " 'keywords': 'LLINs,Bio-efficacy,Piperonyl butoxide,Resistant mosquitoes',\n", " 'creator': 'ocrmypdf 16.1.2 / Tesseract OCRhOCR 5.3.4',\n", " 'producer': 'pikepdf 8.13.0',\n", " 'creationDate': \"D:20170214204129+05'30'\",\n", " 'modDate': \"D:20240423063541+00'00'\",\n", " 'trapped': '',\n", " 'encryption': None,\n", " 'page': 10,\n", " 'total_pages': 11,\n", " 'file_path': '/content/pdfs/Allossogbe_et_al_2017_Mal_J.pdf'}"]}, "metadata": {}}, {"output_type": "stream", "name": "stdout", "text": ["<PERSON><PERSON><PERSON><PERSON> et al. <PERSON> J (2017) 16:77 Page 10 of 11\n", "\n", "\n", "\n", "**Author details**\n", "1 Centre de Recherche Entomologique de Cotonou (CREC), Cotonou,\n", "Benin. [2] Université d’Abomey-Calavi, Abomey‑Calavi, Benin. [3] Medical Care\n", "and Development International, Washington, USA.\n", "\n", "\n", "**Acknowledgements**\n", "We thank CREC personnel for their technical assistance and collaboration.\n", "\n", "\n", "**Competing interests**\n", "The authors declare that they have no competing interests.\n", "\n", "\n", "**Availability of data and materials**\n", "Data collected during this study are included in the published article and its\n", "additional files.\n", "\n", "\n", "**Funding**\n", "This work is supported by Faculty of Letters, Arts and Human Sciences of the\n", "University of Abomey-Calavi.\n", "\n", "\n", "Received: 5 December 2016  Accepted: 7 February 2017\n", "\n", "\n", "**References**\n", "\n", "1. WHO. World malaria report 2015. Geneva: World Health Organiza[tion; 2015. http://www.who.int/malaria/publications/world-malaria-](http://www.who.int/malaria/publications/world-malaria-report-2015/report/en/)\n", "[report-2015/report/en/. Accessed 15 Aug 2016.](http://www.who.int/malaria/publications/world-malaria-report-2015/report/en/)\n", "2. <PERSON><PERSON><PERSON><PERSON>, Lindbla<PERSON> KA, Mount DL, Atieli FK, Crawford S, Wolkon A, et al.\n", "Laboratory wash resistance of long-lasting insecticidal nets. Trop Med Int\n", "Health. 2005;10:1022–9.\n", "3. WHO. The Abuja declaration and the plan of action. An extract from the\n", "African Summit on Roll Back Malaria. Geneva: World Health Organization;\n", "[2003; p. 1–11. http://www.who.int/malaria/publications/atoz/whocd-](http://www.who.int/malaria/publications/atoz/whocdsrbm200346/en/)\n", "[srbm200346/en/. Accessed 15 Aug 2016.](http://www.who.int/malaria/publications/atoz/whocdsrbm200346/en/)\n", "4. <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, et al. Control\n", "of malaria using mosquito nets impregnated with pyrethroids in Burkina\n", "Faso. Bull Soc Path Exot Filiales. 1988;81:832–46 **(in French)** .\n", "5. <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Carnevale P. Evaluation des pratiques et des couts de lutte antivectorielle à\n", "l’échelon familial en Afrique Centrale. II. <PERSON> (Cameroun), juillet\n", "1988. <PERSON> Méd <PERSON>. 1990;70:137–44.\n", "6. <PERSON>, <PERSON>, <PERSON>, <PERSON> Francisco A, Shenton FC,\n", "<PERSON> et al. The effect of insecticide-treated bed nets on mortality of Gambian children. Lancet. 1991;337:1499–502.\n", "7. <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, Lange<PERSON>,\n", "<PERSON>, et al. A comparison of the efficacy of insecticide-treated and\n", "untreated bed nets in preventing malaria in Gambian children. Trans R\n", "Soc Trop Med Hyg. 1995;89:596–8.\n", "8. <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>,\n", "et al. The Gambian National Impregnated Bednet Programme: costs,\n", "consequences and net cost-effectiveness. Soc Sci Med. 1998;46:181–91.\n", "9. <PERSON>, <PERSON>, Lindsay <PERSON>, Greenwood BM. A trial of bed nets\n", "(mosquito nets) as a malaria control strategy in a rural area of The Gambia, West Africa. Trans R Soc Trop Med Hyg. 1988;82:212–5.\n", "10. <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>t J. Moustiquaires impregnées contre le paludisme au Zaire. <PERSON> Belge Méd\n", "Trop. 1993;73:37–53.\n", "11. <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, et al.\n", "Insecticide-treated bednets reduce mortality and severe morbidity\n", "from malaria among children on the Kenyan coast. Trop Med Int Health.\n", "1996;1:139–46.\n", "12. <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, et al.\n", "Impact of permethrin impregnated bednets on child mortality in\n", "Kassena-Nankana district, Ghana: a randomized controlled trial. Trop Med\n", "Int Health. 1996;1:147–54.\n", "13. <PERSON><PERSON><PERSON><PERSON><PERSON> PM, <PERSON><PERSON> <PERSON><PERSON> of deltamethrin impregnated mosquito\n", "nets on the transmission of malaria in the coastal lagoon area, Benin. Bull\n", "Soc Path Exot. 1996;89:291–8 **(in French)** .\n", "\n", "\n", "\n", "14. <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>L. Reduction\n", "of childhood malaria by social marketing of insecticide-treated nets:\n", "a case–control study of effectiveness in Malawi. Am J Trop Med Hyg.\n", "2005;73:622–5.\n", "15. <PERSON><PERSON>, <PERSON><PERSON><PERSON> F, <PERSON><PERSON>, Brengues C, Carnevale P, Guillet P.\n", "Pyrethroid cross resistance spectrum among populations of Anopheles\n", "gambiae s.s. from Côte d’Ivoire. J Am Mosq Control Assoc. 1999;15:53–9.\n", "16. <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> S. Resistance of malaria vectors to pyrethrins\n", "used for impregnating mosquito nets in Benin, West Africa. Bull Soc\n", "Pathol Exot. 1999;92:123–30 **(in French)** .\n", "17. <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>. Use of agricultural insecticides in\n", "Benin. Bull Soc Pathol Exot. 2005;98:400–5 **(in French)** .\n", "18. <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>\n", "<PERSON><PERSON> et al. Multiple insecticide resistance mechanisms in Anopheles\n", "gambiae and Culex quinquefasciatus from Benin, West Africa. Acta Trop.\n", "2007;101:207–16.\n", "19. <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, et al. Evaluation\n", "of indoxacarb, an oxadiazine insecticide for the control of pyrethroidresistant Anopheles gam<PERSON>e (Diptera: Culicidae). J Med Entomol.\n", "2007;44:270–6.\n", "20. <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, et al. Managing insecticide resistance in malaria vectors by combining carbamatetreated plastic wall sheeting and pyrethroid-treated bed nets. Malar J.\n", "2009;8:233.\n", "21. <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>\n", "<PERSON><PERSON> et al. Insecticide resistance status in Anopheles gambiae in southern\n", "Benin. Malar J. 2010;9:83.\n", "22. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>. Insecticide\n", "resistance in the Anopheles gambiae complex in Benin: a nationwide\n", "survey. Med Vet Entomol. 2011;25:256–67.\n", "23. <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, OkeAgbo F, et al. Malaria vectors resistance to insecticides in Benin: current\n", "trends and mechanisms involved. Parasit Vectors. 2015;8:223.\n", "24. <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, et al. Protective efficacy of lambda-cyhalothrin treated nets in Anopheles gambiae pyrethroid resistance areas of Cote d’Ivoire. Am J Trop Med Hyg.\n", "2005;73:859–64.\n", "25. <PERSON>, <PERSON><PERSON><PERSON>, Maxwell <PERSON>. Insecticide treated nets: impact on\n", "vector populations and relevance of initial intensity of transmission and\n", "pyrethroid resistance. J Vector Borne Dis. 2003;40:1–8.\n", "26. <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON> Loss of household protection from use of insecticide-treated nets against pyrethroidresistant mosquitoes, Benin. Emerg Infect Dis. 2012;18:1101–6.\n", "27. <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>. Evidence of multiple mechanisms providing carbamate and organophosphate resistance in field An.\n", "gambiae population from Atacora in Benin. Parasit Vectors. 2014;7:568.\n", "28. <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> Evidence for selection\n", "of insecticide resistance due to insensitive acetylcholinesterase by\n", "carbamate-treated nets in Anopheles gambiae s.s. (Diptera: Culicidae)\n", "from Côte d’Ivoire. J Med Entomol. 2003;40:985–8.\n", "29. <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>. Efficacy of PermaNet [®] 3.0 and PermaNet [®] 2.0 nets against laboratory-reared and wild Anopheles gambiae\n", "sensu lato populations in northern Tanzania. Infect Dis Poverty. 2017;6:11.\n", "30. <PERSON><PERSON><PERSON>, <PERSON>c<PERSON><PERSON> JC, Vulule JM. Association of heme peroxidase\n", "activity measured in single-mosquitoes identifies individuals expressing\n", "an elevated oxidase for insecticide resistance. J Am Mosq Control Assoc.\n", "1997;13:233–7.\n", "31. <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Devonshire AL, et al. Molecular characterization of pyrethroid knockdown\n", "resistance (kdr) in the major malaria vector Anopheles gambiae s.s. Insect\n", "Mol Biol. 1998;7:179–84.\n", "32. <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>.\n", "The role of gene splicing, gene amplification and regulation in mosquito\n", "insecticide resistance. Philos Trans R Soc Lond B Biol Sci. 1998;353:1695–9.\n", "33. <PERSON><PERSON> Review of probit analysis: a statistical treatment of the sigmoid\n", "response curve. J R Stat Soc. 1947;110:263–6.\n", "34. <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>\n", "<PERSON><PERSON>, et al. Bioefficacy of long-lasting insecticidal nets against pyrethroidresistant populations of Anopheles gambiae s.s. from different malaria\n", "transmission zones in Uganda. Parasit Vectors. 2013;6:130.\n", "\n", "\n", "\n"]}, {"output_type": "display_data", "data": {"text/plain": ["{'format': 'PDF 1.7',\n", " 'title': 'WHO cone bio-assays of classical and new-generation long-lasting insecticidal nets call for innovative insecticides targeting the knock-down resistance mechanism in Benin',\n", " 'author': '<PERSON>',\n", " 'subject': 'Malaria Journal, doi:10.1186/s12936-017-1727-x',\n", " 'keywords': 'LLINs,Bio-efficacy,Piperonyl butoxide,Resistant mosquitoes',\n", " 'creator': 'ocrmypdf 16.1.2 / Tesseract OCRhOCR 5.3.4',\n", " 'producer': 'pikepdf 8.13.0',\n", " 'creationDate': \"D:20170214204129+05'30'\",\n", " 'modDate': \"D:20240423063541+00'00'\",\n", " 'trapped': '',\n", " 'encryption': None,\n", " 'page': 11,\n", " 'total_pages': 11,\n", " 'file_path': '/content/pdfs/Allossogbe_et_al_2017_Mal_J.pdf'}"]}, "metadata": {}}, {"output_type": "stream", "name": "stdout", "text": ["<PERSON><PERSON><PERSON><PERSON> et al. <PERSON> J (2017) 16:77 Page 11 of 11\n", "\n", "\n", "\n", "35. <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>\n", "N. Bio-efficacy of selected long-lasting insecticidal nets against pyrethroid resistant Anopheles arabiensis from South-Western Ethiopia. Parasit\n", "Vectors. 2012;5:159.\n", "36. <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, et al.\n", "Efficacy of Olyset [®] Plus, a new long-lasting insecticidal net incorporating\n", "permethrin and piperonil-butoxide against multi-resistant malaria vectors. PLoS ONE. 2013;8:e75134.\n", "37. <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\n", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, et al. Insecticide resistance in Anopheles gambiae:\n", "data from the first year of a multi-country study highlight the extent of\n", "the problem. Malar J. 2009;8:299.\n", "38. <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>’<PERSON><PERSON>, <PERSON><PERSON>, et al.\n", "Increase in susceptibility to insecticides with aging of wild Anopheles\n", "gambiae mosquitoes from Côte d’Ivoire. BMC Infect Dis. 2012;12:214.\n", "39. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Kindé-Gazard DA. Screening of pesticide\n", "residues in soil and water samples from agricultural settings. Malar J.\n", "2006;5:22.\n", "40. <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>.\n", "Development of vegetable farming: a cause of the emergence of\n", "\n", "\n", "\n", "insecticide resistance in populations of Anopheles gambiae in urban areas\n", "of Benin. Malar J. 2009;8:103.\n", "41. <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> T<PERSON>, Darriet F,\n", "et al. The role of agricultural use of insecticides in resistance to pyrethroids in Anopheles gambiae s.l. in Burkina Faso. Am J Trop Med Hyg.\n", "2002;67:617–22.\n", "42. <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>ji <PERSON>. Exploring mechanisms of multiple insecticide resistance in a population of the malaria vector Anopheles\n", "funestus in Benin. PLoS ONE. 2011;6:e27760.\n", "43. <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> F, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Oké-Agbo F, et al.\n", "Bendiocarb resistance in Anopheles gambiae s.l. populations from Atacora\n", "department in Benin, West Africa: a threat for malaria vector control.\n", "Parasit Vectors. 2013;6:192.\n", "44. WHO. Test procedures for insecticide resistance monitoring in malaria\n", "vector mosquitoes. Geneva: World Health Organization; 2013.\n", "45. WHO. Guidelines for laboratory and field-testing of long-lasting insecticidal nets. Geneva: World Health Organization; 2013. p. 1–102.\n", "\n", "\n", "\n", "\n", "\n"]}, {"output_type": "execute_result", "data": {"text/plain": ["[(None, None),\n", " (None, None),\n", " (None, None),\n", " (None, None),\n", " (None, None),\n", " (None, None),\n", " (None, None),\n", " (None, None),\n", " (None, None),\n", " (None, None),\n", " (None, None)]"]}, "metadata": {}, "execution_count": 11}]}, {"cell_type": "markdown", "source": ["## <PERSON>er"], "metadata": {"id": "c4g4deYjcCeZ"}, "id": "c4g4deYjcCeZ"}, {"cell_type": "code", "source": ["from marker.converters.pdf import PdfConverter\n", "from marker.config.parser import ConfigParser\n", "\n", "from marker.models import create_model_dict\n", "from marker.schema import BlockTypes\n", "\n", "artifact_dict = create_model_dict()\n", "artifact_dict"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "KtnRWV1KoanW", "outputId": "4efccf4b-adb1-4264-e05e-f4405690332a"}, "id": "KtnRWV1KoanW", "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["{'layout_model': <surya.layout.LayoutPredictor at 0x7f91c1e6ce90>,\n", " 'recognition_model': <surya.recognition.RecognitionPredictor at 0x7f91d5c9e550>,\n", " 'table_rec_model': <surya.table_rec.TableRecPredictor at 0x7f91ca3e67d0>,\n", " 'detection_model': <surya.detection.DetectionPredictor at 0x7f93d2cb5ad0>,\n", " 'ocr_error_model': <surya.ocr_error.OCRErrorPredictor at 0x7f91bded8b10>}"]}, "metadata": {}, "execution_count": 110}]}, {"cell_type": "code", "source": [], "metadata": {"id": "P4V8h8pOUZ0c"}, "id": "P4V8h8pOUZ0c", "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["%%time\n", "# Step 2: Configure to skip OCR on tables\n", "cli_opts = {\n", "    \"OCR_ENGINE\": None,\n", "    \"processors\": None,\n", "    \"output_format\": 'markdown'\n", "}\n", "config = ConfigParser(cli_opts).generate_config_dict()\n", "\n", "# Step 3: Create converter with custom TOC header processor\n", "converter = PdfConverter(\n", "    config=config,\n", "    artifact_dict=artifact_dict,\n", "    renderer=ConfigParser(cli_opts).get_renderer(),\n", ")\n", "\n", "# Step 4: Run conversion\n", "result = converter(file_path)\n", "md, meta, imgs = result.markdown, result.metadata, result.images"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "DiczzGCekvuu", "outputId": "bfdfb371-3a82-46de-ddd1-40a05a85468e"}, "id": "DiczzGCekvuu", "execution_count": null, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["Recognizing layout: 100%|██████████| 1/1 [00:05<00:00,  5.19s/it]\n", "Running OCR Error Detection: 100%|██████████| 1/1 [00:00<00:00, 35.45it/s]\n", "Detecting bboxes: 100%|██████████| 1/1 [00:03<00:00,  3.47s/it]\n", "Recognizing Text: 100%|██████████| 607/607 [03:03<00:00,  3.31it/s]\n", "Detecting bboxes: 100%|██████████| 1/1 [00:00<00:00,  1.28it/s]\n", "Recognizing Text: 100%|██████████| 292/292 [01:02<00:00,  4.65it/s]\n", "Recognizing tables: 100%|██████████| 1/1 [00:08<00:00,  8.70s/it]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["CPU times: user 4min 35s, sys: 13.2 s, total: 4min 48s\n", "Wall time: 4min 47s\n"]}]}, {"cell_type": "code", "source": ["Markdown(md)"], "metadata": {"colab": {"resources": {"http://localhost:8080/_page_5_Figure_1.jpeg": {"data": "", "ok": false, "headers": [["content-length", "0"]], "status": 404, "status_text": ""}, "http://localhost:8080/_page_2_Figure_10.jpeg": {"data": "", "ok": false, "headers": [["content-length", "0"]], "status": 404, "status_text": ""}}, "base_uri": "https://localhost:8080/", "height": 1000}, "id": "UcRe8yxiF-mF", "outputId": "ef1cdcbf-f62e-40eb-c8dd-cf885de6f989"}, "id": "UcRe8yxiF-mF", "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["<IPython.core.display.Markdown object>"], "text/markdown": "# The impact of permethrin-impregnated bednets on malaria vectors of the Kenyan coast\n\nC. N. M. MBOGO,<sup>1,2</sup> N. M. BAYA,<sup>1</sup> A. V. O. OFULLA.<sup>2</sup> J. I. GITHURE<sup>2</sup> and <PERSON><PERSON> <PERSON><PERSON><sup>1,3</sup>\n\n<sup>1</sup> Clinical Research Centre and <sup>2</sup> Biomedical Sciences Research Centre, Kenya Medical Research Institute (KEMRI), Kenya, and <sup>3</sup> Nuffield Department of Clinical Medicine, Oxford University, John Radcliffe Hospital, Oxford, U.K.\n\n> **Abstract.** The effects of introducing permethrin-impregnated bednets on local populations of the malaria vector mosquitoes Anopheles funestus and the An.gambiae complex was monitored during a randomized controlled trial at Kilifi on the Kenyan coast. Pyrethrum spray collections inside 762 households were conducted between May 1994 and April 1995 after the introduction of bednets in half of the study area. All-night human bait collections were performed in two zones (one control and one intervention) for two nights each month during the same period. PCR identifications of An gambiae sensulato showed that proportions of sibling species were An gambiae sensu  $stricto > An.merus > An. arabiensis.$\n\n> Indoor-resting densities of An.gambiae s.l. and the proportion of engorged females decreased significantly in intervention zones as compared to control zones. However, the human blood index and *Plasmodium falciparum* sporozoite rate remained unaffected. Also vector parous rates were unaltered by the intervention, implying that survival rates of malaria vectors were not affected. The human-biting density of An.gambiae s.l., the predominant vector, was consistently higher in the intervention zone compared to the control zone, but showed 8% reduction compared to pre-intervention biting rates - versus 94% increase in the control zone.\n\n> Bioassay, susceptibility and high-performance liquid chromatography results all indicated that the permethrin content applied to the nets was sufficient to maintain high mortality of susceptible vectors throughout the trial. Increased rates of early outdoorbiting, as opposed to indoor-biting later during the night, were behavioural or vector composition changes associated with this intervention, which would require further monitoring during control programmes employing insecticide-treated bednets.\n\n> **Key words.** Anopheles arabiensis, An.funestus, An.gambiae, An.merus, malaria, permethrin-impregnated bednets, mosquito nets, Kenya Coast.\n\n## Introduction\n\nBednets (mosquito nets) are traditionally used to ward off mosquitoes and have been advocated as a means of personal protection against malaria vectors in Africa (W.H.O., 1986). However, torn or incorrectly tucked nets provide little additional protection and mosquitoes are adept at feeding through nets on exposed limbs (Port & Boreham, 1982). For these reasons the application of a residual insecticide (of low mammalian toxicity) to bednets was suggested in the late 1970s as a means of reinstating the effectiveness of torn or incorrectly used nets as a\n\nCorrespondence: Dr Charles N. M. Mbogo, Kilifi Research Unit, KEMRI, P.O. Box 230, Kilifi, Kenya.\n\nman-vector barrier (Curtis et al., 1990). Synthetic pyrethroids such as permethrin and deltamethrin, which have high insecticidal and excito-repellant properties, are most suitable for the treatment of bednets and have been adopted in several countries as part of national malaria control activities (Curtis et al., 1990).\n\nMalaria is the single largest cause of death among children living in tropical Africa (World Bank, 1993). Across this continent, the rates of malaria transmission and endemicity levels vary widely. The impact of insecticide-treated bednets (ITBN) upon the vector population's ability to transmit, and hence the degree of personal protection, depend largely upon the intensity of transmission in any given area. Despite encouraging effects of ITBN in reducing both morbidity and mortality among Gambian children by over 60% (Snow et al., 1988; Alonso et al.,\n\n1991), those results apply to an area with low rates of sporozoite challenge with extremely seasonal vector activity (Lindsay et al., 1993). The limitations of recommendations based on one transmission setting prompted the W.H.O. to replicate ITBN trials in several other areas of Africa where transmission characteristics are very different to those of The Gambia.\n\nAccordingly, in July 1993, ITBN were introduced as part of a randomized controlled trial, conducted in coastal Kenya, to examine their role in reducing childhood mortality and severe malaria morbidity (Nevill et al., 1996). This paper reports the entomological context in which the Kenyan trial was conducted and the impact of ITBNs on malaria vectors in the coastal area of Kenya.\n\n## **Materials and Methods**\n\nStudy area. The study area is located in Kilifi district, 60 km north of Mombasa on the Kenyan coast, extending 30 km inland and 40 km along the Indian Ocean coast north of Kilifi town. The area was designated in 1989 for intensive entomological (Mbogo et al., 1993b, 1995), demographic (Snow et al., 1994) and epidemiologic studies (Snow et al., 1993) of malaria. The principal vectors of malaria are the Anopheles gambiae Giles complex with a minor role played by Anopheles funestus Giles. These two vectors yield on average ten sporozoite inoculations per person per year. Among the people inhabiting this geographical area, annual rates of *P.falciparum* challenge range from less than one to sixty per person (Mbogo et al., 1995). Despite these annual rates of *P.falciparum* inoculation being lower than in most parts of tropical Africa, it has been estimated that at least one in fifteen children will develop severe life-threatening malaria before their fifth birthday (Snow et al., 1993).\n\nThe study population comprises approximately 60,000 inhabitants living mainly in traditional style houses (walls of sticks and mud) with a coconut thatch roof. Unscreened windows, holes in the walls and large open eaves provide easy access for mosquitoes. Homesteads are scattered and separated from one another by open farmland. Maize is the staple crop cultivated for home consumption; cashews and coconuts are grown as cash crops. During the 1989 national Kenyan census the study area was divided into seventy-two enumeration zones, of which thirtyeight were randomly allocated to receive ITBN.\n\nGreen polyester 100 denier mosquito nets (SiamDutch, Thailand) were issued to be used over all beds within the intervention zones and impregnated with 25% permethrin (cis:trans 40:60) emulsifiable concentrate (Imperator, ICI, U.K.) to achieve a target dose of 0.5 g of permethrin per  $m<sup>2</sup>$  of netting. Nets were re-impregnated every 6 months to coincide with the two main rainy seasons: in May, the beginning of the long rains; and October, towards onset of the short rains. In the intervention area, people were asked not to wash their nets until immediately before the next re-impregnation. The remaining thirty-four zones served as the contemporaneous non-intervention control area where bednet ownership was less than 6% (Snow et al., 1992).\n\nThe study area was mapped using a hand-held satellite navigational system (Trimble Navigation Europe, U.K.) and computerized using MapInfo<sup>R</sup> software (Troy Ltd, U.S.A.).\n\nEntomological surveillance. One homestead from each zone was randomly sampled for mosquitoes by Pyrethrum spray-catch (PSC) each month (May 1994 to April 1995); no homestead was sampled more than once. Nine zones (five intervention and four control) were excluded from the sampling frame because they formed part of ongoing entomological studies since 1989. Houses were visited in the morning (07.00–11.30 hours) and occupants asked to tie their bednets up away from the bed. White sheets were laid on the floors and the rooms sprayed with pyrethrum aerosol. All mosquitoes knocked down were collected into labelled petri dishes lined with moist cotton wool and taken to the laboratory at Kilifi for further investigation.\n\nPre-intervention all-night catches of human-biting mosquitoes were undertaken once a week at four sentinel households per zone (five intervention and four control), between May 1992 and April 1993. Post-intervention human-bait collections were performed at one control and one intervention zone (drawn from the nine pre-intervention zones and excluded from PSC catches) for two nights each month between May 1994 and April 1995. Pairs of experienced catchers recruited from the study area were positioned either indoors or outdoors at each site and collections made from 18.00 until 07.00 hours. Catchers rotated in shifts and used aspirators and torches to catch mosquitoes which landed on exposed limbs. Each hourly catch was placed into a prelabelled polystyrene container and taken to the laboratory at Kilifi for assessment.\n\nLaboratory procedures. Mosquito species were identified morphologically and scored as unfed, blood-fed or gravid. A proportion of An.gambiae s.l. females collected by both PSC and all-night biting catches were identified to sibling species by the method of polymerase chain reaction, PCR (Paskewitz & Collins, 1990). Primers used were specific for An.gambiae s.s., An.arabiensis and An.merus, members of the An.gambiae complex found at the Kenyan coast (Mosha & Petrarca, 1983). Samples of An.gambiae s.l. collected on human bait were dissected for parity determination as described by Detinova (1962). Mosquitoes collected by PSC were prepared for sporozoite enzyme-linked immunosorbent assay (ELISA) testing using monoclonal antibodies to detect circumsporozoite proteins of P.falciparum (Wirtz et al., 1987). Tests were assessed visually for positivity (Beier & Koros, 1991). Bloodmeals were identified by direct ELISA using anti-host (IgG) conjugates against human, cow and goat (Beier et al., 1988).\n\nBioassay, bioavailability and susceptibility tests. Nets were randomly selected from intervention households, between 1 and 17 months after they were issued. These nets were visually inspected and coded as either clean or dirty, and for the number of re-impregnations each net had received. Bioassay cones (W.H.O., 1975) were attached to the nets by means of elastic bands whilst the nets were hung upright in the laboratory. Two cones were used, one placed at the top of the net and the other on the lower portion toward the floor. Twenty laboratory-colonized female An. gambiae s.s. were introduced to each cone and exposed to the netting for 3 min before they were removed to paper cups. Delayed mortality was recorded after the mosquitoes had been left in the paper cups for 24 h with adequate sugar water in an ambient temperature of 25°C and a relative humidity of 72%. Four repeats per net were performed. Identical procedures were followed for untreated nets to serve as controls. Mortality was corrected for control mortality where the latter exceeded 20% of exposed mosquitoes.\n\nSample swatches of netting fabric were collected for highperformance liquid chromatography (HPLC) immediately after nets were impregnated for the first time and 11 months later, after two re-impregnations. HPLC assays were conducted at the Centers for Disease Control, Atlanta, U.S.A., to determine the concentration of the active cis isomer of permethrin per m<sup>2</sup> of netting.\n\nSusceptibility of wild-caught female mosquitoes, collected from an area adjacent to the study area, was determined in February 1995 using the W.H.O. (1981) test kit and procedure. Unfed An. gambiae s.l. females ( $n = 415$ ) were exposed to 0.25% permethrin test paper for 1 h. Delayed mortality was measured 24 h post-exposure to the permethrin or control papers, and corrected if control mortality exceeded 20%.\n\nStatistical analysis. The mean number of mosquitoes per house was calculated (from PSC data) for each of the zones sampled over the 12 months of surveillance (Table 1). The annual means of the thirty-three intervention zones were compared with the annual means of the thirty control zones using a Mann-Whitney U test (given their non-normal distribution). Human blood indices, sporozoite rates, parity and man-biting rates were analysed post-intervention using a Chi-square test, or controlling for pre-intervention rates using a Mantel Haenzel Chi-square test.\n\n## **Results**\n\nA total of 762 houses were sampled by PSC between May 1994 and April 1995. Of the 362 houses sampled within the nonintervention (control) area, 31.5% (114) yielded at least one An.gambiae s.l. or An.funestus, compared to only 11.3% (45/\n\n400) of the intervention houses sampled during the same period  $(\\chi^2 = 45.9, v = 1, P < 0.001)$ . As described previously (Mbogo et al., 1995), large between-zone variation in vector abundance occurs within this relatively small geographical area (Table 1). Comparing the ranks of the mean zonal densities of either An.gambiae s.l. or An.funestus per house indicates, before intervention, a significant difference of indoor-resting mosquito densities between intervention and non-intervention areas (Mann-Whitney U test,  $P < 0.0001$ ). Overall, post-intervention, there was a nine-fold reduction of the indoor-resting densities of both An.gambiae s.l. and An.funestus associated with ITBN use. Fig. 1 shows that the typical peaks of An.gambiae s.l. density during the long rains (May-August) and the short rains (November-December) were virtually eliminated in areas where ITBN were used.\n\nComposition of the An. gambiae complex differed significantly between non-intervention (control) and ITBN intervention areas (Table 2). Proportions of An.arabiensis, An.gambiae s.s. and An.merus were 7%, 49% and 44% respectively among seventytwo specimens identified by PCR from the intervention area. compared with 11%, 83% and 6% of these three sibling species, respectively, among 165 specimens identified from the nonintervention area. PSC densities of all three species were significantly different both between treatment areas and between species within areas (Table 2). Per house sampled, the intervention area had 2.9-fold more An.merus but 4-fold less An.arabiensis and 4.3-fold less An.gambiae s.s. than the non-intervention (control) area.\n\nIn houses with impregnated bednets, significantly fewer An.gambiae s.l. were found to be blood-fed and their humanblood index was lower than in control houses, although this difference was not statistically significant (Table 3). There were no significant differences between areas in the proportion of An.gambiae s.l. with detectable P.falciparum CS protein and\n\n![](_page_2_Figure_10.jpeg)\n\nFig. 1. Monthly abundance (May 1994 to April 1995) of indoor-resting An. gambiae s.l. females among households where ITBN were used (closed line) and households in a non-intervention (control) zone without bednets (dotted line).\n\n| An.gambiae s.l.<br>ż<br>0<br>$\\geq$<br>$\\sim$<br>≏<br>0.99<br>$\\circ$<br>$\\sim$ $\\sim$<br>$\\circ$<br>0<br>$\\sim$<br>$\\sim$<br>n u<br>n<br>$\\sim$<br>$\\circ$<br>$\\bullet$<br>houses<br>Σ,<br>$\\frac{2}{2}$<br>$\\overline{5}$<br>5<br>$\\overline{5}$<br>$\\vec{a}$<br>$\\overline{12}$<br>ż.<br>ā<br>$\\bar{c}$<br>$\\mathbf{r}$<br>$\\mathbf{z}$<br>≌<br>N<br>្ម<br>$\\overline{2}$<br>$\\overline{a}$<br>$\\overline{2}$<br>$\\frac{2}{2}$<br>$\\mathbf{5}$<br>$\\begin{array}{c}\\n2 \\\\ 2 \\\\ 3\\n\\end{array}$<br>$\\overline{5}$<br>⋖<br>Zone<br>28<br>$\\overline{\\phantom{0}}$<br>$\\overline{18}$<br>$\\overline{24}$<br>25<br>ສ<br>$\\frac{28}{28}$<br>$\\ddot{ }$<br>54<br>$\\infty$<br>$\\infty$<br>g<br>$\\mathbf{z}$<br>$\\mathbf{r}$<br>호<br>$\\overline{c}$<br>$\\overline{5}$<br>47<br>$\\frac{48}{5}$<br>$\\overline{6}$<br>$\\Xi$<br>$\\overline{a}$<br>ఠ | An.funestus<br>ပ ဦ              |                         |                             | Non-intervention (control)            |                         |               |                                        |\n|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|---------------------------------|-------------------------|-----------------------------|---------------------------------------|-------------------------|---------------|----------------------------------------|\n|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            |                                 | Anopheles               |                             |                                       | <u>ន</u>                | u g           | Anopheles                              |\n|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            |                                 | $(B + C)$ /A<br>density | Zone                        | houses<br>$\\leq \\frac{3}{2}$          | An.gambiae s.l.         | An.funestus   | $(18 + C)/A$<br>density                |\n|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            |                                 |                         |                             |                                       |                         |               |                                        |\n|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            |                                 | $\\frac{8}{1}$           | $\\boldsymbol{\\mathcal{S}}$  |                                       | $\\mathfrak{L}$          |               | 2.83                                   |\n|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            | 00                              | 1.33                    | ड                           | $\\vec{a}$ $\\vec{a}$                   | $\\equiv$                |               |                                        |\n|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            |                                 | 0.17                    |                             |                                       | 29                      |               | 2.42                                   |\n|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            |                                 | 0.17                    | 885                         |                                       | $\\infty$                | 000           |                                        |\n|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            |                                 | 0.00                    |                             |                                       | $\\frac{10}{2}$          |               |                                        |\n|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            |                                 | 0.08                    | జ                           |                                       |                         |               |                                        |\n|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            | 000000-000-000000-00000000-0000 | 0.17                    |                             | 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 |                         | 0000-00m0-    | <b>\\$88888888888888888888888888558</b> |\n|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            |                                 | 0.08                    |                             |                                       | $\\omega$ 4              |               |                                        |\n|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            |                                 | 0.25                    |                             |                                       |                         |               |                                        |\n|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            |                                 | 0.00                    |                             |                                       | 4                       |               |                                        |\n|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            |                                 |                         |                             |                                       | $\\rightarrow$ $-$       |               |                                        |\n|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            |                                 | 0.17<br>0.17            |                             |                                       |                         |               |                                        |\n|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            |                                 | 0.08                    |                             |                                       |                         |               |                                        |\n|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            |                                 |                         | 112835385544465585888865885 |                                       | $4 \\omega \\bar{\\omega}$ |               |                                        |\n|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            |                                 | 0.08                    |                             |                                       |                         |               |                                        |\n|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            |                                 | 0.42                    |                             |                                       | S                       | $2n + 9n - n$ |                                        |\n|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            |                                 | 0.17                    |                             |                                       | $\\equiv$                |               |                                        |\n|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            |                                 | $0.25$<br>$0.50$        |                             |                                       | 3973                    |               |                                        |\n|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            |                                 |                         |                             |                                       |                         |               |                                        |\n|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            |                                 | 0.08                    |                             |                                       |                         |               |                                        |\n|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            |                                 | 0.08                    |                             |                                       |                         |               |                                        |\n|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            |                                 | 0.00                    |                             |                                       |                         |               |                                        |\n|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            |                                 | 0.08                    |                             |                                       |                         | $\\bullet$     |                                        |\n|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            |                                 | 0.42                    |                             |                                       | 370                     | $\\rightarrow$ |                                        |\n|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            |                                 | 0.17                    |                             |                                       |                         |               |                                        |\n|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            |                                 | 0.50                    |                             |                                       |                         |               |                                        |\n| $\\overline{c}$                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |                                 | 0.08                    |                             |                                       |                         |               |                                        |\n| ∘<br>$\\overline{5}$ $\\overline{5}$                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         |                                 | 0.00                    |                             |                                       |                         |               |                                        |\n| 3<br><b>SSRFK</b>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          |                                 | 1.17                    |                             |                                       | 22522                   | $0 - n$ $N$   |                                        |\n| $\\sim$<br>$\\frac{13}{12}$                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  |                                 | 0.15                    |                             |                                       |                         |               |                                        |\n| $\\circ$                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    |                                 | 0.00                    |                             |                                       |                         |               |                                        |\n| $\\mathbf{r}$<br>77                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         |                                 | 0.08                    |                             |                                       |                         |               |                                        |\n| $\\overline{13}$<br>79                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      |                                 | 0.15                    |                             |                                       |                         |               |                                        |\n| 82<br>400<br>Total                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         | 4                               | 0.22                    |                             | 362                                   | 647                     | 83            | $2.02*$                                |\n\n|                           | Area                          |                        | Difference |          |                               |\n|---------------------------|-------------------------------|------------------------|------------|----------|-------------------------------|\n|                           | Non-intervention<br>(control) | Intervention<br>(ITBN) | $\\chi^2$   | P        |                               |\n| No. houses sampled by PSC | 362                           | 400                    |            |          |                               |\n| Species                   |                               |                        |            |          |                               |\n| arabiensis                | 18(11)                        | 5(7)                   | 7          | < 0.01   | $\\chi^2 = 167$ ,<br>23        |\n| gambiae                   | 137(83)                       | 35(49)                 | 60         | < 0.0001 | 172<br>$v = 2$ .              |\n| merus                     | 10(6)                         | 32(44)                 | 12         | < 0.001  | P < 0.0001<br>42 <sup>7</sup> |\n| Total An.gambiae s.l.     | 165 (100)                     | 72 (100)               | 48         | < 0.0001 |                               |\n\nTable 2. Numbers (%) of each sibling species of the Anopheles gambiae complex collected in each area by pyrethrum spray collections indoors.\n\nthe proportion of parous female An.gambiae s.l. collected on human bait (Table 3).\n\nTable 3 also gives the mean numbers of An. gambiae s.l. females/person/night landing on human bait, both indoors and outdoors, during periods of 12 months before and after intervention in both the ITBN area (185 man-nights pre- and 64 man-nights post-intervention) and the non-intervention area (182) man-nights pre- and 70 man-nights post-intervention), sampling consistently from the same two houses in each area. After introduction of ITBN, greater proportion of An.gambiae s.l. were caught biting outdoors (30.3%) in the intervention area, compared to the non-intervention area (23.2%): Mantel Haenzel allowing for differences pre- intervention:  $\\chi^2 = 26.0$ ,  $P < 0.0001$ . Fig. 2 suggests that there was a tendency toward earlier biting activity inside houses where ITBN were in use compared to the biting cycle in control houses: 12% of bites occurred before 22.00 hours in houses with ITBN, compared to only 7% in control houses.\n\nCalculating the product of the monthly man-biting rates and sporozoite rates shown in Table 3 (determined from all-night human bait catches in only two zones) suggests that the average annual sporozoite inoculation rate per person was not significantly reduced by the use of ITBN.\n\nIn an attempt to examine the possible influence on mosquitoes\n\nTable 3. An.gambiae s.l. collections from control (non-intervention) and ITBN (intervention) areas, pre-intervention (May 1992 to April 1993) and post-intervention (May 1994 to April 1995). Proportions bloodfed, parous and sporozoite positive from PSC samples; man-biting rates and sporozoite inoculation rates from human bait catches.\n\n|                                                 | Non-intervention<br>(control) | Intervention<br>(TTBN)   | Difference |         |\n|-------------------------------------------------|-------------------------------|--------------------------|------------|---------|\n|                                                 | $(n = 362)$                   | $(n = 400)$              | $\\chi^2$   | P       |\n| PSC surveys                                     |                               |                          |            |         |\n| Blood-fed (%)                                   | 55.5% (359/647)               | 28.0% (23/82)            | 20.9       | < 0.001 |\n| Human Blood Index (%)                           | 86.4% (242/280)               | 80.0% (16/20)            | 0.09       | >0.75   |\n| Sporozoite rate $(\\%)$ (csp positive)           | 5.0% (32/647)                 | 4.9% (4/82)              | 0.01       | >0.9    |\n| Human-bait surveys                              |                               |                          |            |         |\n| Parity (%)                                      |                               |                          |            |         |\n| <b>Before</b>                                   | 65.5% (129/197)               | 63.2% (427/676)          | 0.11       | < 0.75  |\n| After                                           | 63.4 (78/123)                 | 54.1% (242/447)          | 1.50       | < 0.25  |\n| Difference                                      | $\\chi^2$ = 0.05 P < 0.75      | $\\chi^2$ = 3.60 P < 0.05 |            |         |\n| Man-biting rate* per man per night $(n)$        |                               |                          |            |         |\n| <b>Indoors</b>                                  |                               |                          |            |         |\n| Before                                          | 5.3(182)                      | 93.4 (185)               |            |         |\n| After                                           | 3.7(70)                       | 23.5(64)                 |            |         |\n| Difference                                      | $-1.6$                        | $-69.9$                  |            |         |\n| <b>Outdoors</b>                                 |                               |                          |            |         |\n| <b>Before</b>                                   | 0.15(182)                     | 1.14(185)                |            |         |\n| After                                           | 0.94(70)                      | 10.20(64)                |            |         |\n| Difference                                      | $+0.79$                       | $+9.06$                  |            |         |\n| Annual sporozoite inoculation rate <sup>†</sup> |                               |                          |            |         |\n| <b>Before</b>                                   | 18.0                          | 59.6                     |            |         |\n| After                                           | 35.0                          | 54.1                     |            |         |\n| Difference                                      | $+94%$                        | $-9.2%$                  |            |         |\n\n\\* Sampling done in the same rooms before and after intervention, two houses per zone  $(n = number of man-nights collection)$ .\n\n<sup>+</sup> Calculated as a sum of the products of the monthly indoor man-biting rates and monthly sporozoite rates.\n\n![](_page_5_Figure_1.jpeg)\n\nFig. 2. Biting cycle (hourly percentage on human bait) of An gambiae s.l. in houses where ITBN were used (light bars) and in a non-intervention zone where bednets were not used (dark bars).\n\n(deleterious or beneficial effects) entering houses in the control (non-intervention) area close to the intervention area, we used longitude and latitude to establish precise distances from intervention zones of thirty-five houses in control zones 57, 58 and 59, selected because they generated the largest numbers of mosquitoes. Distances were classified as more or less than 400 m from the intervention area. Among eleven households within 400 m, 9% had at least one mosquito, significantly less than 46% of twenty-four households at a distance of more than 400 m from the intervention area. ( $\\chi^2 = 4.5$ ,  $v = 1$ ,  $P < 0.05$ ).\n\nDetails of user compliance of the intervention will be presented elsewhere (Some et al., in prep.). Potency of the permethrin applied to bednets remained high throughout the trial. HPLC analyses of the nets indicated that, following the first treatment, the average concentration of cis-permethrin was  $0.43$  g/m<sup>2</sup> (95% CL 0.34-0.52) on thirty-six nets. Eleven months later, following re-impregnation after 6 months, the average concentration on thirty-six nets was 1.36 g/m<sup>2</sup> (95% CL 1.21-1.51).\n\nTable 4. An.gambiae s.l. mortality within 24 h following exposure for 3 min to permethrin-impregnated bednets retrieved from intervention villages in May 1994 and March 1995.\n\n| Months in use<br>(no. of)<br>impregnations) | Percentage<br>mortality<br>(no. of nets tested) |\n|---------------------------------------------|-------------------------------------------------|\n| 0(1)                                        | 80.8(4)                                         |\n| $4 - 8(2)$                                  | 78.3 (13)                                       |\n| 10(3)                                       | 96.0(9)                                         |\n| 17(4)                                       | 99.8(5)                                         |\n\nPermethrin susceptibility tests of local wild-caught An.gambiae s.l., using the diagnostic dosage of 1 h exposure to  $0.25\\%$ permethrin in W.H.O. (1975) test kits, gave a mortality-rate of 94.5% when tests were undertaken 21 months after the trial began.\n\nBioassay tests with An.gambiae s.l. (3 min exposure, 24 h mortality) on various nets that had been used for up to 8 months following their initial impregnation in July 1993 gave greater than 78% kill (Table 4). Nets re-impregnated in April and November 1994 showed an increased killing capacity of 96-99.8%. Interestingly, nets which were found to be dirty with cooking soot had higher killing effects (94.1%) than nets which were clean  $(84.5\\%)$ .\n\n#### **Discussion**\n\nOur results demonstrate that permethrin-impregnated bednets exert a major impact upon the indoor-resting abundance of the principal vectors of *P.falciparum* malaria in coastal villages of Kenya. Indoor-resting densities of An.gambiae s.l. and An funestus were 9 times lower in houses where ITBN were in use, compared to households where no nets were used. This had the additional effect of eliminating the typical seasonal peaks in vector density usually seen in this part of Kenya (Fig. 1), despite evidence that more of the vector species were biting outdoors (Table 3). These findings are consistent with other studies of synthetic pyrethroid treated bednets or curtains in Africa (Lines et al., 1987; Majori et al., 1987; Lindsay et al., 1989, 1993; Magesa et al., 1991; Robert & Carnevale, 1991; Beach et al., 1993). The precise effect in each of these areas is difficult\n\nto compare, given the inherent differences in the sampling procedures used within each study. We opted not to use light traps (Lines et al., 1991; Mbogo et al., 1993a) in our estimation of vector abundance, because they tend to be less efficient in areas of low vector abundance (such as our study area) and have been shown to over-estimate parity rates in this area of Kenya (Petrarca et al., 1991). Furthermore, we required a simple and rapid means of monitoring endophilic mosquitoes over a wide geographical area, so as to truly reflect the impact of ITBN within our entire study population. Intensive entomological surveillance limited to a few sites – as suggested by the W.H.O.  $(1991)$  – can yield unrepresentative results in areas where marked overdispersion of vectors is common. However, it could be argued that reductions of indoor-resting densities - as determined by PSC - may simply reflect increased excito-repellency of the insecticides and not a reduction in the numbers of vectors coming to feed. Indeed, studies with exit traps in The Gambia have shown an increased rate of exophily due to ITBNs indoors (Snow et al., 1987; Miller et al., 1991). In addition, however, there is clear evidence that houses with pyrethroid-treated fabrics tend to significantly deter entry of vectors into the house (Lines et al., 1987; Lindsay et al., 1991). Further evidence from our study that man-vector contact was reduced is shown by the very highly significantly reduced proportion of An. gambiae s.l. found bloodfed in the early-morning PSC samples (Table 3). Human bait catches, however, revealed no significant reduction in the number of sporozoite inoculations an unprotected individual is likely to receive per year when living in a household where ITBN were used, compared to living in a house where no nets were in use. Whereas the sporozoite inoculation rate increased by 94% in the non-intervention area, for unaccountable (probably climatic) reasons between pre- and post-intervention years, it decreased by 8.3% in the ITBN intervention area, a significant reduction.\n\nInterestingly, our study did not demonstrate a significant reduction in the actual sporozoite rate or parity (an index of longevity) among vectors sampled from ITBN intervention zones compared to non-intervention (control) zones. Similar results were obtained in the Gambia, where bednets were also impregnated with permethrin  $0.5$  g/m<sup>2</sup>, and this has been interpreted as a probable lack of any so called 'mass effect' upon the vector population (Lindsay et al., 1993; Thomson et al., 1995). Mass effects would be difficult to prove in most field study designs, because the intervention could affect mosquito abundance in the untreated (control) as well as treated (ITBN) areas, as shown by the overall reductions compared to pre-intervention data in both The Gambia (Lindsay et al., 1993) and Burkina Faso (Robert & Carnevale, 1991). Hence Lines et al. (1987) and Lindsay et al. (1991) argued that, although individuals appear to be protected by ITBN against the bites of vector mosquitoes, there is no evidence that this increases the biting rate on unprotected neighbours. Under fortuitous circumstances, there may be some reduction of biting on people without ITBN if they are sufficiently closely associated with ITBN users to be afforded some protection. We have tried to assess this 'community protection' by studying three control communities in close proximity to intervention communities, comparing vector abundance by distance from the nearest houses where ITBN were widely employed. This analysis indicated that, within the non-intervention area, fewer houses closest to the intervention area had any malaria\n\nvectors compared to those further away.\n\nThe dipping procedures used for bednet impregnation during this trial provided adequate target treatment concentration of  $0.5$  g/m<sup>2</sup> (over 76% of all netting samples tested had excess of this figure), giving bioassay mortalities in excess of 80% throughout the study, increasing to almost 100% following multiple re-impregnations at half-yearly intervals (Table 4).\n\nPerhaps the greatest concern raised by this study is the observation that a significant proportion of malaria vectors appeared to bite earlier in the evening in houses where ITBN were used, with a greater tendency toward exophagy rather than the typical endophagy of most anthropophilic An gambiae.s.l. Furthermore, there was an apparent shift in sibling species composition of the An gambiae complex following the intro-duction of ITBN. Both An.merus and An.arabiensis have slightly different biting cycles to An. gambiae s.s. (Ivengar, 1962; White, 1974; Mosha & Petrarca, 1983). Earlier biting is associated with use of permethrin-treated bednets in Papua New Guinea (Charlwood & Graves, 1987). As the biting cycle change occurred immediately after installation of ITBNs in our study, in conjunction with the lack of evidence for a mass-killing effect, we conclude that the earlier biting reflects either an immediate intraspecific behavioural effect or a change in vector species proportions within the An.gambiae complex, and was not the result of selection for evolved behavioural resistance. Among our Kenyan study population, people usually 'go to bed' at 21.00-22.00 hours (unpublished data) and most children retire earlier, so their customs limit the opportunities for vectors to bite them, especially when they sleep under bednets. If ITBN are increasingly to be employed against malaria in tropical Africa, their effects on mosquito behaviour and insecticide susceptibility (cf. Vulule et al., 1996) should be monitored.\n\n#### **Acknowledgments**\n\nThis study was supported by funds from the UNDP/World Bank/ W.H.O. Special Programme for Research and Training in Tropical Diseases: The Wellcome Trust; The International Development and Research Centre of the Canadian International Development Agency, and by the Kenya Medical Research Institute. We are grateful for the assistance of all scientific and technical staff at the Kilifi Research Unit, particularly Ms Laura New, Dr Chris Nevill, Dr Kevin Marsh, Dr N. M. Peshu, Mr Barnes Kitsao, David Ireri and Reuben K. Peshu. We thank Dr Bill Hawley of KEMRI/CDC, Nairobi, for identification of mosquitoes by PCR, Dr Robert Wirtz for providing monoclonal antibodies (through a grant from the World Health Organization), Dr Jim Todd of CDC, Atlanta, for conducting the HPLC assays, and Dr Jo Lines for useful comments on the manuscript. Dr Bob Snow is a Senior Wellcome Trust Fellow in Basic Biomedical Sciences. This paper is published with the permission of the Director of the Kenya Medical Research Institute.\n\n# **References**\n\nAlonso, P.L., Lindsay, S.W., Armstrong, J.R.M., Conteh, M., Hill, A.G., David, P.H., Fegan, G., de Francisco, A., Hall, A.J., Shenton, F.C., Cham, K. & Greenwood, B.M. (1991) The effect of insecticidetreated bednets on mortality of Gambian children. Lancet, 337, 1499-1502.\n\n- Beach, R.F., Ruebush, T.K., Sexton, J.D., Bright, P.L., Hightower, A.N., Breman, J.G., Mount, D.L. & Oloo, A.J. (1993) Effectiveness of permethrin impregnated bed nets and curtains for malaria control in a holoendemic area of Western Kenya. American Journal of Tropical Medicine and Hygiene, 49, 290-300.\n- Beier, J.C. & Koros, J.K. (1991) Visual assessment of sporozoite and blood meal ELISA samples in malaria field studies. Journal of Medical Entomology, 28, 805-808.\n- Beier, J.C., Perkins, P.V., Wirtz, R.A., Koros, J., Diggs, D., Gargan, T.P. & Koech, D.K. (1988) Blood meal identification by direct enzymelinked immunosorbent assay (ELISA), tested on Anopheles (Diptera: Culicidae) in Kenva. Journal of Medical Entomology, 25, 9-16.\n- Charlwood, J.D. & Graves, P.M. (1987) The effect of permethrinimpregnated bednets on a population of Anopheles farauti in coastal Papua New Guinea. Medical and Veterinary Entomology, 1, 319- $327$\n- Curtis, C.F., Lines, J.D., Carnevale, P. & Robert, V. (1990) Impregnated bednets and curtains against malaria mosquitoes. Appropriate Methods of Vector Control (ed. by C. F. Curtis), pp. 5-46. CRC Press, Boca Raton, Florida\n- Detinova, T.S. (1962) Age-grading methods in Diptera of medical importance with special reference to some vectors of malaria. World Health Organization Monograph Series, 47, 1-216.\n- Ivengar, R. (1962) The bionomics of salt water Anopheles gambiae in East Africa. Bulletin of World Health Organization, 27, 223-229.\n- Lindsay, S.W., Snow, R.W., Broomfield, G.I., Janneh, M.S., Wirtz, R.A. & Greenwood, B.M. (1989) Impact of permethrin treated bednets on malaria transmission by the Anopheles gambiae complex in The Gambia. Medical and Veterinary Entomology, 3, 263-271.\n- Lindsay, S.W., Adiamah, J.H., Miller, J.E. & Armstrong, J.R.M. (1991) Pyrethroid-treated bednet effects on mosquitoes of the Anopheles gambiae complex in The Gambia. Medical and Veterinary Entomology, 5, 477-483.\n- Lindsay, S.W., Alonso, P.L., Armstrong Schellenberg, J.R.M., Hemingway, J., Adiahmah, J.H., Shenton, F.C., Jawara, M. & Greenwood, B.M. (1993) A malaria control trial using insecticidetreated bed nets and targeted chemoprophylaxis in a rural area of The Gambia, West Africa. 7. Impact of permethrin-impregnated bed nets on malaria vectors. Transactions of Royal Society of Tropical Medicine and Hygiene. 87, 45-52.\n- Lines, J.D., Myamba, J. & Curtis, C.F. (1987) Experimental hut trials of permethrin-impregnated mosquito nets and curtains against malaria vectors in Tanzania. Medical and Veterinary Entomology,  $1.37 - 51.$\n- Lines, J.D., Curtis, C.F., Wilkes, T.J. & Njunwa, K.J. (1991) Monitoring human-biting mosquitoes (Diptera: Culicidae) in Tanzania with lighttraps hung beside mosquito nets. Bulletin of Entomological Research, 81, 77-84.\n- Majori, G., Sabatinelli, G. & Coluzzi, M. (1987) Efficacy of permethrinimpregnated curtains for malaria vector control. Medical and Veterinary Entomology, 1, 185-192.\n- Magesa, S.M., Wilkies, T.J., Mnzava, A.E.P., Njunwa, K.J., Myamba, J., Kivuyo, M.D.P., Hill, N., Lines, J.D. & Curtis, C.F. (1991) Trial of pyrethroid impregnated bednets in an area of Tanzania holoendemic for malaria. Part 2. Effects on the malaria vector population. Acta Tropica, 49, 97-108.\n- Mbogo, C.N., Glass, G.E., Forster, D., Kabiru, E.W., Githure, J.I., Ouma, J.H. & Beier. J.C. (1993a) Evaluation of light traps for sampling anopheline mosquitoes in Kilifi, Kenya. Journal of the American Mosquito Control Association, 9, 141-144.\n- Mbogo, C.N.M., Snow, R.W., Kabiru, E.W., Ouma, J.H., Githure, J.I., Marsh, K. & Beier, J.C. (1993b) Low-level Plasmodium falciparum\n\ntransmission and the incidence of severe malaria infections on the Kenyan coast. American Journal of Tropical Medicine and Hygiene, 49.245-253.\n\n- Mbogo, C.N.M., Snow, R.W., Khamala, C.P.M., Kabiru, E.W., Ouma, J.H., Githure, J.I., Marsh, K. & Beier, J.C. (1995) Relationship between Plasmodium falciparum transmission by vector populations and the incidence of severe disease at nine sites on the Kenyan coast. American Journal of Tropical Medicine and Hygiene, 52, 201-206.\n- Miller, J.E., Lindsay, S.W. & Armstrong, J.R.M. (1991) Experimental hut trials of bednets impregnated with synthetic pyrethroid and organophosphate insecticides for mosquito control in The Gambia. Medical and Veterinary Entomology, 5, 465-476.\n- Mosha, F.W. & Petrarca V. (1983) Ecological studies of Anopheles gambiae complex species on the Kenyan Coast. Transactions of the Roval Society of Tropical Medicine and Hygiene, 77, 344-345.\n- Nevill, C.G., Some, E.S., Mungala, V.O., New, L., Marsh, K., Lengeler, C. & Snow, R.W. (1996) Insecticide-treated bednets reduce mortality and severe morbidity from malaria among children on the Kenyan coast. Tropical Medicine and International Health, 1, 139-146.\n- Paskewitz, S.M. & Collins, F.H. (1990) Use of the polymerase chain reaction to identify mosquito species of the Anopheles gambiae complex. Medical and Veterinary Entomology, 4, 367-373.\n- Petrarca, V., Beier, J.C., Onyango, F., Koros, J., Asiago, C., Koech, D.K. & Roberts, C.R. (1991) Species composition of the An.gambiae complex (Diptera: Culicidae) at two sites in Western Kenya. Journal of Medical Entomology, 28, 307-313.\n- Port, G.R. & Boreham, P.F.L. (1982) The effect of bed nets on feeding by Anopheles gambiae Giles (Diptera: Culicidae). Bulletin of Entomological Research, 72, 483-488.\n- Robert, V. & Carnevale, P. (1991) Influence of deltamethrin treatment of bed nets on malaria transmission in the Kou valley, Burkina Faso. Bulletin of the World Health Organization, 69, 735-740.\n- Snow, R.W., Juwara, M. & Curtis, C.F. (1987) Observations on Anopheles gambiae Giles s.l. during a trial of permethrin treated bed nets in The Gambia. Bulletin of Entomological Research, 77, 279-286.\n- Snow, R.W., Lindsay, S.W., Hayes, R.J. & Greenwood, B.M. (1988) Permethrin-treated bednets (mosquito nets) prevent malaria in Gambian children. Transactions of the Royal Society of Tropical Medicine and Hygiene, 82, 838-842.\n- Snow, R.W., Peshu, N., Forster, D., Mwenesi, H.M. & Marsh, K. (1992) The role of shops in the prevention of malaria on the coast of Kenya. Transactions of the Royal Society of Tropical Medicine and Hygiene, 86, 237-239.\n- Snow, R.W., Armstrong, J.R.M., Forster, D., Winstanley, P.A., Mwangi, I., Waruiru, C., Warn, P., Newbold, C. & Marsh, K. (1993) Periodicity and time space clustering of severe childhood malaria on the Kenyan coast. Transactions of the Royal Society of Tropical Medicine and Hygiene, 87, 386-390.\n- Snow, R.W., Mung'ala, V.O., Forster, D. & Marsh, K. (1994) The role of the district hospital in child survival at the Kenyan Coast. African Journal of Health Sciences, 1, 71-75.\n- Thomson, M.C., Adiamah, J.H., Connor, S.J., Jawara, M., Bennett, S., D'Allessandro, U., Quinones, M., Langerock, P. & Greenwood, B.M. (1995) Entomological evaluation of the Gambia National Impregnated bednet Programme. Annals of Tropical Medicine and Parasitology,  $89, 229 - 241.$\n- Vulule, J.M., Beach, R.F., Atieli, F.K., Mount, D.L., Roberts, J.M. & Mwangi, R.W. (1996) Long-term use of permethrin-impregnated nets does not increase Anopheles gambiae permethrin tolerance. Medical and Veterinary Entomology, 10, 71-79.\n- Wirtz, R.A., Zavala, F., Charoenvit, Y., Campbell, G.H., Burkot, T.R., Schnieder, I., Esser, K.M., Beaudoin, R.L. & Andre, R.G. (1987) Comparative testing of Plasmodium falciparum circumsporozoite antibody. Bulletin of the World Health Organization, 65, 39-45.\n- White, G.B. (1974) Anopheles gambiae complex and disease transmission\n\nin Africa. Transactions of the Royal Society of Tropical Medicine and Hygiene, 77, 344-345.\n\n- World Bank (1993) World Development Report: Investing in Health. Oxford University Press, New York.\n- W.H.O. (1975) Manual on Practical Entomology in Malaria, Part II. Methods and Techniques. World Health Organization, Geneva.\n- W.H.O. (1981) Instructions for determining the susceptibility or resistance of adult mosquitoes to organochloride, organophosphates and carbamate insecticides: diagnostic test. Unpublished document, VBC/\n\n81.806/WHO, World Health Organization, Geneva.\n\n- W.H.O. (1986) Expert Committee on Malaria, 18th Report. Technical Report Series, 737. World Health Organization, Geneva.\n- W.H.O. (1991) Guidelines for the development of protocols for studies to evaluate the impact of insecticide-treated bed-nets on mortality. WHO/TDR Unpublished document, World Health Organization, Geneva.\n\nAccepted 19 February 1996"}, "metadata": {}, "execution_count": 129}]}, {"cell_type": "code", "source": ["display(JSON(meta, expanded=False))"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 1000}, "id": "SxDnmXEcHw0k", "outputId": "0e8bbe19-4765-4b82-e06d-4e3849e85748"}, "id": "SxDnmXEcHw0k", "execution_count": null, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<IPython.core.display.JSON object>"], "application/json": {"table_of_contents": [{"title": "The impact of permethrin-impregnated bednets\non malaria vectors of the Kenyan coast", "heading_level": null, "page_id": 0, "polygon": [[47.2093023255814, 96.84135977337111], [452.841796875, 96.84135977337111], [452.841796875, 136.25244140625], [47.2093023255814, 136.25244140625]]}, {"title": "Introduction", "heading_level": null, "page_id": 0, "polygon": [[45.71059431524548, 569.0368271954675], [100.41343669250647, 569.0368271954675], [100.41343669250647, 578.78173828125], [45.71059431524548, 578.78173828125]]}, {"title": "Materials and Methods", "heading_level": null, "page_id": 1, "polygon": [[46.5, 232.5], [144.984375, 232.5], [144.984375, 242.279296875], [46.5, 242.279296875]]}, {"title": "Results", "heading_level": null, "page_id": 2, "polygon": [[49.542857142857144, 376.61873226111635], [82.974609375, 376.61873226111635], [82.974609375, 386.81982421875], [49.542857142857144, 386.81982421875]]}, {"title": "Discussion", "heading_level": null, "page_id": 5, "polygon": [[299.25, 543.75], [349.3125, 543.75], [349.3125, 553.0078125], [299.25, 553.0078125]]}, {"title": "Acknowledgments", "heading_level": null, "page_id": 6, "polygon": [[298.5, 443.56640625], [378.75, 443.56640625], [378.75, 452.84765625], [298.5, 452.84765625]]}, {"title": "References", "heading_level": null, "page_id": 6, "polygon": [[297, 676.5], [347.625, 676.5], [347.625, 686.8125], [297, 686.8125]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "surya", "block_counts": [["Span", 64], ["Line", 60], ["Text", 10], ["SectionHeader", 2], ["<PERSON>Footer", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "surya", "block_counts": [["Span", 124], ["Line", 118], ["Text", 10], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "surya", "block_counts": [["Line", 90], ["Span", 75], ["Text", 7], ["SectionHeader", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "surya", "block_counts": [["TableCell", 304], ["Line", 19], ["Span", 2], ["Table", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "surya", "block_counts": [["TableCell", 166], ["Span", 40], ["Line", 34], ["Text", 8], ["Table", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "surya", "block_counts": [["Line", 68], ["Span", 68], ["TableCell", 10], ["Text", 6], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Figure", 1], ["Caption", 1], ["Table", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "surya", "block_counts": [["Span", 120], ["Line", 115], ["Text", 7], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "surya", "block_counts": [["Line", 133], ["Span", 133], ["ListItem", 33], ["Text", 2], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "surya", "block_counts": [["Line", 20], ["Span", 20], ["ListItem", 5], ["Text", 3], ["ListGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Mbogo_et_al_1996_Med_Vet_Ento"}}, "metadata": {"application/json": {"expanded": false, "root": "root"}}}]}, {"cell_type": "markdown", "source": ["### Marker Blocks Filtering and Parsing"], "metadata": {"id": "gDquzwE69zqu"}, "id": "gDquzwE69zqu"}, {"cell_type": "code", "execution_count": null, "id": "a5018f48", "metadata": {"id": "a5018f48"}, "outputs": [], "source": ["# 3. <PERSON> to extract JSON structure\n", "import os\n", "output_dir = 'marker_output'\n", "os.makedirs(output_dir, exist_ok=True)\n", "json_out = os.path.join(output_dir, os.path.splitext(os.path.basename(pdf_path))[0] + '_structure.json')\n", "\n", "!marker_single \"{pdf_path}\" --output_format json --output_dir \"{output_dir}\""]}, {"cell_type": "code", "execution_count": null, "id": "df4ea443", "metadata": {"id": "df4ea443"}, "outputs": [], "source": ["# 4. <PERSON><PERSON> the Marker JSON\n", "import json\n", "with open(json_out, 'r') as f:\n", "    marker_json = json.load(f)"]}, {"cell_type": "code", "execution_count": null, "id": "456e8cf8", "metadata": {"id": "456e8cf8"}, "outputs": [], "source": ["# 5. Data models and flattening utilities (Python version of your TypeScript)\n", "from typing import List, Dict, Any\n", "\n", "class SimplifiedBlock:\n", "    def __init__(self, type: str, content: str, page: int, bbox: list):\n", "        self.type = type\n", "        self.content = content\n", "        self.page = page\n", "        self.bbox = bbox\n", "\n", "    def as_dict(self):\n", "        return {\n", "            'type': self.type,\n", "            'content': self.content,\n", "            'page': self.page,\n", "            'bbox': self.bbox,\n", "        }\n", "\n", "import html\n", "\n", "def decode_html_entities(text: str) -> str:\n", "    return html.unescape(text)\n", "\n", "def flatten_marker_json(blocks: List[Dict[str, Any]], page_number: int = 0) -> List[SimplifiedBlock]:\n", "    flat_blocks = []\n", "    for block in blocks:\n", "        # Skip Page blocks but process their children\n", "        if block.get('block_type') == 'Page':\n", "            child_page = int(block.get('id', '0/0/0').split('/')[2]) if 'id' in block else 0\n", "            flat_blocks.extend(flatten_marker_json(block.get('children', []), child_page))\n", "            continue\n", "\n", "        # Process current block\n", "        content = ''\n", "        if block.get('images') and isinstance(block['images'], dict) and block['images']:\n", "            content = next(iter(block['images'].values()))\n", "        elif block.get('block_type') == 'Table':\n", "            content = block.get('html', '').strip()\n", "        elif block.get('html'):\n", "            import re\n", "            content = re.sub(r'<[^>]*>', ' ', block['html']).strip()\n", "        content = decode_html_entities(content)\n", "\n", "        page = (int(block.get('id', '0/0/0').split('/')[2]) if 'id' in block else page_number) + 1\n", "        bbox = block.get('bbox', [0,0,0,0])\n", "\n", "        flat_blocks.append(SimplifiedBlock(\n", "            type=block.get('block_type', ''),\n", "            content=content,\n", "            page=page,\n", "            bbox=bbox\n", "        ))\n", "\n", "        # Recursively process children (except for Page blocks)\n", "        if block.get('children'):\n", "            flat_blocks.extend(flatten_marker_json(block['children'], page))\n", "    return flat_blocks\n", "\n", "def filter_and_flatten_marker_json(blocks: List[Dict[str, Any]], page_number: int = 0) -> List[SimplifiedBlock]:\n", "    unfiltered = flatten_marker_json(blocks, page_number)\n", "    remove_types = {\n", "        'TableCell', 'TableGroup', 'FigureGroup', 'ListGroup', 'Reference',\n", "        '<PERSON><PERSON><PERSON>er', '<PERSON><PERSON><PERSON><PERSON>', 'Footnote'\n", "    }\n", "    return [b for b in unfiltered if b.type not in remove_types and b.content]"]}, {"cell_type": "code", "execution_count": null, "id": "8788d923", "metadata": {"id": "8788d923"}, "outputs": [], "source": ["# 6. Flatten and filter the Marker output\n", "flat_blocks = filter_and_flatten_marker_json(marker_json.get('children', []))"]}, {"cell_type": "code", "execution_count": null, "id": "8c7a9664", "metadata": {"id": "8c7a9664"}, "outputs": [], "source": ["# 7. Explore block types and content\n", "import pandas as pd\n", "\n", "df = pd.DataFrame([b.as_dict() for b in flat_blocks])\n", "print('Block types found:', df['type'].unique())\n", "df.head(20)  # Show first 20 blocks"]}, {"cell_type": "code", "execution_count": null, "id": "5a0e173d", "metadata": {"id": "5a0e173d"}, "outputs": [], "source": ["# 8. Simple metadata extraction (title, authors, abstract)\n", "def extract_metadata(blocks: List[SimplifiedBlock]):\n", "    title = next((b.content for b in blocks if b.type.lower() in {'title', 'main_title'}), '')\n", "    authors = next((b.content for b in blocks if 'author' in b.type.lower()), '')\n", "    abstract = next((b.content for b in blocks if 'abstract' in b.type.lower()), '')\n", "    return {'title': title, 'authors': authors, 'abstract': abstract}\n", "\n", "metadata = extract_metadata(flat_blocks)\n", "print('Extracted Metadata:', metadata)"]}, {"cell_type": "code", "execution_count": null, "id": "7b8b19d2", "metadata": {"id": "7b8b19d2"}, "outputs": [], "source": ["# 9. Find and display all tables and figures (with extensibility for custom processing)\n", "tables = [b for b in flat_blocks if b.type == 'Table']\n", "figures = [b for b in flat_blocks if b.type == 'Figure' or b.type == 'Picture']\n", "\n", "print(f'Found {len(tables)} tables and {len(figures)} figures.')\n", "\n", "# Example: Show first table's HTML (for further processing)\n", "if tables:\n", "    from IPython.display import display, HTML\n", "    print('First table HTML:')\n", "    display(HTML(tables[0].content))\n", "\n", "# Example: Show first figure as image (if base64-encoded)\n", "import base64\n", "from IPython.display import Image\n", "\n", "def show_base64_image(b64str):\n", "    try:\n", "        display(Image(data=base64.b64decode(b64str)))\n", "    except Exception as e:\n", "        print('Could not display image:', e)\n", "\n", "if figures:\n", "    print('First figure (if image):')\n", "    show_base64_image(figures[0].content)"]}, {"cell_type": "code", "execution_count": null, "id": "67298ff1", "metadata": {"id": "67298ff1"}, "outputs": [], "source": ["# 10. (Optional) Extensible: Add your own logic to process tables/figures, e.g., send table HTML to a model, extract captions, etc.\n", "# (No LLM-based summarization or captioning included)"]}, {"cell_type": "code", "execution_count": null, "id": "3b0329a2", "metadata": {"id": "3b0329a2"}, "outputs": [], "source": ["# 11. Save flattened blocks for further analysis\n", "df.to_json('flattened_blocks.json', orient='records', indent=2)\n", "from google.colab import files\n", "files.download('flattened_blocks.json')"]}, {"cell_type": "markdown", "source": ["# Eval OCR accuracy"], "metadata": {"id": "B8MmmRYnxAFP"}, "id": "B8MmmRYnxAFP"}, {"cell_type": "code", "source": [], "metadata": {"id": "tMNmskQGxB4b"}, "id": "tMNmskQGxB4b", "execution_count": null, "outputs": []}, {"cell_type": "code", "source": [], "metadata": {"id": "5CSv39luITlK"}, "id": "5CSv39luITlK", "execution_count": null, "outputs": []}, {"cell_type": "code", "source": [], "metadata": {"id": "05_k8GqNIbyu"}, "id": "05_k8GqNIbyu", "execution_count": null, "outputs": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python", "version": "3.10.17"}, "colab": {"provenance": [], "gpuType": "T4", "include_colab_link": true}, "accelerator": "GPU"}, "nbformat": 4, "nbformat_minor": 5}