{"cells": [{"cell_type": "code", "execution_count": 2, "id": "af07b18a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "🔧 Testing body_limit=9, max_levels=2\n", "📊 Header Levels: {'hdr_level_1': 1, 'hdr_level_2': 1}\n", "🧠 Assessment: Header hierarchy appears reasonable.\n", "\n", "🔧 Testing body_limit=9, max_levels=3\n", "📊 Header Levels: {'hdr_level_1': 1, 'hdr_level_2': 1, 'hdr_level_3': 2}\n", "🧠 Assessment: Header hierarchy appears reasonable.\n", "\n", "🔧 Testing body_limit=9, max_levels=4\n", "📊 Header Levels: {'hdr_level_1': 1, 'hdr_level_2': 1, 'hdr_level_3': 2, 'hdr_level_4': 1}\n", "🧠 Assessment: Header levels are too deeply nested.\n", "\n", "🔧 Testing body_limit=9, max_levels=5\n", "📊 Header Levels: {'hdr_level_1': 1, 'hdr_level_2': 1, 'hdr_level_3': 2, 'hdr_level_4': 1}\n", "🧠 Assessment: Header levels are too deeply nested.\n", "\n", "🔧 Testing body_limit=10, max_levels=2\n", "📊 Header Levels: {'hdr_level_1': 1, 'hdr_level_2': 1}\n", "🧠 Assessment: Header hierarchy appears reasonable.\n", "\n", "🔧 Testing body_limit=10, max_levels=3\n", "📊 Header Levels: {'hdr_level_1': 1, 'hdr_level_2': 1, 'hdr_level_3': 2}\n", "🧠 Assessment: Header hierarchy appears reasonable.\n", "\n", "🔧 Testing body_limit=10, max_levels=4\n", "📊 Header Levels: {'hdr_level_1': 1, 'hdr_level_2': 1, 'hdr_level_3': 2, 'hdr_level_4': 1}\n", "🧠 Assessment: Header levels are too deeply nested.\n", "\n", "🔧 Testing body_limit=10, max_levels=5\n", "📊 Header Levels: {'hdr_level_1': 1, 'hdr_level_2': 1, 'hdr_level_3': 2, 'hdr_level_4': 1}\n", "🧠 Assessment: Header levels are too deeply nested.\n", "\n", "🔧 Testing body_limit=11, max_levels=2\n", "📊 Header Levels: {'hdr_level_1': 1, 'hdr_level_2': 1}\n", "🧠 Assessment: Header hierarchy appears reasonable.\n", "\n", "🔧 Testing body_limit=11, max_levels=3\n", "📊 Header Levels: {'hdr_level_1': 1, 'hdr_level_2': 1, 'hdr_level_3': 2}\n", "🧠 Assessment: Header hierarchy appears reasonable.\n", "\n", "🔧 Testing body_limit=11, max_levels=4\n", "📊 Header Levels: {'hdr_level_1': 1, 'hdr_level_2': 1, 'hdr_level_3': 2}\n", "🧠 Assessment: Header hierarchy appears reasonable.\n", "\n", "🔧 Testing body_limit=11, max_levels=5\n", "📊 Header Levels: {'hdr_level_1': 1, 'hdr_level_2': 1, 'hdr_level_3': 2}\n", "🧠 Assessment: Header hierarchy appears reasonable.\n"]}], "source": ["from collections import Counter\n", "import re\n", "import fitz\n", "import pymupdf4llm\n", "from pathlib import Path\n", "from scripts.ocr_text import analyze_markdown_header_hierarchy\n", "\n", "def test_header_tuning(pdf_path):\n", "    doc = fitz.open(pdf_path)\n", "    margins = (0, 50, 0, 30)\n", "\n", "    for body_limit in [9, 10, 11]:\n", "        for max_levels in [2, 3, 4, 5]:\n", "            print(f\"\\n🔧 Testing body_limit={body_limit}, max_levels={max_levels}\")\n", "\n", "            headers = pymupdf4llm.IdentifyHeaders(\n", "                doc,\n", "                max_levels=max_levels,\n", "                body_limit=body_limit\n", "            )\n", "\n", "            md = pymupdf4llm.to_markdown(doc, hdr_info=headers, margins=margins)\n", "            result = analyze_markdown_header_hierarchy(md)\n", "\n", "            print(\"📊 Header Levels:\", {k: v for k, v in result.items() if k.startswith(\"hdr_level\")})\n", "            print(\"🧠 Assessment:\", result[\"assessment\"])\n", "\n", "test_pdf = \"pdfs/Allossogbe_et_al_2017_Mal_J.pdf\"\n", "test_header_tuning(test_pdf)\n"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 5}