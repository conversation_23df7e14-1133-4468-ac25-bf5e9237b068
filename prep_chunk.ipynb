{"cells": [{"cell_type": "code", "execution_count": 69, "id": "3af44e08", "metadata": {}, "outputs": [], "source": ["import os\n", "import fitz  # PyMuPDF\n", "import ocrmypdf\n", "import re\n", "from pathlib import Path\n", "import pymupdf4llm\n", "from llama_index.core.schema import Document\n", "import logging\n", "import weaviate"]}, {"cell_type": "code", "execution_count": 70, "id": "d3e89dc1", "metadata": {}, "outputs": [], "source": ["TEMP_DIR = Path(\"temp_ocr\")\n", "MARKDOWN_DIR = Path(\"output_chunk\")\n", "TEMP_DIR.mkdir(exist_ok=True)\n", "MARKDOWN_DIR.mkdir(exist_ok=True)\n", "\n", "# Set up logging\n", "logging.basicConfig(filename='pipeline.log', level=logging.INFO, \n", "                    format='%(asctime)s:%(levelname)s:%(message)s')\n"]}, {"cell_type": "code", "execution_count": 71, "id": "aed26ff1", "metadata": {}, "outputs": [], "source": ["def is_scanned_pdf(pdf_path):\n", "    try:\n", "        doc = fitz.open(pdf_path)\n", "        return all(not page.get_text().strip() for page in doc)\n", "    except Exception as e:\n", "        logging.error(f\"Error checking if PDF is scanned: {e}\")\n", "        return False"]}, {"cell_type": "code", "execution_count": 72, "id": "e8533441", "metadata": {}, "outputs": [], "source": ["def run_ocr(input_path, output_path):\n", "    try:\n", "        logging.info(\"Running OCR on scanned PDF\")\n", "        ocrmypdf.ocr(\n", "            input_file=input_path,\n", "            output_file=output_path,\n", "            rotate_pages=True,\n", "            deskew=True,\n", "            force_ocr=True,\n", "            skip_text=True\n", "        )\n", "        logging.info(f\"OCR complete: {output_path}\")\n", "    except Exception as e:\n", "        logging.error(f\"OCR failed: {e}\")\n", "        raise"]}, {"cell_type": "code", "execution_count": 73, "id": "0aeb852e", "metadata": {}, "outputs": [], "source": ["def remove_tables_from_markdown(md_text):\n", "    try:\n", "        # Remove sections that start with 'tables:' and continue until a double newline\n", "        cleaned = re.sub(r'(?s)^tables:.*?\\n\\n', '', md_text)\n", "        return cleaned\n", "    except Exception as e:\n", "        logging.error(f\"Failed to clean tables from markdown: {e}\")\n", "        return md_text"]}, {"cell_type": "code", "execution_count": 74, "id": "be7844b8", "metadata": {}, "outputs": [], "source": ["def extract_chunks_with_llamareader(pdf_path, chunk_size=1024, chunk_overlap=128):\n", "    try:\n", "        reader = pymupdf4llm.LlamaMarkdownReader(\n", "            margins=(0, 30, 0, 20),\n", "            max_levels=3,\n", "            body_limit=11,\n", "            chunk_size=chunk_size,\n", "            chunk_overlap=chunk_overlap\n", "        )\n", "        docs = reader.load_data(pdf_path)\n", "        llama_docs = [Document(text=remove_tables_from_markdown(doc.text), metadata=doc.extra_info) for doc in docs]\n", "\n", "        for i, doc in enumerate(llama_docs):\n", "            out_path = MARKDOWN_DIR / f\"{Path(pdf_path).stem}_chunk{i}.md\"\n", "            out_path.write_text(doc.text, encoding=\"utf-8\")\n", "            logging.info(f\"Chunk {i} saved to {out_path}, headers={doc.metadata.get('header_path')}\")\n", "\n", "        return llama_docs\n", "    except Exception as e:\n", "        logging.error(f\"Failed to extract chunks with LlamaMarkdownReader: {e}\")\n", "        return []"]}, {"cell_type": "code", "execution_count": 75, "id": "72149dbc", "metadata": {}, "outputs": [], "source": ["def process_pdf_pipeline(pdf_path):\n", "    filename = Path(pdf_path).stem\n", "    ocr_output = TEMP_DIR / f\"{filename}_ocr.pdf\"\n", "\n", "    try:\n", "        if is_scanned_pdf(pdf_path):\n", "            logging.info(\"Scanned PDF detected\")\n", "            run_ocr(pdf_path, ocr_output)\n", "            used_pdf = ocr_output\n", "        else:\n", "            logging.info(\"Born-digital PDF detected\")\n", "            used_pdf = Path(pdf_path)\n", "\n", "        chunks = extract_chunks_with_llamareader(used_pdf)\n", "        return chunks\n", "    except Exception as e:\n", "        logging.error(f\"Failed to process PDF pipeline: {e}\")\n", "        return []\n"]}, {"cell_type": "code", "execution_count": 76, "id": "18ea9c2b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Processed 0 chunks from: pdfs/Mbogo_et_al_1996_Med_Vet_Ento.pdf\n"]}], "source": ["test_pdf = \"pdfs/Mbogo_et_al_1996_Med_Vet_Ento.pdf\"\n", "results = process_pdf_pipeline(test_pdf)\n", "print(f\"✅ Processed {len(results)} chunks from: {test_pdf}\")\n"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 5}